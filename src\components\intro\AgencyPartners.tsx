import { SectionInner } from '@components/intro'
import InfiniteTicker from '@components/intro/InfiniteTicker'
import { useI18n } from '@hooks/common'
import { TransText } from '@components/common'
import { Link } from 'react-router-dom'
import { ReactComponent as ArrowRightIcon } from '@assets/icon/arrow-right.svg'
import tw from 'twin.macro'

const getLogoUrls = (line: number, length: number, format: string = 'svg') => {
  return Array(length)
    .fill(0)
    .reduce((acc, _, index) => {
      acc.push(`line-${line}-${index + 1}.${format}`)
      return acc
    }, [])
}

const PremiumPartners = [
  { link: 'https://echomarketing.co.kr/', img: 'main-1.svg' },
  { link: 'https://www.playd.com/', img: 'main-2.svg' },
  { link: 'https://www.linkprice.com/', img: 'main-3.svg' },
  { link: 'https://my-progress.co.kr/', img: 'main-4.svg' }
]

const AgencyPartners = () => {
  const { isKO, isEN } = useI18n()
  return (
    <>
      <SectionInner customStyle={tw`flex flex-col items-center`}>
        {/* {isKO ? <span className="text-base md:text-2xl font-medium">에이전시 파트너스</span> : <></>} */}
        <h1 className="font-poppins text-4xl md:text-6xl font-bold">Agency Partners</h1>
        <TransText
          as="p"
          className="text-[#555555] text-center text-base md:text-xl my-6 md:my-16"
          i18nKey="landing.agencyPartners.desc"
        />
        <div className="flex flex-col gap-12 items-center justify-center bg-[url('assets/images/intro/bg-partners-mo.svg')] rounded-2xl md:bg-[url('assets/images/intro/bg-partners-pc.svg')] bg-center bg-no-repeat w-[100%] md:w-[90%] h-[630px] md:h-[430px]">
          <TransText
            as="h2"
            className="text-[#fff] text-center text-4xl font-semibold"
            i18nKey="landing.agencyPartners.premiumPartner.title"
          />
          <TransText
            as="p"
            className="text-[#fff] text-center text-base md:text-xl "
            i18nKey="landing.agencyPartners.premiumPartner.desc"
          />
          <div className="flex items-center flex-col gap-3 md:grid md:grid-cols-2 md:gap-2 md:justify-items-center xl:flex xl:flex-row">
            {PremiumPartners.map((item, index) => (
              <a key={index} href={item.link} target="_blank" className="flex block w-[95%] md:w-[265px] h-[62px] md:h-[80px] xl:h-[105px] items-center justify-center rounded bg-[rgba(255,255,255,0.85)]">
                <img
                  src={require(`@/assets/icon/logos/${item.img}`)}
                  alt={`main-logo-${index}`}
                  className="inline-block h-[62px] md:h-[80px] xl:h-[105px]"
                />
              </a>
            ))}
          </div>
        </div>
      </SectionInner>
      <div className="mt-5 md:mt-16 opacity-70 flex flex-col gap-6">
        <InfiniteTicker items={getLogoUrls(1, 8)} />
        <InfiniteTicker items={getLogoUrls(2, 7)} reverse={true} />
        <InfiniteTicker items={getLogoUrls(3, 8)} />
        <InfiniteTicker items={getLogoUrls(4, 10, 'png')} reverse={true} opacity="opacity-50" />
      </div>
      <div className="mx-auto mt-10 md:mt-20">
        <a id="nav-partners" href="https://contents.mop.co.kr/partner" className="w-fit py-2 px-5 text-base rounded-full text-black border-black border">
          <TransText as="span" i18nKey="landing.common.more" className="pr-2" />
          <ArrowRightIcon />
        </a>
      </div>
    </>
  )
}

export default AgencyPartners
