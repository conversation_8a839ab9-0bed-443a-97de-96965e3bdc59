import React, { ReactElement } from 'react';
import './SearchOptimizationList.scss';
import { pageSizeOptions } from '@models/common/CommonConstants';
import {
  ContextMenuFunctions,
  SearchOptimizationInfo,
} from '@models/optimization/SearchOptimization'
import { useTranslation } from 'react-i18next';
import { FixedLayoutTable, TablePagination, FixedLayoutColumn } from '@components/common/table'
import SearchOptimizationListFormatter from '@components/optimization/SearchOptimizationListFormatter';
import { useRecoilState, useRecoilValue } from 'recoil';
import { soListActiveFilterState, soListState } from '@store/SearchOptimization'
interface Props {
  contextMenuFunctions?: ContextMenuFunctions;
}

const searchOptimizationListFormatter = new SearchOptimizationListFormatter();

const SearchOptimizationList: React.FC<Props> = ({ contextMenuFunctions }: Props): ReactElement => {
  const { t } = useTranslation();

  const soList = useRecoilValue(soListState);
  const [soListActiveFilter, setSoListActiveFilter] = useRecoilState(soListActiveFilterState);
  const allColumns: Array<FixedLayoutColumn<SearchOptimizationInfo>> = searchOptimizationListFormatter.getColumnFormat(
    contextMenuFunctions,
    soListActiveFilter.orderBy,
    soListActiveFilter.sorting,
    soList,
  );

  const handleChangePage = (newPage: number) => {
    if (newPage !== soListActiveFilter.pageIndex) {
      setSoListActiveFilter({ ...soListActiveFilter, pageIndex: newPage });
    }
  };

  const handleChangeRowsPerPage = (newRowsPerPage: number) => {
    if (newRowsPerPage !== soListActiveFilter.pageSize) {
      setSoListActiveFilter({ ...soListActiveFilter, pageSize: newRowsPerPage, pageIndex: 1 });
    }
  };

  const handleOrderChange = (orderBy: number, orderDirection: 'asc' | 'desc') => {
    setSoListActiveFilter({
      ...soListActiveFilter,
      orderBy: allColumns[orderBy]?.field,
      sorting: orderDirection.toUpperCase(),
      pageIndex: 1,
    });
  };

  return (
    <div id="searchOptimizationList">
      {soList.optimizations && allColumns && (
        <>
          <FixedLayoutTable
            data-testid="searchOptimizationTable"
            onOrderChange={handleOrderChange}
            columns={allColumns}
            data={soList.optimizations.map((obj) => Object.create(obj)) || []}
            localization={
              soList.optimizations.length === 0
                ? { body: { emptyDataSourceMessage: t('common.message.list.noData') } }
                : { body: { emptyDataSourceMessage: '' } }
            }
          />
          <TablePagination
            id="search-optimization-list-pagination"
            totalCount={soList.totalCount || 0}
            page={soListActiveFilter.pageIndex || 1}
            rowsPerPage={soListActiveFilter.pageSize || pageSizeOptions[0]}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        </>
      )}
    </div>
  );
};

export default SearchOptimizationList;
