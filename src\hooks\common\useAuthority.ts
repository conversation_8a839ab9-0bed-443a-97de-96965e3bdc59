import { useMemo } from 'react'
import { useRecoilState } from 'recoil'
import { checkAuthority, checkSystemViewer } from '@utils/AuthorityUtil'
import { advertiserState, advertiserListState, advertiserLoadedState, advertiserStateById } from '@store/Advertiser'
import { AuthorityType, AdvertiserCurrencyCode, SubscriptionProductType } from '@models/common/Advertiser'
import { numberWithCommas } from '@utils/FormatUtil'
import { getAdvertisers } from '@api/common/Advertiser'
import { MenuType } from '@models/common/Menu'

// max CPC 를 사용하는 광고주 ID 리스트
const useMaxCPCAdvertisers = [249, 250, 478]

const OPERATE_MENUS: MenuType[] = [
  // MenuType.CREATE_CAMPAIGN
]

const ADMIN_MENUS: MenuType[] = [
]

const useAuthority = () => {
  const [advertiser, setAdvertiser] = useRecoilState(advertiserState)
  const [advertiserList, setAdvertiserList] = useRecoilState(advertiserListState)
  const [isAdvertiserLoaded, setAdvertiserLoaded] = useRecoilState(advertiserLoadedState)
  const hasAdvertisers = advertiser.advertiserId && advertiserList.length
  const hasAuthority = useMemo(
    () => checkAuthority(AuthorityType.OPERATE, advertiser.authorityType),
    [advertiser.authorityType]
  )
  const hasSystemViewerAuthority = useMemo(() => checkSystemViewer(), [advertiser.authorityType])
  const useMaxCPC = useMemo(() => useMaxCPCAdvertisers.includes(advertiser.advertiserId), [advertiser.advertiserId])
  const useKRW = useMemo(
    () => advertiser.advertiserCurrencyCode === AdvertiserCurrencyCode.KRW,
    [advertiser.advertiserCurrencyCode]
  )
  const useUSD = useMemo(
    () => advertiser.advertiserCurrencyCode === AdvertiserCurrencyCode.USD,
    [advertiser.advertiserCurrencyCode]
  )
  
  const getCurrencyValue = (value: number) => {
    return numberWithCommas(useKRW ? value.toFixed(0) : value.toFixed(2))
  }

  const getFunctionValue = (fnId: string) => {
    const targetFn = advertiser.subscriptionFunctions.find((fn) => fn.functionId === fnId)
    return targetFn?.functionValue ?? ''
  }

  const canAccessMenu = (menuType: MenuType): boolean => {
    if (OPERATE_MENUS.includes(menuType)) {
      return hasAuthority
    }
    if (ADMIN_MENUS.includes(menuType)) {
      return advertiser.authorityType === AuthorityType.ADMINISTRATE
    }
    return true 
  }

  const isProAdvertiser = advertiser.subscriptionProductType === SubscriptionProductType.PRO
  const isLiteAdvertiser = advertiser.subscriptionProductType === SubscriptionProductType.LITE
  const isBasicAdvertiser = advertiser.subscriptionProductType === SubscriptionProductType.BASIC

  return {
    advertiser,
    advertiserList,
    getAdvertisers,
    getAdvertiserById: advertiserStateById,
    getCurrencyValue,
    getFunctionValue,
    hasAdvertisers,
    hasAuthority,
    isAdvertiserLoaded,
    isProAdvertiser,
    isLiteAdvertiser,
    isBasicAdvertiser,
    setAdvertiser,
    setAdvertiserList,
    setAdvertiserLoaded,
    useKRW,
    useUSD,
    useMaxCPC,
    hasSystemViewerAuthority,
    canAccessMenu 
  }
}
export default useAuthority
