// src/components/common/mopUI/MopSelect.tsx
import React, { Fragment, useEffect, useState, Children, isValidElement, useMemo, useCallback } from 'react'
import { Listbox, Transition } from '@headlessui/react'
import { cn } from '@utils/index'
import { MOPIcon } from '@models/common'
import MopIcon from '../icon/MopIcon'

interface SelectOption {
  label: string
  value: string
  icon?: string
}

interface Props {
  id?: string
  value: string | string[]
  onChange: (value: string | string[]) => void
  options?: string[] | SelectOption[]  // 하위 호환성을 위해 optional로 변경
  placeholder?: string
  disabled?: boolean
  multiple?: boolean
  children?: React.ReactNode  // children 기반 사용을 위해 추가
  className?: string
  isLoading?: boolean
  renderSelected?: (args: { values: string | string[]; getLabel: (v: string) => string }) => React.ReactNode
}

const getSelectedLabel = (value: string, options?: string[] | SelectOption[], children?: React.ReactNode): string => {
  // options 배열이 있으면 기존 방식 사용 (하위 호환성)
  if (options && options.length > 0) {
    const option = [...options].find((option) => {
      return typeof option === 'object' ? option.value === value : option === value
    })
    if (typeof option === 'object') {
      return option.label
    }
    return option || value
  }

  // children에서 선택된 값에 해당하는 label을 찾기
  let foundLabel = value

  Children.forEach(children, (child) => {
    if (isValidElement(child)) {
      // MopSelectOptions 내부의 MopSelectOption들을 찾기
      if (child.props.children) {
        Children.forEach(child.props.children, (optionChild) => {
          if (isValidElement(optionChild) && (optionChild.props as any).value === value) {
            // children이 string이면 그대로 사용, 아니면 value 사용
            foundLabel = typeof (optionChild.props as any).children === 'string' 
              ? (optionChild.props as any).children 
              : value
          }
        })
      }
      // 직접 MopSelectOption인 경우
      else if ((child.props as any).value === value) {
        foundLabel = typeof (child.props as any).children === 'string' 
          ? (child.props as any).children 
          : value
      }
    }
  })

  return foundLabel
}

// placeholder 기본값 정의
const getPlaceholder = (isLoading?: boolean, placeholder?: string) => {
  if (isLoading) return '로딩 중...'
  return placeholder || '선택하세요'
}

const MopSelect = React.forwardRef<HTMLButtonElement, Props>(
  ({ 
    id, 
    value, 
    onChange, 
    options, 
    placeholder, 
    disabled = false, 
    multiple = false, 
    children, 
    className,
    isLoading = false,
    renderSelected
  }, ref) => {
    const [selected, setSelected] = useState<string | string[]>(
      multiple ? (Array.isArray(value) ? value : [value]) : value
    )

    const finalPlaceholder = getPlaceholder(isLoading, placeholder)

    // 표시 값(또는 커스텀 렌더링)을 준비 - 안정적인 참조를 위해 콜백 고정
    const getLabel = useCallback((v: string) => getSelectedLabel(v, options, children), [options, children])

    const displayContent: React.ReactNode = useMemo(() => {
      if (Array.isArray(selected)) {
        if (selected.length === 0) return finalPlaceholder
        return renderSelected
          ? renderSelected({ values: selected, getLabel })
          : selected.map((v) => getLabel(v)).join(', ')
      }
      if (!selected) return finalPlaceholder
      return renderSelected ? renderSelected({ values: selected, getLabel }) : getLabel(selected)
    }, [selected, renderSelected, getLabel, finalPlaceholder])

    const handleSelect = (option: string | string[]) => {
      if (Array.isArray(option)) {
        const filteredOptions = option.filter((item) => item !== '')
        onChange(filteredOptions)
      } else {
        onChange(option ?? '')
      }
    }

    useEffect(() => {
      setSelected(value)
    }, [value])

    return (
      <div className={cn("w-full", className)} data-testid={id}>
        <Listbox value={selected} onChange={handleSelect} multiple={multiple} disabled={disabled || isLoading}>
          {({ open }) => (
            <div className="relative">
              <Listbox.Button
                ref={ref}
                id={id}
                aria-busy={isLoading}
                className={cn(
                  'w-full h-[42px] flex items-center justify-between',
                  'border border-[#efefef] rounded-[4px] px-5 gap-2.5',
                  'bg-white text-left text-sm text-[#333]',
                  'focus:outline-none focus:border-[#17171780]',
                  'disabled:bg-[#f6f8f9] disabled:cursor-not-allowed',
                  'relative z-0',
                  {
                    'text-[#707070]': !selected || (Array.isArray(selected) && selected.length === 0),
                    'border-[#17171780]': open
                  }
                )}
              >
                <span className="truncate">{displayContent}</span>
                <MopIcon name={MOPIcon.ARROW_DOWN} size={16} />
              </Listbox.Button>

              {/* children이 있으면 children 사용, 없으면 기존 options 방식 사용 */}
              {children ? (
                children
              ) : (
                <Transition
                  as={Fragment}
                  leave="transition ease-in duration-100"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <Listbox.Options
                    className={cn(
                      'absolute top-full left-0 right-0 z-50 mt-0',
                      'bg-white border border-[#efefef] rounded-[4px]',
                      'shadow-lg max-h-60 overflow-auto',
                      'py-1'
                    )}
                    static={false}
                  >
                    {options?.map((option, index) => {
                      const optionValue = typeof option === 'object' ? option.value : option
                      const optionLabel = typeof option === 'object' ? option.label : option
                      const optionIcon = typeof option === 'object' ? option.icon : null

                      return (
                        <Listbox.Option key={`${optionValue}-${index}`} value={optionValue} as={Fragment}>
                          {({ active, selected }) => (
                            <li
                              className={cn(
                                'relative cursor-pointer select-none px-5 py-2',
                                'hover:bg-[#f9f9fb] hover:rounded transition-colors',
                                {
                                  'bg-[#f9f9fb] rounded': active,
                                  'bg-[#f9f9fb] rounded font-bold': selected
                                }
                              )}
                            >
                              <div className="flex items-center gap-2">
                                {optionIcon && <span className="w-4">{optionIcon}</span>}
                                <span className={cn('block truncate')}>
                                  {optionLabel}
                                </span>
                              </div>
                            </li>
                          )}
                        </Listbox.Option>
                      )
                    })}
                  </Listbox.Options>
                </Transition>
              )}
            </div>
          )}
        </Listbox>
      </div>
    )
  }
)

MopSelect.displayName = 'MopSelect'

export default MopSelect
