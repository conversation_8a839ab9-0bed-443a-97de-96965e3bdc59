import { MopSwitch } from '@components/common'
import { MopButton } from '@components/common/buttons'
import MopSelect from '@components/common/mopUI/MopSelect'
import RestoreKeywordModal from '@components/report/negativeKeyword/RestoreKeywordModal'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'

const NegativeKeywordHeader = () => {
  const [switchSelectedProducts, setSwitchSelectedProducts] = useState(false)
  const { t } = useTranslation()
  const [isRestoreKeywordModalOpen, setIsRestoreKeywordModalOpen] = useState(false)
  return (
    <div
      className="flex w-full bg-campaign-background px-5 py-3 gap-5 mb-6 rounded-[4px] border border-campaign-border-light border-solid border-1 font-pretendard"
      id="MediaAccountChannelSection"
    >
      <div className="flex w-1/2 gap-2 items-center">
        <div className="select-label">{t('createCampaign.createModal.mediaAccount')}</div>
        <MopSelect
          id="select-media"
          data-testid="mediaSelect"
          options={[]}
          value={'hee'}
          onChange={(value) => {
            console.log(value)
          }}
          placeholder={t('createCampaign.createModal.mediaAccount')}
        />
        <div className="flex items-center gap-3">
          <span className="view-selected-products whitespace-nowrap">자동</span>
          <MopSwitch
            checked={switchSelectedProducts}
            onChange={() => {
              setSwitchSelectedProducts((prev) => !prev)
            }}
          />
        </div>
      </div>
      <div className="flex w-1/2 gap-2 items-center justify-end">
        <MopButton 
          label="제외이력보기"  
          onClick={() => {
            // setIsRestoreKeywordModalOpen(true)
          }} 
          customStyle={{
            backgroundColor: "#ffffff", 
            borderColor: "#EFEFEF",
            color:"#333333"
          }} 
        />
      </div>

      <RestoreKeywordModal
        open={isRestoreKeywordModalOpen}
        onClose={() => setIsRestoreKeywordModalOpen(false)}
      />
    </div>
  )
}

export default NegativeKeywordHeader
