import React, { ReactElement, useEffect, useState, useC<PERSON>back, useMemo } from 'react'
import {
  Box,
  Button,
  Dialog,
  DialogContent,
  DialogTitle,
  FormLabel,
  Grid,
  Icon,
  IconButton,
  InputAdornment,
  List,
  ListItem,
  ListItemText,
  MenuItem,
  Paper,
  TextField,
  Slider,
  Popper,
  Table,
  TableBody,
  TableRow,
  TableCell
} from '@material-ui/core'
import { getSaMediaCodes, getOptimizationGoalCodes } from '@utils/CodeUtil'
import {
  SearchOptimizationSaveRequest,
  SearchOptimizationDetail,
  SearchOptimizationAdgroup,
  SearchOptimizationCostsRequest,
  SearchOptimizationCost,
  SearchOptimizationSaveRequestWithoutAdgroup
} from '@models/optimization/SearchOptimization'
import { DatePeriodPicker } from '@components/common/DatePeriodPicker'
import { ToggleButtonGroup, ToggleButton } from '@material-ui/lab'
import { MediaType } from '@models/common/Media'
import { OptimizationGoal, OptimizationGoalType } from '@models/optimization/OptimizationGoal'
import { Keyword, SearchKeywordRequest } from '@models/common/Keyword'
import './SearchOptimizationDetailModal.scss'
import { add, endOfMonth, format, max, parse } from 'date-fns'
import { t } from 'i18next'
import { DateFnsFormat, SearchingTextLimitLength } from '@models/common/CommonConstants'
import { convertDateDisplayFormat, convertFormatToDate } from '@utils/DateUtil'
import { getKeywords } from '@api/common/Keyword'
import { cloneDeep, find, debounce } from 'lodash'
import { array, number, object, string } from 'yup'
import SelectGroupComponent from '@components/optimization/SelectGroupComponent'
import {
  getSearchOptimizationDetail,
  getSearchOptimizationCosts,
  createSearchOptimization,
  updateSearchOptimization,
  updateSearchOptimizationWithoutAdgroup
} from '@api/optimization/SearchOptimization'
import { StatusCode } from '@models/common/CommonResponse'
import { OptimizationKpi, KpiType } from '@models/optimization/Kpi'
import IntegerNumberFormat from '@components/common/IntegerNumberFormat'
import { useRecoilValue } from 'recoil'
import { soListActiveFilterState, soListState } from '@store/SearchOptimization'
import LeftArrowIcon from '@components/assets/images/icon_arrow_left.svg'
import RightArrowIcon from '@components/assets/images/icon_arrow_right.svg'
import CloseIcon from '@components/assets/images/icon_close.png'
import { ReactComponent as AdviceMarkIcon } from '@components/assets/images/advicemark.svg'
import SelectBottom from '@components/common/SelectBottom'
import { ReactComponent as SearchIcon } from '@components/assets/images/icon_search.svg'
import { ReactComponent as CountCheckIcon } from '@components/assets/images/icon_count_check.svg'
import MultiKpiSettings from './MultiKpiSettings'
import useStatefulStateWithInitialData from '@components/common/hook/UseStatefulStateWithInitialData'
import WithLoader from '@components/common/WithLoader'
import AdviceTooltip from '@components/common/AdviceTooltip'
import TooltipCard from '@components/common/tooltip/TooltipCard'
import { objectCompare } from '@utils/CompareUtil'
import { CONSTRAINT } from '@models/common/ContraintsByCurrency'
import { numberWithCommas } from '@utils/FormatUtil'
import { getDurationDate } from '@utils/DateUtil'
import { isNumber } from 'lodash'
import NumberFormat from 'react-number-format'
import { ReactComponent as Reload } from '@components/assets/images/reload2.svg'
import { ReactComponent as SelectIconDropDown } from '@components/assets/images/select_icon_drop_down.svg'
import { ReactComponent as SelectIconDropDownUp } from '@components/assets/images/select_icon_drop_down_up.svg'
import ModalTitle from '@components/common/optimization/ModalTitle'
import { ReactComponent as TitleLabelIcon } from '@components/assets/images/icon_label_SA.svg'
import { ReactComponent as AccordionArrow } from '@components/assets/images/accordion_arrow.svg'
import { downloadCSVFile } from '@utils/jsonToCSV'
import { MAX_DATE_VALUE } from '@models/common/CommonConstants'
import { ConvertedToTreeDataInfoV2 } from '@models/optimization/SaOptimizationAdgroups'
import { useActionType, useAuthority, useToast, useDialog } from '@hooks/common'
import { OptimizationIcon, InnerHtml, MopIcon, ProBadge, TransText } from '@components/common'
import { LiteBadge } from '@components/common/BaseChip'
import { MOPIcon } from '@models/common'
import { ActionType } from '@models/common/CommonConstants'
import { Disclosure, Transition } from '@headlessui/react'
import AdvancedOptions, { OptionType } from './AdvancedOptions'
import { advertiserState, getFunctionValue } from '@store/Advertiser'
import CommonTooltip from '@components/common/CommonTooltip'

// TODO
export interface Props {
  onClose: (saveYn: string) => void
  linkedMedia: MediaType[]
  optimizationId?: number
  type: ActionType
  open: boolean
  remainAdgroupsCount?: number
  saShoppingType?: string
}

const SearchOptimizationDetailModal: React.FC<Props> = (props: Props): ReactElement => {
  const { canCreate, isCreateType, isEditType, isReadType } = useActionType(props.type)
  const advertiser = useRecoilValue(advertiserState)
  const clustering =
    useRecoilValue(getFunctionValue({ functionType: 'UI', functionId: 'CLUSTERING' })) === 'ON' ? 'Y' : 'N'
  const maxCpc = useRecoilValue(getFunctionValue({ functionType: 'UI', functionId: 'MAX_CPC' })) === 'ON' ? 'Y' : 'N'
  const turbo = useRecoilValue(getFunctionValue({ functionType: 'UI', functionId: 'TURBO' })) === 'ON' ? 'Y' : 'N'
  const sprint = useRecoilValue(getFunctionValue({ functionType: 'UI', functionId: 'SPRINT' })) === 'ON' ? 'Y' : 'N'
  const cpcReboot =
    useRecoilValue(getFunctionValue({ functionType: 'UI', functionId: 'CPC_REBOOT' })) === 'ON' ? 'Y' : 'N'
  const soList = useRecoilValue(soListState)
  const { openDialog } = useDialog()
  const { openToast } = useToast()
  const soListActiveFilter = useRecoilValue(soListActiveFilterState)
  const initOptimizationInfo = async () => {
    const startDate = add(new Date(), { days: 1 })
    const endDate = MAX_DATE_VALUE
    let optimizationInfo: SearchOptimizationDetail = {
      optimizationName: '',
      advertiserId: advertiser.advertiserId || 0,
      mediaType: Object.values(MediaType).includes((soListActiveFilter.mediaType || '') as MediaType)
        ? (soListActiveFilter.mediaType as MediaType)
        : MediaType.NAVER,
      bidStartDate: format(startDate, DateFnsFormat.DATE),
      bidEndDate: format(endDate, DateFnsFormat.DATE),
      dailyBudget: 0,
      optimizationGoal: OptimizationGoalType.CLICK.valueOf(),
      adgroupIds: [],
      topRankImpressionBoosted: false,
      addTopCpcYn: 'N',
      boostingRateLevel: 0,
      exclusiveMaxCpcYn: maxCpc,
      exclusiveSprintYn: sprint,
      exclusiveTurboYn: turbo,
      exclusiveClusteringYn: clustering,
      exclusiveCpcReboot: cpcReboot,
      cpcReboot: false
    }

    switch (props.type) {
      case ActionType.READ:
      case ActionType.MODIFY: {
        optimizationInfo = await getSearchOptimizationDetail(props.optimizationId!)
        // API에서 받은 데이터에 exclusiveCpcReboot 속성 확인 및 설정
        if (optimizationInfo.exclusiveCpcReboot === undefined) {
          optimizationInfo.exclusiveCpcReboot = cpcReboot
        }
        setSavedOptimizationInfo(cloneDeep(optimizationInfo))
        // Store the original selected adgroups for SelectGroupComponent
        setSelectedAdgroups(cloneDeep(optimizationInfo.adgroupIds))
        if (optimizationInfo.excludeKeywords?.length) {
          setExcludeKeywords(optimizationInfo.excludeKeywords)
        }
        break
      }
      case ActionType.DUPLICATE: {
        const origin = await getSearchOptimizationDetail(props.optimizationId!)
        const { startDate: bidStartDate, endDate: bidEndDate } = getDurationDate(origin.bidEndDate)

        // kpis 배열 중복 제거
        let dedupedKpis: OptimizationKpi[] = []
        if (origin.kpis && origin.kpis.length > 0) {
          const kpiMap = new Map<string, OptimizationKpi>()
          origin.kpis.forEach((kpi) => {
            if (kpi && kpi.kpiType) {
              kpiMap.set(kpi.kpiType, kpi)
            }
          })
          dedupedKpis = Array.from(kpiMap.values())
        }

        optimizationInfo = {
          ...origin,
          optimizationName: `${origin.optimizationName} copy`,
          bidStartDate,
          bidEndDate,
          kpis: dedupedKpis // 중복 제거된 kpis 사용
        }
        // API에서 받은 데이터에 exclusiveCpcReboot 속성 확인 및 설정
        if (optimizationInfo.exclusiveCpcReboot === undefined) {
          optimizationInfo.exclusiveCpcReboot = cpcReboot
        }
        if (optimizationInfo.excludeKeywords?.length) {
          setExcludeKeywords(optimizationInfo.excludeKeywords)
        }
        break
      }
      case ActionType.CREATE:
      default:
        break
    }
    setOptimizationInfo(optimizationInfo)
    setSelectedOptimizationGoal(optimizationInfo.optimizationGoal as OptimizationGoalType)
    getOptimizationCosts(optimizationInfo, false)
    setDailyBudget(optimizationInfo.dailyBudget)
  }
  const [optimizationInfo, setOptimizationInfo] = useState<SearchOptimizationDetail>()
  const [savedOptimizationInfo, setSavedOptimizationInfo] = useState<SearchOptimizationDetail>()
  const [selectedAdgroups, setSelectedAdgroups] = useState<SearchOptimizationAdgroup[]>([])
  const [searchKeyword, setSearchKeyword] = useState<string>('')
  const [currentKeyword, setCurrentKeyword] = useState<Keyword[]>([])
  const [leftKeywords, setLeftKeywords, leftKeywordsStatus, setLeftKeywordsStatus] = useStatefulStateWithInitialData<
    Keyword[]
  >([])
  const [excludeKeywords, setExcludeKeywords] = useState<Keyword[]>([])
  const [optimizationCosts, setOptimizationCosts] = useState<SearchOptimizationCost[]>([])
  const [dailyBudget, setDailyBudget] = useState(0)
  const [sliderMaxValue, setSliderMaxValue] = useState(0)
  const [sliderStepValue, setSliderStepValue] = useState(1)
  const {
    min: BUDGET_MIN,
    max: BUDGET_MAX,
    unit: BUDGET_UNIT
  } = CONSTRAINT[advertiser.advertiserCurrencyCode].SA_BUDGET

  const [startIndex, setStartIndex] = useState<number>()
  const [initCampaignInfo, setInitCampaignInfo] = useState<ConvertedToTreeDataInfoV2[]>()
  const useMaxCPC = optimizationInfo?.exclusiveMaxCpcYn === 'Y'

  const { isProAdvertiser } = useAuthority()
  const maxAdgroupCount = Number(
    useRecoilValue(getFunctionValue({ functionType: 'UI', functionId: 'SA_OPT_TARGET_COUNT' }))
  )
  const adGroupSelectedCount = useMemo(
    () => soList?.optimizations?.find((item) => item.optimizationId === props.optimizationId)?.adgroupsCount || 0,
    [soList, props.optimizationId]
  )
  const remainAdGroupsCount = [ActionType.MODIFY, ActionType.READ].includes(props.type)
    ? adGroupSelectedCount + (props?.remainAdgroupsCount || 0)
    : props?.remainAdgroupsCount || 0

  const setDailyBudgetDebounce = useCallback(
    debounce((value) => {
      handleChangeOptimizationInfo('dailyBudget', value)
    }, 100),
    []
  )
  useEffect(() => {
    setDailyBudgetDebounce(dailyBudget)
  }, [dailyBudget])

  const getOptimizationCosts = async (optInfo: SearchOptimizationDetail, isLoading = false) => {
    const startDate = optInfo.bidStartDate || format(add(new Date(), { days: 1 }), DateFnsFormat.DATE)
    const endDate = optInfo.bidEndDate || format(endOfMonth(new Date()), DateFnsFormat.DATE)
    const adgroupIds = optInfo.adgroupIds.map((item) => item.adgroupId) || []
    const optimizationCostsRequest: SearchOptimizationCostsRequest = {
      mediaType: optInfo.mediaType || MediaType.NAVER,
      advertiserId: advertiser.advertiserId || 0,
      startDate: startDate,
      endDate: endDate,
      adgroupIds: adgroupIds,
      excludeKeywordIds: excludeKeywords.map((keyword) => keyword.keywordId)
    }
    const emptyResult = [
      { days: 7, cost: 0 },
      { days: 14, cost: 0 },
      { days: 30, cost: 0 }
    ]
    const result =
      adgroupIds.length === 0 ? emptyResult : await getSearchOptimizationCosts(optimizationCostsRequest, isLoading)
    const averageCost = result.find((item) => item.days === 7)?.cost || 0
    const step = Number(
      ((2 * averageCost) / 100).toLocaleString('fullwide', { maximumSignificantDigits: 1, useGrouping: false })
    )
    const applyUnit = (val: number) => Math.round(val / BUDGET_UNIT) * BUDGET_UNIT

    setOptimizationCosts(result)
    setSliderMaxValue(applyUnit(2 * averageCost))
    setSliderStepValue(applyUnit(step))
  }

  const minDate = add(new Date(), { days: 1 })
  const maxDate = add(new Date(), { years: 1 })
  const todayDate = format(new Date(), DateFnsFormat.DATE)

  const validationSchema = object()
    .shape({
      mediaType: string().required(t('common.message.validation.required', { param: t('common.label.media') })),
      bidStartDate: string().required(
        t('common.message.validation.required', {
          param: t('optimization.label.SearchOptimizationDetailModal.bidPeriod')
        })
      ),
      bidEndDate: string().required(
        t('common.message.validation.required', {
          param: t('optimization.label.SearchOptimizationDetailModal.bidPeriod')
        })
      ),
      optimizationName: string().required(
        t('common.message.validation.required', {
          param: t('optimization.label.SearchOptimizationDetailModal.optimizationName')
        })
      ),
      dailyBudget: number()
        .typeError(
          t('common.message.validation.required', {
            param: t('optimization.label.SearchOptimizationDetailModal.dailyBudget')
          })
        )
        .min(
          BUDGET_MIN,
          t('optimization.message.SearchOptimizationDetailModal.validation.budgetMin', {
            min: numberWithCommas(BUDGET_MIN)
          })
        )
        .max(
          BUDGET_MAX,
          t('optimization.message.SearchOptimizationDetailModal.validation.budgetMax', {
            max: numberWithCommas(BUDGET_MAX)
          })
        )
        .test(
          'dailyBudgetUnit',
          t('optimization.message.SearchOptimizationDetailModal.validation.budgetUnit', { unit: BUDGET_UNIT }),
          (value) => !!value && isNumber(value) && (+value / BUDGET_UNIT) % 1 === 0
        ),
      optimizationGoal: string().required(
        t('common.message.validation.required', {
          param: t('optimization.label.SearchOptimizationDetailModal.optimizationGoal')
        })
      ),
      kpis: array(),
      adgroupIds: array().min(
        1,
        t('common.message.validation.required', {
          param: t('optimization.label.SearchOptimizationDetailModal.targetAdgroups')
        })
      )
    })
    .test('bidStartDate', t('optimization.message.SearchOptimizationDetailModal.validation.bidPeriod'), (value) => {
      if (!value.bidStartDate || !value.bidEndDate) return true
      return value.bidStartDate <= value.bidEndDate
    })
    .test('kpis', t('optimization.message.SearchOptimizationDetailModal.validation.kpis'), (value) => {
      return !(!value.kpis?.length && value.optimizationGoal === OptimizationGoalType.KPIS.valueOf())
    })
    .test(
      'kpis',
      t('optimization.message.SearchOptimizationDetailModal.validation.maxCpc', {
        min: optimizationInfo?.mediaType === MediaType.NAVER ? numberWithCommas(70) : numberWithCommas(10),
        max: numberWithCommas(100000)
      }),
      (value) => {
        const min = optimizationInfo?.mediaType === MediaType.NAVER ? 70 : 10
        const max = 100000
        const cpc_max_item = value.kpis?.find((item) => item.kpiType == 'CPC_MAX')
        if (cpc_max_item) {
          return min <= cpc_max_item.kpiValue && cpc_max_item.kpiValue <= max
        } else {
          return true
        }
      }
    )
    .test('cpcReboot', t('optimization.AdvancedOptions.validation.cpcReboot'), (value) => {
      if (value.cpcReboot) {
        const cpc_item = value.kpis?.find((item) => item.kpiType === KpiType.CPC)
        const cpa_item = value.kpis?.find((item) => item.kpiType === KpiType.CPA)
        return !(cpc_item || cpa_item)
      }
      return true
    })

  const handleSave = () => {
    // setPopperEl(null);
    if (isEditType) {
      // Compare current selected adgroups with saved ones
      const currentAdgroupIds = selectedAdgroups.map((adgroup) => adgroup.adgroupId).sort()
      const savedAdgroupIds = savedOptimizationInfo?.adgroupIds.map((adgroup) => adgroup.adgroupId).sort() || []

      // Create objects for comparison
      const currentOptimizationInfoForCompare = {
        ...optimizationInfo,
        adgroupIds: currentAdgroupIds,
        excludeKeywordIds: excludeKeywords.map((keyword) => keyword.keywordId)
      }

      const savedOptimizationInfoForCompare = {
        ...savedOptimizationInfo,
        adgroupIds: savedAdgroupIds
      }

      // Compare the objects with the simplified adgroupIds arrays
      if (objectCompare(currentOptimizationInfoForCompare, savedOptimizationInfoForCompare, true)) {
        openToast(t('common.message.notChanged'))
        props.onClose('N')
        return
      }
    }

    if (!isProAdvertiser && (optimizationInfo?.adgroupIds?.length || 0) > (maxAdgroupCount || 0)) {
      openToast(
        t('optimization.message.SearchOptimizationDetailModal.validation.adgroupCount', {
          plan: advertiser.subscriptionProductType,
          num: maxAdgroupCount
        })
      )
      return
    }

    validationSchema
      .validate(optimizationInfo)
      .then((value) => {
        const isPredictable = value.adgroupIds?.map((item) => item.predicted).some((val) => val === true)
        const analyticsConversion = value.adgroupIds?.map((item) => item.analyticsConversion)
        const analyticsType = value.adgroupIds?.map((item) => item.analyticsType)
        const analyticsConversionNum = [...new Set(analyticsConversion)]
        const analyticsTypeNum = [...new Set(analyticsType)]
        const isNotMatchedConversion = analyticsConversionNum.length > 1 || analyticsTypeNum.length > 1
        const messageKey = isNotMatchedConversion
          ? 'optimization.message.SearchOptimizationDetailModal.notMatchedConversion'
          : isPredictable
          ? 'common.message.saveConfirm'
          : 'optimization.message.SearchOptimizationDetailModal.notPredictable'

        if (isProAdvertiser && !isNotMatchedConversion && !isPredictable) {
          openDialog({
            title: t('common.message.title.notice'),
            message: t('common.message.saveConfirm'),
            cancelLabel: t('common.label.button.cancel'),
            actionLabel: t('common.label.button.confirm'),
            onAction: () => {
              saveOptimization()
            }
          })
        } else {
          openDialog({
            title: t('common.message.title.notice'),
            message: t(messageKey),
            cancelLabel: t('common.label.button.cancel'),
            actionLabel: t('common.label.button.confirm'),
            onAction: () => {
              saveOptimization()
            }
          })
        }
      })
      .catch((err) => {
        openToast(err.message)
      })
  }

  const saveOptimization = async () => {
    const campaignIds: string[] = []
    if (optimizationInfo) {
      /**
       * adgroup이 모두 선택이되면 campaignIds만 보내도록 처리
       */
      let filterAdgroupIds: any[] = []
      for (const campaignRow of initCampaignInfo as ConvertedToTreeDataInfoV2[]) {
        const adgroupRows = selectedAdgroups.filter(
          (adgroupRow: { [key: string]: any }) => adgroupRow?.campaignId === campaignRow.id.toString()
        )
        if (!adgroupRows.length) continue

        if (adgroupRows.length !== campaignRow?.adgroups?.length) {
          filterAdgroupIds = filterAdgroupIds.concat(adgroupRows)
        } else {
          campaignIds.push(campaignRow.id.toString())
        }
      }
      const saveRequest: SearchOptimizationSaveRequest = {
        optimizationName: optimizationInfo.optimizationName,
        advertiserId: optimizationInfo.advertiserId,
        mediaType: optimizationInfo.mediaType,
        bidStartDate: optimizationInfo.bidStartDate,
        bidEndDate: optimizationInfo.bidEndDate,
        dailyBudget: optimizationInfo.dailyBudget,
        optimizationGoal: optimizationInfo.optimizationGoal,
        kpis: optimizationInfo.kpis,
        adgroupIds: filterAdgroupIds.map((adgroup) => ({
          accountId: adgroup.accountId,
          adgroupId: adgroup.adgroupId
        })),
        campaignIds,
        excludeKeywordIds: excludeKeywords.map((keyword) => keyword.keywordId),
        topRankImpressionBoosted: optimizationInfo.topRankImpressionBoosted,
        addTopCpcYn: optimizationInfo.addTopCpcYn,
        boostingRateLevel: optimizationInfo.boostingRateLevel,
        cpcReboot: optimizationInfo.cpcReboot
      }
      const { adgroupIds: __omitted__, campaignIds: __omitted2__, ...saveRequestWithoutAdgroup } = saveRequest

      let response
      if (isEditType) {
        if (!optimizationInfo?.optimizationId) return
        const oldAdgroupIds = savedOptimizationInfo!.adgroupIds.map((adgroup) => adgroup.adgroupId)
        const newAdgroupIds = selectedAdgroups.map((adgroup) => adgroup.adgroupId)
        if (oldAdgroupIds.length === newAdgroupIds.length && oldAdgroupIds.every((id) => newAdgroupIds.includes(id))) {
          response = await updateSearchOptimizationWithoutAdgroup(
            optimizationInfo.optimizationId,
            saveRequestWithoutAdgroup as SearchOptimizationSaveRequestWithoutAdgroup
          )
        } else {
          response = await updateSearchOptimization(optimizationInfo.optimizationId, saveRequest)
        }
      } else {
        response = await createSearchOptimization(saveRequest)
      }

      switch (response.statusCode) {
        case StatusCode.SUCCESS:
          openToast(t('common.message.saveSuccess'))
          props.onClose('Y')
          break
        case StatusCode.DUPLICATE_NAME:
          openToast(t('optimization.message.common.duplicateName'))
          break
        case StatusCode.DUPLICATE_ADGROUP:
          openToast(t('optimization.message.SearchOptimizationDetailModal.duplicateAdgroup'))
          break
        case StatusCode.MESSAGE_EXCEED_MAX_ADGROUPS_CONFIG:
          openToast(
            t('optimization.message.SearchOptimizationDetailModal.validation.adgroupCount', {
              plan: advertiser.subscriptionProductType,
              num: maxAdgroupCount
            })
          )
          break
        default:
          openToast(t('common.message.systemError'))
          break
      }
    }
  }

  const handleChangeOptimizationInfo = (key: string, value: any, init?: boolean) => {
    if (
      props.type !== ActionType.READ &&
      key === 'adgroupIds' &&
      !init &&
      (excludeKeywords.length > 0 || leftKeywords.length > 0)
    ) {
      openToast(t('optimization.message.SearchOptimizationDetailModal.changeAdgroups'))
      const adgroupIds = value.map((el: any) => el.adgroupId)
      setSearchKeyword('')
      setExcludeKeywords(excludeKeywords.filter((el) => adgroupIds.includes(el.adgroupId)))
      setLeftKeywords([])
      setLeftKeywordsStatus('INITIAL')
    }
    if (key === 'optimizationGoal') {
      setOptimizationInfo(
        (prev) =>
          ({
            ...prev,
            kpis: [],
            [key]: value
          } as SearchOptimizationDetail)
      )
    } else if (key === 'mediaType') {
      setOptimizationInfo(
        (prev) =>
          ({
            ...prev,
            adgroupIds: [],
            optimizationGoal: OptimizationGoalType.CLICK.valueOf(),
            kpis: [],
            [key]: value
          } as SearchOptimizationDetail)
      )
      setExcludeKeywords([])
      setSearchKeyword('')
      setLeftKeywords([])
      setLeftKeywordsStatus('INITIAL')
      setDailyBudget(0)
    } else {
      if (key === 'bidEndDate' && optimizationInfo?.bidLimitDate && value >= optimizationInfo?.bidLimitDate) {
        openToast(`${t('optimization.message.SearchOptimizationDetailModal.validation.endDate')}
        ${convertDateDisplayFormat(optimizationInfo?.bidLimitDate) ?? ''}`)
      }
      setOptimizationInfo(
        (prev) =>
          ({
            ...prev,
            [key]: value
          } as SearchOptimizationDetail)
      )
    }
  }

  const handleOnKeyPress = (event: React.KeyboardEvent<HTMLDivElement>) => {
    if (event.key === 'Enter') {
      event.preventDefault()
      if (optimizationInfo!.adgroupIds.length === 0) {
        openToast(t('optimization.message.SearchOptimizationDetailModal.validation.emptyAdgroupIds'))
      } else if (searchKeyword.length < 2) {
        openToast(t('optimization.message.SearchOptimizationDetailModal.validation.keywordLength'))
      } else {
        setCurrentKeyword([])
        handleSearchKeyword()
      }
    }
  }

  const handleSearchKeywordClick = () => {
    if (optimizationInfo!.adgroupIds.length === 0) {
      openToast(t('optimization.message.SearchOptimizationDetailModal.validation.emptyAdgroupIds'))
    } else if (searchKeyword.length < 2) {
      openToast(t('optimization.message.SearchOptimizationDetailModal.validation.keywordLength'))
    } else {
      setCurrentKeyword([])
      handleSearchKeyword()
    }
  }

  const handleSearchKeyword = () => {
    const request: SearchKeywordRequest = {
      searchKeyword: searchKeyword,
      adgroupIds: optimizationInfo?.adgroupIds.map((adgroup) => adgroup.adgroupId),
      mediaType: optimizationInfo!.mediaType
    }
    setLeftKeywordsStatus('LOADING')
    getKeywords(request, false).then((response) => {
      const searchKeywords = response.filter((keyword) => {
        return excludeKeywords.filter((obj) => obj.keywordId === keyword.keywordId).length === 0
      })
      if (searchKeywords.length === 0) {
        openToast(t('optimization.message.SearchOptimizationDetailModal.searchingKeywordIsEmpty'))
      }
      setLeftKeywords(searchKeywords)
      setLeftKeywordsStatus('LOADED')
    })
  }

  const handleMoveLeft = () => {
    if (currentKeyword.length > 0) {
      setExcludeKeywords(excludeKeywords.filter((exclude: Keyword) => !currentKeyword.includes(exclude)))
      setLeftKeywords(leftKeywords.concat(currentKeyword))
      setCurrentKeyword([])
    }
  }

  const handleMoveRight = () => {
    if (currentKeyword.length > 0) {
      setLeftKeywords(leftKeywords.filter((obj) => !currentKeyword.includes(obj)))
      setExcludeKeywords(excludeKeywords.concat(currentKeyword))
      setCurrentKeyword([])
    }
  }

  const handleToggle = (e: React.MouseEvent<HTMLElement>, keyword: Keyword, keywords: Keyword[]) => {
    if (e.shiftKey) {
      const last = keywords.findIndex((k) => k.keywordId === keyword.keywordId)
      const multiRange = keywords.slice(startIndex, last + 1)
      setCurrentKeyword(multiRange)
    } else {
      setStartIndex(keywords.findIndex((k) => k.keywordId === keyword.keywordId))
      setCurrentKeyword([keyword])
    }
  }

  const handleDoubleClick = (keyword: Keyword) => {
    setCurrentKeyword([keyword])
    if (find(leftKeywords, { keywordId: keyword.keywordId })) {
      handleMoveRight()
    }
    if (find(excludeKeywords, { keywordId: keyword.keywordId })) {
      handleMoveLeft()
    }
  }

  const convertDate = (dateStr: string): Date => {
    return convertFormatToDate(dateStr, DateFnsFormat.DATE)
  }

  useEffect(() => {
    initOptimizationInfo()
    console.log(advertiser)
  }, []) // eslint-disable-line

  const getKeywordsComponent = (keywords: Keyword[], disabled: boolean) => {
    return (
      <>
        {keywords.map((keyword: Keyword) => {
          return (
            <ListItem
              key={keyword.keywordId}
              role="listitem"
              button
              onClick={(e) => handleToggle(e, keyword, keywords)}
              onDoubleClick={() => handleDoubleClick(keyword)}
              selected={currentKeyword.includes(keyword)}
              disabled={disabled}
            >
              <ListItemText
                primary={
                  <>
                    {keyword.keywordName}{' '}
                    <span>
                      ( {keyword.campaignName} / {keyword.adgroupName} )
                    </span>
                  </>
                }
                secondary={`ID:${keyword.keywordId}`}
              />
            </ListItem>
          )
        })}
      </>
    )
  }

  const renderOptimizationGoal = (goal: OptimizationGoal) => {
    const disabled =
      isReadType ||
      (isEditType &&
        !!optimizationInfo &&
        !!optimizationInfo.bidStartDate &&
        optimizationInfo.bidStartDate <= todayDate)
    return (
      <ToggleButton
        className={'optimization-goal'}
        key={goal.optimizationGoal}
        value={goal.optimizationGoal}
        // mac cpc 사용하는 광고주 CUSTOM STYLE
        style={useMaxCPC ? { height: 130 } : {}}
        disabled={disabled}
        disableRipple
      >
        {goal.optimizationGoal === OptimizationGoalType.KPIS &&
        selectedOptimizationGoal === OptimizationGoalType.KPIS ? (
          <div className="optimization-description-container optimization-kpis-settings">
            <MultiKpiSettings
              kpis={optimizationInfo!.kpis}
              setKpis={(kpis: OptimizationKpi[]) => {
                setBaseKpis(kpis)
              }}
              modalType={props.type}
              notYetStarted={todayDate < optimizationInfo!.bidStartDate}
            />
          </div>
        ) : (
          <>
            <div className="optimization-name-kor">
              {t(`optimization.label.SearchOptimizationDetailModal.label.${goal.optimizationGoal}`)}
            </div>
            {/* <div className="optimization-name-eng">
            {t(`optimization.label.SearchOptimizationDetailModal.labelEng.${goal.optimizationGoal}`)}
          </div> */}
            <OptimizationIcon goalType={goal.optimizationGoal} size={34} />
            <div className="optimization-description-container">
              <InnerHtml
                className="optimization-description"
                innerHTML={t(`optimization.label.SearchOptimizationDetailModal.description.${goal.optimizationGoal}`)}
              />
            </div>
          </>
        )}
      </ToggleButton>
    )
  }

  const isLinkedMedia = (targetMedia: MediaType) => {
    return props.linkedMedia.includes(targetMedia)
  }

  const downloadNegativeKeywrods = () => {
    if (excludeKeywords.length === 0) {
      openToast(t('optimization.message.searchOptimization.toast.emptyNegative'))
      return
    }
    const negativeCSV = excludeKeywords.map((word) => ({
      optimizationId: props.optimizationId,
      keywordId: word.keywordId,
      keywordName: word.keywordName,
      campaignId: word.campaignId,
      campaignName: word.campaignName,
      adgroupId: word.adgroupId,
      adgroupName: word.adgroupName
    }))
    downloadCSVFile(negativeCSV, `${props.optimizationId}-negativeKeyword`)
  }

  const [popperEl, setPopperEl] = useState<HTMLElement | null>(null)
  const [openDisclosure, setOpenDisclosure] = useState<boolean>(true)
  const [selectedOptimizationGoal, setSelectedOptimizationGoal] = useState<OptimizationGoalType>(
    OptimizationGoalType.CLICK
  )
  const setBaseKpis = (base_kpis: OptimizationKpi[]) => {
    const kpis = optimizationInfo?.kpis || []
    const cpc = kpis.find((item) => item.kpiType === KpiType.CPC)
    const cpa = kpis.find((item) => item.kpiType === KpiType.CPA)
    const cpc_max = kpis.find((item) => item.kpiType === KpiType.CPC_MAX)
    handleChangeOptimizationInfo(
      'kpis',
      base_kpis.concat([cpc, cpa, cpc_max].filter((item) => !!item) as OptimizationKpi[])
    )
  }

  const updateKpi = (kpiType: KpiType, rawValue: string) => {
    const kpis = optimizationInfo?.kpis || []
    const advanced_kpi_types = [KpiType.CPC, KpiType.CPA, KpiType.CPC_MAX]
    const base_kpis = kpis.filter((item) => advanced_kpi_types.indexOf(item.kpiType as KpiType) < 0)
    const cpc = kpis.find((item) => item.kpiType === KpiType.CPC)
    const cpa = kpis.find((item) => item.kpiType === KpiType.CPA)
    const cpc_max = kpis.find((item) => item.kpiType === KpiType.CPC_MAX)
    const new_cpc =
      kpiType === KpiType.CPC_MAX
        ? cpc
        : kpiType === KpiType.CPC && rawValue
        ? { kpiType, kpiValue: Number(rawValue) }
        : null
    const new_cpa =
      kpiType === KpiType.CPC_MAX
        ? cpa
        : kpiType === KpiType.CPA && rawValue
        ? { kpiType, kpiValue: Number(rawValue) }
        : null
    const new_cpc_max =
      kpiType === KpiType.CPC_MAX ? (rawValue ? { kpiType, kpiValue: Number(rawValue) } : null) : cpc_max
    handleChangeOptimizationInfo(
      'kpis',
      base_kpis.concat([new_cpc, new_cpa, new_cpc_max].filter((item) => !!item) as OptimizationKpi[])
    )
  }

  return (
    <Dialog
      className={`SearchOptimizationDetailModal${useMaxCPC ? '--use-max-cpc' : ''}`}
      id={`SearchOptimizationDetailModal`}
      open={props.open}
      fullScreen
      scroll="body"
      onClose={() => props.onClose('N')}
    >
      <DialogTitle>
        <IconButton className="modal-close" aria-label="close" onClick={() => props.onClose('N')}>
          <img alt="close-image" src={CloseIcon} />
        </IconButton>
      </DialogTitle>
      <DialogContent>
        {optimizationInfo?.mediaType && (
          <Grid container>
            <Grid item className="LeftGrid">
              <Paper>
                <Box id="title">
                  <ModalTitle
                    type={props.type}
                    title={t('optimization.label.SearchOptimizationDetailModal.modalTitle.sa')}
                    icon={<TitleLabelIcon width={24} />}
                  />
                </Box>
                <Box id="select-media-row">
                  <FormLabel>{t('optimization.label.SearchOptimizationDetailModal.label.media')}</FormLabel>
                  <SelectBottom
                    id="select-media"
                    data-testid="MediaTypeSelect"
                    value={optimizationInfo.mediaType}
                    onChange={(event) => {
                      handleChangeOptimizationInfo('mediaType', event.target.value)
                    }}
                    displayEmpty
                    disabled={isEditType || isReadType}
                    MenuProps={{
                      id: 'select-media-items',
                      anchorOrigin: {
                        vertical: 28,
                        horizontal: 'left'
                      }
                    }}
                  >
                    {getSaMediaCodes().map((media) => {
                      return (
                        <MenuItem
                          key={media.mediaType}
                          value={media.mediaType}
                          disabled={!isLinkedMedia(media.mediaType)}
                        >
                          {media.mediaName}
                        </MenuItem>
                      )
                    })}
                  </SelectBottom>
                </Box>
                <Box id="BidPeriod">
                  <FormLabel>{t('optimization.label.SearchOptimizationDetailModal.label.bidPeriod')}</FormLabel>
                  <div>
                    <DatePeriodPicker
                      id="sa-optimizatoin-modal-period"
                      disableToolbar={true}
                      startDate={optimizationInfo.bidStartDate}
                      endDate={optimizationInfo.bidEndDate}
                      autoOk
                      onClickStartDate={(date) => {
                        date && handleChangeOptimizationInfo('bidStartDate', format(date, DateFnsFormat.DATE))
                      }}
                      onClickEndDate={(date) => {
                        date && handleChangeOptimizationInfo('bidEndDate', format(date, DateFnsFormat.DATE))
                      }}
                      minStartDate={
                        canCreate || (isEditType && todayDate < optimizationInfo.bidStartDate) ? minDate : undefined
                      }
                      maxStartDate={maxDate}
                      minEndDate={
                        isCreateType
                          ? minDate
                          : isReadType || (isEditType && optimizationInfo.bidEndDate < todayDate)
                          ? undefined
                          : max([convertDate(optimizationInfo.bidStartDate), convertDate(todayDate)])
                      }
                      disabledStartDate={(isEditType && optimizationInfo.bidStartDate <= todayDate) || isReadType}
                      disabledEndDate={(isEditType && optimizationInfo.bidEndDate < todayDate) || isReadType}
                      allowUnsetEndDate={true}
                    />
                  </div>
                </Box>
                <Box>
                  <SelectGroupComponent
                    mediaType={optimizationInfo.mediaType}
                    startDate={optimizationInfo.bidStartDate}
                    endDate={optimizationInfo.bidEndDate}
                    selectedGroups={selectedAdgroups}
                    setSelectedGroups={(
                      adgroupIds: SearchOptimizationAdgroup[],
                      init?: boolean,
                      initData?: ConvertedToTreeDataInfoV2[]
                    ) => {
                      // Update both selectedAdgroups and optimizationInfo.adgroupIds
                      if (!init) {
                        setSelectedAdgroups(adgroupIds)
                      }
                      handleChangeOptimizationInfo('adgroupIds', adgroupIds, init)
                      if (init) {
                        setInitCampaignInfo(initData)
                      }
                    }}
                    optimizationId={props.optimizationId}
                    type={props.type}
                    saShoppingType={props.saShoppingType}
                  />
                </Box>
                <Box className="select-adgroups-bottom">
                  <Box id="select-adgroups-info">
                    <div>
                      <Icon className="select-mark-blue" />
                      <InnerHtml innerHTML={t('optimization.label.SearchOptimizationDetailModal.possibleBidding')} />
                    </div>
                    <div className="!items-start">
                      <Icon className="select-mark-red !mt-1" />
                      <div>
                        <InnerHtml
                          innerHTML={t('optimization.label.SearchOptimizationDetailModal.conditionalBidding')}
                        />
                        <div className="flex items-center gap-1">
                          <ProBadge className="!text-white" size="mini" />
                          <InnerHtml innerHTML={t('optimization.label.SearchOptimizationDetailModal.isPro')} />
                        </div>
                      </div>
                    </div>
                    <div>
                      <Icon className="select-mark-gray" />
                      <InnerHtml innerHTML={t('optimization.label.SearchOptimizationDetailModal.impossibleBidding')} />
                    </div>
                    <div>
                      <Icon className="select-mark-new" />
                      <InnerHtml innerHTML={t('optimization.label.SearchOptimizationDetailModal.newBidding')} />
                    </div>
                  </Box>
                  {/* 99 */}
                  <Box
                    id="select-adgroups-count"
                    className={`${
                      (isEditType && optimizationInfo.bidStartDate <= todayDate) || isReadType ? 'disabled' : ''
                    }`}
                  >
                    {optimizationInfo && (
                      <TextField
                        value={optimizationInfo.adgroupIds?.length || 0}
                        InputProps={{
                          inputComponent: IntegerNumberFormat,
                          startAdornment: <CountCheckIcon />,
                          style: {
                            color:
                              !isProAdvertiser && (optimizationInfo.adgroupIds?.length || 0) > remainAdGroupsCount
                                ? '#B51B3E'
                                : undefined
                          }
                        }}
                        disabled
                      />
                    )}
                    {isProAdvertiser ? (
                      <></>
                    ) : (
                      <span className="text-gray-500 text-base absolute right-[70px] top-[3px]">
                        /{remainAdGroupsCount}
                      </span>
                    )}
                    <div className="unit">
                      <CommonTooltip
                        id="sa-optimization-advice-tooltip-maxCPC"
                        title={
                          <>
                            <TransText as="h1" i18nKey={'optimization.message.searchOptimization.tooltip.title'} />
                            <div className="common-style">
                              <TransText
                                as="p"
                                i18nKey={'optimization.message.searchOptimization.tooltip.contents.0'}
                              />
                              <div className="!mt-2.5 !py-2.5 align-middle leading-relaxed bg-[#F2F3F6] rounded-sm">
                                <div className="flex flex-col justify-center items-center">
                                  <div className="flex justify-start items-center w-[260px]">
                                    <ProBadge disabled={false} className="mb-[-3px]" />
                                    <TransText
                                      as="p"
                                      i18nKey={'optimization.message.searchOptimization.tooltip.contents.1'}
                                    />
                                  </div>
                                </div>
                                <div className="flex justify-center items-center">
                                  <div className="flex justify-start items-center w-[260px]">
                                    <LiteBadge disabled={false} className="mb-[-3px]" />
                                    <TransText
                                      as="p"
                                      i18nKey={'optimization.message.searchOptimization.tooltip.contents.2'}
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>
                          </>
                        }
                        placement="right-start"
                        arrow
                      >
                        <span>
                          <AdviceMarkIcon className="mr-0.5 mb-[-2px]" />
                          {t('optimization.label.ShoppingOptimizationDetailModal.unit')}
                        </span>
                      </CommonTooltip>
                    </div>
                  </Box>
                </Box>
              </Paper>
            </Grid>
            <Grid item className="RightGrid">
              <Paper>
                <Box id="header">
                  <FormLabel>{t('optimization.label.SearchOptimizationDetailModal.label.setting')}</FormLabel>
                </Box>
                <Box id="optimization-name">
                  <FormLabel>
                    <AdviceTooltip
                      id="sa-optimization-advice-tooltip-optimization-name"
                      title={
                        <TooltipCard
                          tKey="optimization.label.SearchOptimizationDetailModal.tooltip.optimizationName"
                          type="paragraph"
                        />
                      }
                      placement="right-start"
                      arrow
                    >
                      <span className="icon">
                        <AdviceMarkIcon />
                      </span>
                    </AdviceTooltip>
                    {t('optimization.label.SearchOptimizationDetailModal.label.optimizationName')}
                  </FormLabel>
                  <TextField
                    data-testid="optimizationName"
                    value={optimizationInfo.optimizationName}
                    onChange={(event) => {
                      handleChangeOptimizationInfo('optimizationName', event.target.value)
                    }}
                    inputProps={{
                      maxLength: 30
                    }}
                    disabled={isReadType}
                  />
                </Box>
                <Box id="budget">
                  <FormLabel>
                    <AdviceTooltip
                      id="sa-optimization-advice-tooltip-budget"
                      title={
                        <InnerHtml
                          innerHTML={t('optimization.label.SearchOptimizationDetailModal.tooltip.dailyBudget', {
                            min: numberWithCommas(BUDGET_MIN),
                            max: numberWithCommas(BUDGET_MAX),
                            unit: numberWithCommas(BUDGET_UNIT)
                          })}
                        />
                      }
                      placement="right-start"
                      arrow
                    >
                      <span className="icon">
                        <AdviceMarkIcon />
                      </span>
                    </AdviceTooltip>
                    {t('optimization.label.SearchOptimizationDetailModal.label.dailyBudget')}
                    <Box className="dailyAverageSpending">
                      <span className="recommended-label">
                        {t('optimization.label.SearchOptimizationDetailModal.label.recommended')}
                      </span>
                      <AdviceTooltip
                        id="sa-optimization-advice-tooltip-daily-average-spending"
                        title={
                          <InnerHtml
                            innerHTML={t(
                              'optimization.label.SearchOptimizationDetailModal.tooltip.dailyAverageSpending'
                            )}
                          />
                        }
                        placement="right-start"
                        arrow
                      >
                        <span className="icon">
                          <AdviceMarkIcon />
                        </span>
                      </AdviceTooltip>
                      <span className="label">
                        {t('optimization.label.SearchOptimizationDetailModal.label.dailyAverageSpending')}
                      </span>
                      <IconButton
                        className="dropdown"
                        onClick={(event) => {
                          event.stopPropagation()
                          event.preventDefault()
                          if (!popperEl) {
                            getOptimizationCosts(optimizationInfo, true)
                          }
                          setPopperEl(popperEl ? null : event.currentTarget)
                        }}
                      >
                        {!popperEl && <SelectIconDropDown width="24" height="24" />}
                        {!!popperEl && <SelectIconDropDownUp width="24" height="24" />}
                      </IconButton>
                      <Popper
                        id={popperEl ? 'sa-optimization-table-popper' : ''}
                        open={!!popperEl}
                        anchorEl={popperEl}
                        placement="bottom-end"
                        className={`${
                          (isEditType && optimizationInfo.bidEndDate < todayDate) || isReadType ? 'disabled' : ''
                        }`}
                      >
                        <Box id="sa-optimization-table-caption">
                          <FormLabel>{t('optimization.label.SearchOptimizationDetailModal.vatIncluded')}</FormLabel>
                        </Box>
                        <Table id="sa-optimization-table">
                          <colgroup>
                            <col width="27%" />
                            <col width="27%" />
                            <col width="27%" />
                            <col width="19%" />
                          </colgroup>
                          <TableBody>
                            <TableRow>
                              <TableCell>
                                <InnerHtml
                                  innerHTML={t(
                                    'optimization.label.SearchOptimizationDetailModal.dailyAverageSpending.last7Days'
                                  )}
                                />
                                <div className="recommended">
                                  {t(
                                    'optimization.label.SearchOptimizationDetailModal.dailyAverageSpending.recommended'
                                  )}
                                </div>
                              </TableCell>
                              <TableCell>
                                <InnerHtml
                                  innerHTML={t(
                                    'optimization.label.SearchOptimizationDetailModal.dailyAverageSpending.last14Days'
                                  )}
                                />
                              </TableCell>
                              <TableCell>
                                <InnerHtml
                                  innerHTML={t(
                                    'optimization.label.SearchOptimizationDetailModal.dailyAverageSpending.last30Days'
                                  )}
                                />
                              </TableCell>
                              <TableCell rowSpan={2} className="reload">
                                <span>{t('optimization.label.SearchOptimizationDetailModal.update')}</span>
                                <IconButton
                                  onClick={() => getOptimizationCosts(optimizationInfo, true)}
                                  disabled={(isEditType && optimizationInfo.bidEndDate < todayDate) || isReadType}
                                >
                                  <Reload />
                                </IconButton>
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell>
                                <NumberFormat
                                  displayType="text"
                                  value={optimizationCosts.find((item) => item.days === 7)?.cost || 0}
                                  thousandsGroupStyle="thousand"
                                  thousandSeparator=","
                                />
                              </TableCell>
                              <TableCell>
                                <NumberFormat
                                  displayType="text"
                                  value={optimizationCosts.find((item) => item.days === 14)?.cost || 0}
                                  thousandsGroupStyle="thousand"
                                  thousandSeparator=","
                                />
                              </TableCell>
                              <TableCell>
                                <NumberFormat
                                  displayType="text"
                                  value={optimizationCosts.find((item) => item.days === 30)?.cost || 0}
                                  thousandsGroupStyle="thousand"
                                  thousandSeparator=","
                                />
                              </TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                      </Popper>
                    </Box>
                  </FormLabel>
                  <TextField
                    data-testid="dailyBudget"
                    value={dailyBudget}
                    onChange={(event) => {
                      setDailyBudget(Number(event.target.value))
                    }}
                    InputProps={{
                      inputComponent: IntegerNumberFormat,
                      endAdornment: (
                        <InputAdornment position="end">
                          {t('optimization.label.SearchOptimizationDetailModal.vatExcluded')}
                        </InputAdornment>
                      )
                    }}
                    disabled={(isEditType && optimizationInfo.bidEndDate < todayDate) || isReadType}
                  />
                  <div className="vat-included">
                    <b>{numberWithCommas(Number((1.1 * Number(dailyBudget ?? 0)).toFixed(0)))}</b>
                    &nbsp;&nbsp;
                    <small>{t('optimization.label.SearchOptimizationDetailModal.vatIncluded')}</small>
                  </div>
                </Box>
                <Box id="daily-average-spending">
                  <Box className="placeholder"></Box>
                  <Box className="slider-wrapper">
                    <Box className="rows">
                      <Slider
                        className="slider"
                        valueLabelDisplay="auto"
                        valueLabelFormat={() => numberWithCommas(dailyBudget)}
                        value={dailyBudget}
                        min={0}
                        max={sliderMaxValue}
                        step={sliderStepValue}
                        onChange={(event, value) => {
                          setDailyBudget(value as number)
                        }}
                        disabled={(isEditType && optimizationInfo.bidEndDate < todayDate) || isReadType}
                      />
                      <Box className="row">
                        <InnerHtml
                          innerHTML={t('optimization.label.SearchOptimizationDetailModal.dailyAverageSpending.min')}
                        />
                        <InnerHtml
                          innerHTML={t('optimization.label.SearchOptimizationDetailModal.dailyAverageSpending.avr')}
                        />
                        <InnerHtml
                          innerHTML={t('optimization.label.SearchOptimizationDetailModal.dailyAverageSpending.max')}
                        />
                      </Box>
                    </Box>
                  </Box>
                </Box>
                <Box id="optimization-goals">
                  <div>
                    <FormLabel>
                      <AdviceTooltip
                        id="sa-optimization-advice-tooltip-optimization-goal"
                        title={
                          <TooltipCard
                            tKey="optimization.label.SearchOptimizationDetailModal.tooltip.optimizationGoal"
                            type="paragraph"
                          />
                        }
                        placement="right-start"
                        arrow
                      >
                        <span className="icon">
                          <AdviceMarkIcon />
                        </span>
                      </AdviceTooltip>
                      {t('optimization.label.SearchOptimizationDetailModal.label.optimizationGoal')}
                    </FormLabel>
                  </div>
                  <div style={{ marginTop: '14px' }}>
                    <ToggleButtonGroup
                      data-testid="optimizationGoal"
                      value={selectedOptimizationGoal}
                      exclusive={true}
                      onChange={(event, value) => {
                        if (value === null) {
                          setSelectedOptimizationGoal(selectedOptimizationGoal)
                          return
                        }

                        setSelectedOptimizationGoal(value)
                        if (
                          value === OptimizationGoalType.CLICK ||
                          value === OptimizationGoalType.CONVERSION ||
                          value === OptimizationGoalType.ROAS
                        ) {
                          handleChangeOptimizationInfo('optimizationGoal', value)
                          setBaseKpis([])
                        } else if (OptimizationGoalType.KPIS) {
                          handleChangeOptimizationInfo('optimizationGoal', OptimizationGoalType.KPIS)
                          setBaseKpis([])
                        }
                      }}
                    >
                      {(() => {
                        const goals = getOptimizationGoalCodes()

                        return goals.map((goal) => renderOptimizationGoal(goal))
                      })()}
                    </ToggleButtonGroup>
                  </div>
                </Box>
                {/* <h3 className="text-primary-text text-base font-black leading-8">고급 옵션설정</h3> */}
                <Disclosure as="div" className="mt-2.5 border border-[#E9E9E9] rounded" defaultOpen={openDisclosure}>
                  <Disclosure.Button
                    className="w-full py-1.5 px-5 bg-slate-lighter border-b border-[#E9E9E9] text-primary-text text-sm leading-8 font-bold flex justify-between items-center"
                    onClick={() => setOpenDisclosure(!openDisclosure)}
                  >
                    <span>{t('optimization.label.SearchOptimizationDetailModal.label.advancedSettingOptions')}</span>
                    <AccordionArrow className={openDisclosure ? 'rotate-180 transform' : ''} />
                  </Disclosure.Button>
                  <Transition show={openDisclosure}>
                    <Disclosure.Panel>
                      <AdvancedOptions
                        optimizationInfo={optimizationInfo}
                        handleChangeOptimizationInfo={handleChangeOptimizationInfo}
                        updateKpi={updateKpi}
                        optionType={OptionType.SA}
                        isEditType={isEditType}
                        isReadType={isReadType}
                        todayDate={todayDate}
                      />
                    </Disclosure.Panel>
                  </Transition>
                </Disclosure>
                <Disclosure as="div" className="mt-2.5 border border-[#E9E9E9] rounded">
                  <Disclosure.Button
                    className="w-full py-1.5 px-5 bg-slate-lighter border-b border-[#E9E9E9] text-primary-text text-sm leading-8 font-bold flex justify-between items-center"
                    onClick={() => setOpenDisclosure(!openDisclosure)}
                  >
                    <h4>
                      <AdviceTooltip
                        id="sa-optimization-advice-tooltip-negative-keywrods"
                        title={
                          <InnerHtml
                            innerHTML={t('optimization.label.SearchOptimizationDetailModal.tooltip.negativeKeywrods')}
                          />
                        }
                        placement="right-start"
                        arrow
                      >
                        <span className="align-middle">
                          <AdviceMarkIcon />
                        </span>
                      </AdviceTooltip>
                      &nbsp;{t('optimization.label.SearchOptimizationDetailModal.label.negativeKeywrods')}&nbsp;
                    </h4>
                    <AccordionArrow className={!openDisclosure ? 'rotate-180 transform' : ''} />
                  </Disclosure.Button>
                  <Transition show={!openDisclosure}>
                    <Disclosure.Panel>
                      {(isReadType || isEditType) && (
                        <Box id="negative-keywrods">
                          {/* <FormLabel>
                          <AdviceTooltip
                            id="sa-optimization-advice-tooltip-negative-keywrods"
                            title={
                              <InnerHtml
                                innerHTML={t('optimization.label.SearchOptimizationDetailModal.tooltip.negativeKeywrods')}
                              />
                            }
                            placement="right-start"
                            arrow
                          >
                            <span className="icon">
                              <AdviceMarkIcon />
                            </span>
                          </AdviceTooltip>
                          {t('optimization.label.SearchOptimizationDetailModal.labelEng.negativeKeywrods')}
                          <span>{t('optimization.label.SearchOptimizationDetailModal.labelKor.negativeKeywrods')}</span>
                        </FormLabel> */}
                          <div className="download-box">
                            <MopIcon name={MOPIcon.DOWNLOAD} onClick={downloadNegativeKeywrods} size={24} />
                          </div>
                        </Box>
                      )}
                      <Box id="keywords-list">
                        <Grid container justifyContent="space-between" alignItems="center">
                          <Grid item id="LeftShuttle">
                            <Grid container direction="column" alignItems="center">
                              <Grid item id="search-keyword">
                                <TextField
                                  data-testid="searchKeyword"
                                  value={searchKeyword}
                                  onChange={(event) => setSearchKeyword(event.target.value)}
                                  placeholder={t('optimization.label.SearchOptimizationDetailModal.keywordOrKeywordId')}
                                  InputProps={{
                                    endAdornment: (
                                      <SearchIcon className="search-icon" onClick={() => handleSearchKeywordClick()} />
                                    )
                                  }}
                                  disabled={(isEditType && optimizationInfo.bidEndDate < todayDate) || isReadType}
                                  onKeyPress={handleOnKeyPress}
                                  inputProps={{
                                    maxLength: SearchingTextLimitLength
                                  }}
                                />
                              </Grid>
                              <Grid item id="searching-keywords">
                                <WithLoader status={leftKeywordsStatus}>
                                  <Paper>
                                    {leftKeywords.length > 0 && (
                                      <List dense component="div" role="list" data-testid="leftKeywordList">
                                        {getKeywordsComponent(
                                          leftKeywords,
                                          (isEditType && optimizationInfo.bidEndDate < todayDate) || isReadType
                                        )}
                                        <ListItem className="empty" />
                                      </List>
                                    )}
                                  </Paper>
                                </WithLoader>
                              </Grid>
                            </Grid>
                          </Grid>
                          <Grid item id="ShuttleButton">
                            <Grid container direction="column" alignItems="center">
                              <IconButton
                                id="move-right"
                                data-testid="moveRightBtn"
                                size="small"
                                onClick={handleMoveRight}
                                disabled={
                                  leftKeywords.length === 0 ||
                                  (isEditType && optimizationInfo.bidEndDate < todayDate) ||
                                  isReadType
                                }
                                aria-label="move selected right"
                              >
                                <img src={RightArrowIcon} />
                              </IconButton>
                              <IconButton
                                id="move-left"
                                data-testid="moveLeftBtn"
                                size="small"
                                onClick={handleMoveLeft}
                                disabled={
                                  excludeKeywords.length === 0 ||
                                  (isEditType && optimizationInfo.bidEndDate < todayDate) ||
                                  isReadType
                                }
                                aria-label="move selected left"
                              >
                                <img src={LeftArrowIcon} />
                              </IconButton>
                            </Grid>
                          </Grid>
                          <Grid item id="RightShuttle">
                            <Paper>
                              <List dense component="div" role="list" data-testid="excludeKeywordList">
                                {getKeywordsComponent(
                                  excludeKeywords,
                                  (isEditType && optimizationInfo.bidEndDate < todayDate) || isReadType
                                )}
                                <ListItem className="empty" />
                              </List>
                            </Paper>
                          </Grid>
                        </Grid>
                      </Box>
                    </Disclosure.Panel>
                  </Transition>
                </Disclosure>
                <Box id="save">
                  {props.type !== ActionType.READ && (
                    <Button
                      data-testid="saveButton"
                      variant="contained"
                      onClick={handleSave}
                      className="DialogSaveButton"
                    >
                      {t('optimization.label.SearchOptimizationDetailModal.save')}
                    </Button>
                  )}
                </Box>
              </Paper>
            </Grid>
          </Grid>
        )}
      </DialogContent>
    </Dialog>
  )
}
export default SearchOptimizationDetailModal
