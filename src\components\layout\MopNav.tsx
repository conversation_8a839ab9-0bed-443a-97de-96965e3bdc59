import React, { useEffect, useState } from 'react'
import {
  Box,
  Paper,
  List,
  ListSubheader,
  ListItem,
  ListItemText,
  Grid,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@material-ui/core/'
import { ReactComponent as IconMenuHeader } from '@components/assets/images/menu_icon_header.svg'
import { ReactComponent as IconReport } from '@components/assets/images/menu_icon_report.svg'
import { ReactComponent as IconShopping } from '@components/assets/images/menu_icon_shopping.svg'
import { ReactComponent as IconSA } from '@components/assets/images/menu_icon_search.svg'
import { ReactComponent as IconBudget } from '@components/assets/images/menu_icon_budget.svg'
import { ReactComponent as IconTools } from '@components/assets/images/menu_icon_tools.svg'
import { ReactComponent as IconApiCenter } from '@components/assets/images/menu_icon_api_center.svg'
import { ReactComponent as IconSetting } from '@components/assets/images/menu_icon_setting.svg'

import LockIcon from '@material-ui/icons/Lock'
import { useTranslation } from 'react-i18next'
import './MopNav.scss'
import { MenuGroupItem, MenuGroupType, MenuItem, MenuType } from '@models/common/Menu'
import { useNavigate, useLocation } from 'react-router-dom'
import InnerHtml from '@components/common/InnerHtml'
import SessionUtil from '@utils/SessionUtil'

import { anomalyUrlNoti, anomalyUtmNoti, anomalySpaNoti } from '@store/detectionCenter'
import { useRecoilValue } from 'recoil'
import { useAuthority, useDialog, useI18n, useLayout } from '@hooks/common'
import { BetaBadge, ProBadge } from '@components/common'

const sessionUtil = new SessionUtil()
const email = sessionUtil.getSessionInfo().email
const isTester = email?.endsWith('@mop.co.kr')

const menuGroups: MenuGroupItem[] = [
  {
    type: MenuGroupType.SA_SHOPPING,
    icon: () => <IconShopping data-testid="icon-menu-optimization" id="icon-menu-optimization" className="icon-menu" />,
    subMenuItems: [
      {
        type: MenuType.NEGATIVE_KEYWORD,
        pathname: '/negative-keyword'
      },
      {
        type: MenuType.CREATE_CAMPAIGN,
        pathname: '/campaign',
      },
      {
        type: MenuType.BID_OPT_SHOP,
        pathname: '/shopping-optimization'
      },
      {
        type: MenuType.ANOMALY_DETECTION_SPA,
        pathname: '/report/anomaly-detection-spa'
      },
      {
        type: MenuType.BUDGET_SHOP,
        pathname: '/budget/SA_SHOPPING'
      },
      {
        type: MenuType.COMPETITION_SHOP,
        pathname: '/competition/SA_SHOPPING'
      },
      {
        type: MenuType.TARGET_BID,
        pathname: '/target-bidding'
      },     
    ]
  },
  {
    type: MenuGroupType.SA,
    icon: () => <IconSA data-testid="icon-menu-optimization" id="icon-menu-optimization" className="icon-menu" />,
    subMenuItems: [
      {
        type: MenuType.BID_OPT_SA,
        pathname: '/optimization'
      },
      {
        type: MenuType.RANKMAINTENANCE_SA,
        pathname: '/rank-maintenance'
      },
      {
        type: MenuType.BUDGET_SA,
        pathname: '/budget/SA'
      },
      {
        type: MenuType.COMPETITION_SA,
        pathname: '/competition/SA'
      }
    ]
  },
  {
    type: MenuGroupType.AD_BUDGET,
    icon: () => <IconBudget data-testid="icon-menu-optimization" id="icon-menu-optimization" className="icon-menu" />,
    subMenuItems: [
      {
        type: MenuType.SPEND_PACING,
        pathname: '/spend-pacing'
      },
      {
        type: MenuType.BUDGET_OPT,
        pathname: '/budget-opt'
      },
      {
        type: MenuType.CONTRIBUTION,
        pathname: '/attribution'
      }
    ]
  },
  {
    type: MenuGroupType.REPORT,
    icon: () => <IconReport data-testid="icon-menu-report" id="icon-menu-report" className="icon-menu" />,
    subMenuItems: [
      {
        type: MenuType.CAMPAIGN_REPORT,
        pathname: '/report/campaign'
      },
      {
        type: MenuType.OPTIMIZATION_REPORT,
        pathname: '/report/optimization'
      },
      {
        type: MenuType.RAW_DATA_REPORT,
        pathname: '/report/raw-data'
      },
    ]
  },
  {
    type: MenuGroupType.TOOLS,
    icon: () => (
      <IconTools data-testid="icon-menu-rank-targeting" id="icon-menu-rank-targeting" className="icon-menu" />
    ),
    subMenuItems: [
      {
        type: MenuType.TOOLS_CREATIVE,
        pathname: '/ad-efficiency'
      },
      {
        type: MenuType.ANOMALY_DETECTION_URL,
        pathname: '/report/anomaly-detection-url'
      },
      {
        type: MenuType.ANOMALY_DETECTION_UTM,
        pathname: '/report/anomaly-detection-utm'
      }
    ]
  },
  // {
  //   type: MenuGroupType.API_CENTER,
  //   icon: () => (
  //     <IconApiCenter data-testid="icon-menu-budget-pacing" id="icon-menu-budget-pacing" className="icon-menu" />
  //   ),
  //   pathname: '#'
  // },
  {
    type: MenuGroupType.SETTING,
    icon: () => <IconSetting data-testid="icon-menu-optimization" id="icon-menu-optimization" className="icon-menu" />,
    subMenuItems: [
      {
        type: MenuType.DATA_CONNECT,
        pathname: '/setting/connect-data'
      },
      {
        type: MenuType.SETTING_CIRCLE,
        pathname: '/setting/circle'
      }
      // ,
      // {
      //   type: MenuType.SUBSCRIPTION,
      //   pathname: '/setting/subscription'
      // }
      //   {
      //     type: MenuType.ACCESS_MANAGE,
      //     pathname: '/setting/access-manage',
      //   },
    ]
  }
]

const badgeItems = [
  // { menu: MenuType.TOOLS_SHOP, badgeType: 'close' },
  { menu: MenuType.ANOMALY_DETECTION_SPA, badgeType: 'warning' },
  { menu: MenuType.ANOMALY_DETECTION_URL, badgeType: 'warning' },
  { menu: MenuType.ANOMALY_DETECTION_UTM, badgeType: 'warning' },
  { menu: MenuType.TARGET_BID, badgeType: 'beta' },
  { menu: MenuType.CREATE_CAMPAIGN, badgeType: 'pro' }
]

const MopNav: React.FC = () => {
  const { advertiser, hasAuthority, canAccessMenu } = useAuthority()
  const { openDialog } = useDialog()
  const { hideSidebar } = useLayout()
  const { t } = useTranslation()
  const navigation = useNavigate()
  const location = useLocation()
  const { isEN } = useI18n()

  const [selectedMenuGroup, setSelectedMenuGroup] = useState<MenuGroupType>(MenuGroupType.BUDGET_OPT)
  const [selectedMenu, setSelectedMenu] = useState<MenuType | MenuGroupType>(MenuGroupType.BUDGET_OPT)
  const [openAll, setOpenAll] = useState<boolean>(false)
  const hasSpaAnomaly = useRecoilValue(anomalySpaNoti)
  const hasUtmAnomaly = useRecoilValue(anomalyUtmNoti)
  const hasUrlAnomaly = useRecoilValue(anomalyUrlNoti)

  const findMenuItemByType = (groups: MenuGroupItem[], menuType: MenuType | MenuGroupType) => {
    for (const group of groups) {
      if (group.type === menuType) return group
      if (!group.subMenuItems) continue
      for (const item of group.subMenuItems) {
        if (item.type === menuType) return item
      }
    }
    return null
  }

  const findMenuItemByPathname = (groups: MenuGroupItem[], pathname: string) => {
    for (const group of groups) {
      if (group.pathname === pathname) return group
      if (!group.subMenuItems) continue
      for (const item of group.subMenuItems) {
        if (item.pathname === pathname) return item
      }
    }
    return null
  }

  const handleSelectedMenu = (menuType: MenuType) => {
    const menu = findMenuItemByType(menuGroups, menuType)

    if (menu && menu.pathname) {
      if (menu.pathname ==='/setting/subscription') {
        navigation(menu.pathname+"/"+advertiser.advertiserId, {
          state: {
            advertiserId: advertiser.advertiserId,
            advertiserName: advertiser.advertiserName
          }
        })
      } else {
        navigation(menu.pathname)
      }
    }

    if (menuType === MenuType.TOOLS_SHOP) {
      openDialog({
        actionLabel: t('common.label.button.confirm-k'),
        message: t('layout.label.MopNav.menuClosedMessage.shop'),
        onAction: () => {}
      })
    }
  }

  const handleSelectMenuGroup = (menuGroup: MenuGroupType) => () => {
    const menu = findMenuItemByType(menuGroups, menuGroup)
    if (menu && menu.pathname) {
      if (menu.pathname.startsWith('/login')) {
        window.open(menu.pathname + `&advertiserId=${advertiser.advertiserId}`, '_blank')
        return
      } else if (menu.pathname !== location.pathname) {
        navigation(menu.pathname)
      }
    }
    setSelectedMenuGroup(menuGroup)
  }

  const toggleAllMenuItems = () => {
    setOpenAll(!openAll)
  }

  const renderMenuWithBadge = (badgeItem: { menu: MenuType; badgeType: string }) => {
  const hasBlock = ['shop.targetBid','shop.createCampaign'].includes(badgeItem.menu)

    const hasAnomaly = (menu: MenuType) => {
      switch (menu) {
        case MenuType.ANOMALY_DETECTION_SPA:
          return hasSpaAnomaly
        case MenuType.ANOMALY_DETECTION_URL:
          return hasUrlAnomaly
        case MenuType.ANOMALY_DETECTION_UTM:
          return hasUtmAnomaly
        default:
          return true
      }
    }
    const badge = (badgeType: string) => {
      switch (badgeType) {
        case 'close':
          return <span className="badge-closed">Closed</span>
        case 'beta':
          return <BetaBadge size="sm" className="!bg-primary-black !text-white" />
        case "pro":
          return  <ProBadge size="md" className='!bg-[#CE161D]' />
        case 'warning':
          return (
            <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M8.63397 1.5C9.01887 0.833334 9.98112 0.833333 10.366 1.5L16.8612 12.75C17.2461 13.4167 16.765 14.25 15.9952 14.25H3.00481C2.23501 14.25 1.75388 13.4167 2.13878 12.75L8.63397 1.5Z"
                fill="#EB414C"
              />
              <path
                d="M9.11553 9.984L8.93953 7.032L8.90753 5.92H10.0835L10.0515 7.032L9.88353 9.984H9.11553ZM9.49153 12.112C9.07553 12.112 8.76353 11.784 8.76353 11.344C8.76353 10.904 9.07553 10.584 9.49153 10.584C9.91553 10.584 10.2275 10.904 10.2275 11.344C10.2275 11.784 9.91553 12.112 9.49153 12.112Z"
                fill="white"
              />
            </svg>
          )
      }
    }

    return isEN && hasBlock ? (
      <div>
        {hasAnomaly(badgeItem.menu) && badge(badgeItem.badgeType)}
        {t(`layout.label.MopNav.menu.${badgeItem.menu}`)}
      </div>
    ) : (
      <div className="!flex-row gap-1">
        {t(`layout.label.MopNav.menu.${badgeItem.menu}`)}
        {hasAnomaly(badgeItem.menu) && badge(badgeItem.badgeType)}
      </div>
    )
  }

  const renderListItemText = (item: MenuItem) => {
    const badgeItem = badgeItems.find((badgeItem) => badgeItem.menu === item.type)
    return (
      <ListItemText data-testid={`${item.type}-menuLabel`} className="menuName">
        {item.pathname === '#' && <LockIcon className="lock" />}
        {badgeItem ? (
          renderMenuWithBadge(badgeItem)
        ) : (
          <InnerHtml innerHTML={t(`layout.label.MopNav.menu.${item.type}`)} />
        )}
      </ListItemText>
    )
  }


  const renderMenu = (groups: MenuGroupItem[]) => {
    return groups
      .filter((item) => !item.hidden)
      .map((menuGroup) => (
        <Accordion
          key={menuGroup.type}
          expanded={selectedMenuGroup === menuGroup.type || openAll}
          onChange={handleSelectMenuGroup(menuGroup.type)}
          data-testid={`menu-group-container-${menuGroup.type}`}
          className={selectedMenuGroup === menuGroup.type && !menuGroup.subMenuItems ? 'selected' : ''}
          onClick={() => handleSelectMenuGroup(menuGroup.type)}
        >
          <AccordionSummary aria-controls="panel1bh-content" id="panel1bh-header">
            <Grid container direction="column" className="menu-group" data-testid={`menu-group-${menuGroup.type}`}>
              <div className="menuIcon">{menuGroup.icon()}</div>
              <span>{t(`layout.label.MopNav.menuGroup.${menuGroup.type}`)}</span>
            </Grid>
          </AccordionSummary>
          <AccordionDetails>
            <List className="depth01" component="nav" aria-labelledby="nested-list-subheader">
              {menuGroup.subMenuItems
                ?.filter((item) => !item.hidden && canAccessMenu(item.type))
                .map((item) => {
                  return (
                    <ListItem
                      data-testid={`menu-item-${item.type}`}
                      key={`${item.type}-menu`}
                      onClick={() => handleSelectedMenu(item.type)}
                      className={`menuItem ${selectedMenu === item.type ? 'on' : ''}`}
                    >
                      {renderListItemText(item)}
                    </ListItem>
                  )
                })}
            </List>
          </AccordionDetails>
        </Accordion>
      ))
  }

  useEffect(() => {
    const menu = findMenuItemByPathname(menuGroups, location.pathname)
    const isMenuGroup = menu && menuGroups.find((item) => item.type === menu.type)
    if (menu) {
      setSelectedMenu(menu.type)
    }
    if (isMenuGroup) {
      setSelectedMenuGroup((menu as MenuGroupItem).type)
    }
  }, [location.pathname]) // eslint-disable-line

  useEffect(() => {
    const groups = menuGroups
    const menuGroup = groups.find(
      (menuGroup) => menuGroup.subMenuItems?.find((item) => item.type === selectedMenu) !== undefined
    )
    if (menuGroup) {
      setSelectedMenuGroup(menuGroup.type)
    }
  }, [selectedMenu]) // eslint-disable-line

  if (hideSidebar) {
    return <></>
  }

  return (
    <Box id="MopNav">
      <Paper data-testid="menuNavigation" elevation={0} className="nav">
        <List
          className="depth01"
          component="nav"
          aria-labelledby="nested-list-subheader"
          subheader={
            <ListSubheader component="div" id="nested-list-subheader" className="navTitle" onClick={toggleAllMenuItems}>
              <Grid container direction="column" className="menu-icon-container">
                <IconMenuHeader data-testid="icon-menu-header" id="icon-menu-header" className="icon-menu" />
                <span>{t(`layout.label.MopNav.menu.title`)}</span>
              </Grid>
            </ListSubheader>
          }
        >
          <div id="menu-groups">{renderMenu(menuGroups)}</div>
        </List>
      </Paper>
    </Box>
  )
}

export default MopNav
