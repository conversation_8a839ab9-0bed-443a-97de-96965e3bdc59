{"label": {"currency": "<PERSON><PERSON><PERSON><PERSON>", "media": "Media", "type": "Type", "oauthStatus": "Status", "accountType": {"MEDIA": "Media", "ANALYTICS": "Analytics Tools"}, "placeholder": {"account": "Account ID / Name"}, "button": {"new": "Connect", "naverCommerce": {"linked": "Commerce Account Linking Completed", "notLinked": "Link Commerce Account"}}, "list": {"connect": "Status", "accountType": "Type", "media": "Media", "accountId": "Account ID", "accountName": "Account Name", "currencyCode": "<PERSON><PERSON><PERSON><PERSON>", "metrics": "Conversion Types", "etc": "Notes", "utmOrCommerce": "UTM / Commerce Integration"}}, "etcCode": {"NEED_UNITS": "Unit setting required", "ON_SET": "Connecting", "CAMPAIGN_COLLECT_FAIL": "Connection failed", "INVALID_API_KEY": "API error", "EXCEED_MASTER_REPORT": "Delete master report", "AUTH_REQUESTED": "Invitation pending", "AUTH_INVITED": "Accept invitation", "AUTH_ERROR": "Connection failed", "AUTH_COMPLETED": ""}, "metric": {"selectable": "Available Conversion Metrics", "selected": "Selected Conversion Metrics"}, "toast": {"maxAppsflyer": "You can connect up to 10 apps per Appsflyer account. Please contact support.", "validation": {"apiToken": "Please enter API Token", "appName": "Please enter App Name", "accessLicense": "Please enter Access License", "secretKey": "Please enter Secret Key", "customerId": "Please enter Customer ID", "required": "Please fill in all fields", "accountName": "Please enter Account Name"}, "metrics": {"ga4": {"setEventFail": "You can set up to 10 conversion values excluding required values.", "invalidEvent": "Only conversion values consisting of English, numbers, and _ can be set. Values in other languages cannot be set.", "saveConfirm": "Conversion Event order can only be set during initial connection<br/>and cannot be modified when turning connection On/Off later.<br/>Do you still want to save?"}, "saveConfirm": "Conversion values can only be set once initially,<br/>and cannot be added/deleted/modified later.<br/>Do you want to start connection?"}, "editApi": {"validation": {"apiToken": "Please enter API Token", "appName": "Please enter App Name", "accessLicense": "Please enter Access License", "secretKey": "Please enter Secret Key"}, "response": {"success": "API Key has been updated", "fail": "Failed to update API Key. Please contact system administrator.", "invalidApiKey": "Invalid account. Please enter correct information."}}, "GOOGLE": {"FAIL": "Connection request failed. Please contact system administrator.", "SUCCESS": "Connection request successful."}, "FAIL": "Account connection failed. Please contact system administrator.", "SUCCESS": "Account connection successful.", "ACCOUNT_ID_ALREADY_IN_USE": "This Account ID is already connected. Please contact support.", "DUPLICATE_ACCOUNT": "This account is already registered. Please contact MOP administrator.", "INVALID_API_KEY": "Invalid account. Please enter correct information.", "INVALID_CUSTOMER_ID": "Please verify Account ID and try again. Contact support if issue persists.", "INVALID_ACCOUNT_ID": "Please verify Account ID and try again. Contact support if issue persists.", "CAMPAIGN_COLLECT_ERROR": "Campaign info collection failed due to media error. Will collect automatically within 6 hours.", "EMPTY_MEDIA_EMAIL": "No ad accounts available for connection. Please connect with business account.", "searchEmailSuccess": "Search completed", "searchEmailFail": "Email search failed. Please enter correct email.", "noDataFound": "NO DATA FOUND", "searchFail": "Search failed. Please contact system administrator.", "updateUseYnFail": "Failed to change connection settings. Please contact system administrator.", "exeedNumberEvent": "You can set up to 10 conversion values excluding required values.", "enrollMetricSuccess": "Conversion values have been set.", "DUPLICATE_CLIENT_ID": "This account has already been registered.", "WRONG_SECRET_KEY": "Integration failed. Please try again.", "NAVER_COMMERCE_INTEGRATION_FAIL": "Integration failed. Please try again.", "FORBIDDEN_IP": "Integration failed. Please verify the API call IP in My Store Application."}, "notice": {"NAVER": "<li>Account name is used in MOP, typically enter the account name registered with Naver.</li><li>If API connection info from Naver changes (Account ID, Access License, Secret Key), MOP connection may be interrupted.</li><li>Before requesting connection, ensure Master Report count is under 90. Maintain under 90 Master Reports during MOP usage for stable data connection.</li>", "META": "<li>Please grant permissions to MOP Meta ad account: <EMAIL></li><li>Connection completes after accepting invitation sent to requested account ID.</li>", "GOOGLE": "<li>Enter advertising account ID to connect to MOP (sub-account ID/CID, not MCC ID).</li><li>MOP's Google Ads account (<EMAIL>) will request sub-account setup for entered CID.</li><li>Connection completes after accepting request sent to entered CID.</li>", "CRITEO": "<li>Check that 'Permissions' status is 'Authorized' for ads to connect in Consent Dashboard.</li><li>After Criteo login, select ads to connect to MOP in Portfolio Access and Approve. For connection help, refer to <a href='https://support.mop.co.kr/data_connect'>Criteo connection guide</a></li><li>Currency is set to KRW for MOP connection. For USD, contact <a href='mop(@lgcns.com'>mop(@lgcns.com</a>.</li>", "APPSFLYER": "<li>Appsflyer data connection only supported on Pro plan. Basic/Lite plans allow data connection but AdCircle unit settings not available.</li>", "AIRBRIDGE": "<li>Airbridge data connection only supported on Pro plan. Basic/Lite plans allow data connection but AdCircle unit settings not available.</li>"}, "tooltip": {"selectableMetrics": {"title": "Available Conversion Metrics Guide", "content": {"0": "Shows conversion values connected from each media.", "1": "※ Available conversion values vary by analytics tool and account ID. Consult with support for details.", "2": "Support: <EMAIL>"}}, "conect": {"title": "Connection Guide", "content": {"0": "Cannot toggle connection status because no conversion value has been set.", "1": "Connection is currently OFF and can be turned ON.", "2": "Connection is currently ON and can be turned OFF."}}, "currency": {"title": "Currency Guide", "content": {"0": "Shows currency set for each account ID from media.", "1": "※ MOP sets currency per AdCircle and can only create units when matching account ID currency.", "2": "※ For ads using currencies other than KRW or USD, <span class='error'>Please contact support.</span>", "3": "Media allowing currency settings per account", "4": "Google, Meta", "5": "Support: <EMAIL>"}}, "metrics": {"title": "Conversion Types Setting Guide", "content": {"0": "Data connection and collection begins after setting conversion types per account ID.", "1": "※ Conversion types can only be set once initially and cannot be added, deleted, or modified.", "2": "※ You can set up to 10 conversion types.", "3": "※ One of the set conversion types can be selected as the unit conversion type.", "4": "Cases requiring conversion type settings per account ", "5": "Media: Google, Meta", "6": "Analytics tools: GA4, Airbridge"}}, "utmOrCommerce": {"title": "UTM and Commerce Account Linking Setting Guide", "content": {"0": "UTM Setting​", "1": "If you are using conversions as a performance analysis tool, enter the UTM structure by account ID and then proceed with data linking and collection.", "2": "※ The UTM structure must be set to allow MOP to recognize performance per keyword/creative.", "3": "※ UTM modifications may be needed if the structure is unrecognizable or incorrectly set by MOP rules, as conversion optimization will not be supported.", "4": "Cases requiring conversion type settings per account", "5": "Analytics tools: GA4, Airbridge, Appsflyer", "6": "커머스 계정 연동", "7": "네이버 계정의 경우, 네이버 커머스 계정을 추가적으로 연동하여 추가적인 기능을 이용할 수 있습니다."}}, "etc": {"title": "Notes Message Guide", "content": {"0": {"type1": "Connecting:", "detail1": "Connecting data from media.", "type2": "Connection Failed:", "detail2": "Failed to connect data from media. <span class='error'>(Please contact support)</span>"}, "1": {"title": "For Naver", "type1": "API Error:", "detail1": "Data connection interrupted due to Naver API key error. Please update Access License and Secret Key.", "type2": "Delete Master Report:", "detail2": "Data connection interrupted due to Naver master reports exceeding 100. Please maintain under 90 master reports."}, "2": {"title": "For Google", "type1": "Invitation Pending:", "detail1": "MOP's Google Ads account will send invitation to entered CID.", "type2": "Accept Invitation:", "detail2": "Please accept invitation sent to entered CID."}, "3": "Support: <EMAIL>"}}, "forPro": {"title": "Analytics Tools", "content": ["Airbridge and Appsflyer integration<br/>available on Pro plan only.", "※ Basic and Lite plans allow data connection but<br/>cannot register Ad Circle units."]}, "naverCommerce": {"title": "내 스토어 애플리케이션 등록", "content1": "NAVER 커머스 계정 연동을 위해서는 내 스토어 애플리케이션에 MOP가 등록되어 있어야 합니다. 만약 MOP가 내 스토어 애플리케이션으로 등록되어 있지 않은 경우, 아래안내에 따라 애플리케이션 등록을 먼저 진행해주세요", "step": {"1": "1단계: 네이버 커머스 센터 로그인", "2": "2단계: '내 스토어 애플리케이션'으로 이동", "3": "3단계: '새 애플리케이션 등록' 클릭", "4": "4단계: 'API 호출 IP' 필드에 아래 IP 주소 입력", "5": "5단계: 발급받은 애플리케이션 ID와 시크릿 확인"}}}, "createModal": {"title": "Data Connection"}, "commerceAccountModal": {"title": "Commerce Account", "table": {"title": "Linked commerce account", "accountName": "Account Name", "integratedDate": "Integrated Date", "delete": "Delete"}, "deleteConfirm": "Are you sure you want to delete this account?", "notice": {"title": "커머스 계정 연동 및 취소 시 확인하세요", "content1": "MOP는 업계 표준과 데이터 보안 관련 법률을 준수하며, 모든 데이터는 광고주 단위로 안전하게 보호되고 철저히 관리합니다.​", "content2": "데이터 연동은 사용자의 선택과 책임 하에 이루어지며, 연동된 데이터의 이관이나 삭제는 연동을 설정한 당사자가 직접 관리해야하 합니다."}, "button": {"addIntegration": "Add Integration"}}, "form": {"NAVER-COMMERCE": {"label": {"clientName": "Account Name*", "clientId": "Application ID*", "secretKey": "Application Secret*", "dataSolutionClientId": "Data Solution Application ID*", "dataSolutionSecretKey": "Data Solution Application Secret*"}, "placeholder": {"clientName": "Enter the name you want to manage this account under in MOP", "clientId": "Please enter Application ID", "secretKey": "Please enter Application Secret", "dataSolutionClientId": "Please enter Data Solution Application ID", "dataSolutionSecretKey": "Please enter Data Solution Application Secret"}}}, "title": {"NAVER-COMMERCE": "Naver Commerce Account Linking"}, "path": {"NAVER-COMMERCE": {"message": "추가적인 MOP 기능(MOP 캠페인 생성, 통합 리포트 등)을 이용하시려면, 커머스 계정을 연동해주세요.", "title": "정보 확인 경로", "content": "네이버 커머스 API 센터 <span class='highlight'> 로그인 > 내 스토어 애플리케이션</span>"}}}