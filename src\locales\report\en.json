{"label": {"tab": {"byDate": "By Date", "byMedia": "By Media", "byOpt": "By <PERSON><PERSON>"}, "button": {"metrics": "Metrics", "day": "Day", "days": "Days", "collapse": "Collapse", "expand": "Expand"}, "checkbox": {"variacnce": "Show % Change", "device": "Show by <PERSON><PERSON>", "includeAdData": "Include Negative Keyword/Ad Data"}, "ReportFilter": {"button": {"search": "Apply"}}, "ReportList": {"list": {"currentPeriod": "Report Period", "comparePeriod": "Comparison Period"}}, "ReportFilterName": {"reportPeriod": "Report Period", "comparePeriod": "Comparison Period", "adType": "Ad Type", "mediaType": "Media", "campaigns": "Campaign", "adGroup": "Ad Group", "optimization": "Optimization"}, "KeywordTabs": {"addedExcludedKeywords": "Added Excluded Keywords", "recoveredKeywords": "Recovered Keywords"}}}