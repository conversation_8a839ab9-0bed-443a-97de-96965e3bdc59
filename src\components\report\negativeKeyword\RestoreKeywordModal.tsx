import { MopActionButton } from '@components/common/buttons'
import VirtualizedTable from '@components/common/table/VirtualizedTable'
import KeywordTabs from '@components/report/negativeKeyword/keywordTabs/KeywordTabs'
import { Box, Dialog, DialogActions, DialogContent, DialogTitle, Grid } from '@material-ui/core'
import { useTranslation } from 'react-i18next'

interface Props {
open: boolean
onClose: () => void
}

const RestoreKeywordModal = ({ open, onClose }: Props) => {
  const { t } = useTranslation()
  const columns = [
      {
        key: 'category',
        label: 'Category',
        align: 'center' as const,
        width: 120,
      },
      {
        key: 'date',
        label: 'Date',
        align: 'center' as const,
        width: 120,
      },
      {
        key: 'addedExcludedKeywords',
        label: '# of excluded keywords added',
        align: 'center' as const,
        width: 250,
      },
      {
        key: 'removedExcludedKeywords',
        label: '# of excluded keywords deleted',
        align: 'center' as const,
        width: 250,
      }
  ]
  
  const data = [
    {
      id: '1',
      category: '전체',
      date: '2023-01-01',
      addedExcludedKeywords: 10,
      removedExcludedKeywords: 5
    },
    {
      id: '2',
      category: '쇼핑',
      date: '2023-01-01',
      addedExcludedKeywords: 10,
      removedExcludedKeywords: 5
    },
    {
      id: '3',
      category: '검색',
      date: '2023-01-01',
      addedExcludedKeywords: 10,
      removedExcludedKeywords: 5
    }
  ]
  
const mockAddedExcludedKeywords = [
  {
    id: '1234543234',
    keyword: '뉴발란스 운동화뉴발란화',
    count: 4,
    subItems: []
  },
  {
    id: '1234543235',
    keyword: '뉴발란스 운동화뉴발란스 운동화뉴발란스 운동화',
    count: 4,
    subItems: []
  },
  {
    id: '1234543236',
    keyword: '뉴발란스 운동화뉴발란스 운동화뉴발란스 운동화뉴발란...',
    count: 4,
    subItems: [
      { id: 'sub1', keyword: 'C type 젠더' },
      { id: 'sub2', keyword: 'C type 젠더 C type 젠더' },
      { id: 'sub3', keyword: 'C type 젠더 C type 젠더' },
      { id: 'sub4', keyword: 'C type 젠더' }
    ]
  },
  {
    id: '1234543237',
    keyword: '나이키 에어맥스 운동화',
    count: 3,
    subItems: [
      { id: 'sub5', keyword: '남성용' },
      { id: 'sub6', keyword: '여성용' },
      { id: 'sub7', keyword: '키즈용' }
    ]
  },
  {
    id: '1234543238',
    keyword: '아디다스 스탠스미스',
    count: 2,
    subItems: [
      { id: 'sub8', keyword: '화이트' },
      { id: 'sub9', keyword: '블랙' }
    ]
  },
  {
    id: '1234543239',
    keyword: '뉴발란스 운동화뉴발란스 운동화뉴발란스 운동화뉴발란...',
    count: 4,
    subItems: []
  },
  {
    id: '1234543240',
    keyword: '컨버스 척테일러',
    count: 5,
    subItems: [
      { id: 'sub10', keyword: '하이탑' },
      { id: 'sub11', keyword: '로우탑' },
      { id: 'sub12', keyword: '원스타' }
    ]
  }
  ]
  
  const mockRecoveredKeywords = [
  {
    id: 'rec1',
    keyword: '복구된 키워드 1',
    count: 2
  },
  {
    id: 'rec2',
    keyword: '복구된 키워드 2',
    count: 1
  }
]
  return (

    <Dialog
      PaperProps={{ style: { minWidth: '1500px' } }}
      open={open} onClose={onClose}
    >
      <DialogTitle className='text-center'>
        <h2>제외키워드 추가/삭제 이력</h2>
      </DialogTitle>
      <DialogContent className='p-2'>
        <Grid container spacing={2}>
           <Grid item xs={6}>
              <VirtualizedTable
                  getItemId={(item) => item.id}
                  data={data || []}
                  columns={columns}
                  tableOptions={{
                    width: '740px',
                    rowHeight: 50,
                    headerHeight: 50,
                    stickyHeader: true,
                    className: 'border border-gray-300 rounded-lg'
                  }}
                messageOptions={{
                  emptyMessage: 'No data available',
                  loadingMessage: 'Loading...'
                }}
                isLoading={false}
                />
          </Grid>
          <Grid item md={6}>
            <div className='bg-[#F9FAFB]'>
              <KeywordTabs
                addedExcludedKeywords={mockAddedExcludedKeywords}
                recoveredKeywords={mockRecoveredKeywords}
              />
              <Box className='text-center'>
                <MopActionButton label="복구" />
              </Box>
            </div>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <MopActionButton  label="복구" rounded="half" contained={false} onClick={onClose}/>
      </DialogActions>
    </Dialog>
  )
}

export default RestoreKeywordModal
