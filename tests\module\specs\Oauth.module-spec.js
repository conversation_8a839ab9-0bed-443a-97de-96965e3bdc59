import OauthPage from '@pages/oauth/OauthPage';
import OauthMock from '@mock/OauthMock';
import AdvertiserMock from '@mock/AdvertiserMock';

const page = new OauthPage();
const server = new OauthMock();
const advertiserService = new AdvertiserMock();

const sessionInfo = {
  sessionId: 'test-session-id',
  memberId: 1,
  memberName: '홍길동',
};

describe('설정-데이터 연동 페이지로 이동', () => {
    it.guide('화면이 로드되면 Then 조회필터와 리스트가 표시됨.',{
        mockFunc:()=>{
            server.successWhenGetOAuthList();
            advertiserService.successWhenGetAdvertisers();
        },
        actionFunc:()=>{
            page.fakeSession(sessionInfo);
            page.visit('/setting/connect-data');
        },
        waitFunc:()=>{
            cy.wait('@successWhenGetOAuthList');
        },
        assertFunc:()=>{
            page.assertPageIsVisable();
        }
    })
    it.guide('데이터가 없는 경우 Then 리스트에”데이터가 없습니다."라는 문구가 표시됨',{
        mockFunc:()=>{},
        actionFunc:()=>{},
        waitFunc:()=>{},
        assertFunc:()=>{
            page.assertTableColspanIsNine();
            page.assertTextShowTableIsEmpty();

        }

    })
    it.guide('신규 연동 버튼을 누르면 Then 데이터 연동이 가능한 모달창이 뜬다',{
        mockFunc:()=>{},
        actionFunc:()=>{
            page.clickNewConnectButton();
        },
        waitFunc:()=>{},
        assertFunc:()=>{
            page.assertNewConnectModalIsVisable();
        }
    })
});