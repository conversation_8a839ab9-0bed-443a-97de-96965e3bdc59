import React, { ReactElement, useCallback, useEffect, useState } from 'react'
import { t } from 'i18next'
import { useRecoilState, useResetRecoilState } from 'recoil'
import {
  RankMaintenanceList,
  RankMaintenanceListFilter,
  ShoppingBidSettingModal,
  KeywordRankMonitoringModal
} from '@components/manualBidding'

import { getManualBidList, deleteManualBidItem, updateManualBidMonitoringYn } from '@api/manualBidding'
import TableHeaderRowCount from '@components/common/table/TableHeaderRowCount'
import { Button } from '@material-ui/core'
import AddIcon from '@material-ui/icons/Add'
import { ProBadge, LiteBadge } from '@components/common/BaseChip'

import { GetManualBidListQeury, ManualBidListItem } from '@models/manualBidding'
import { isSuccessResponse, StatusCode } from '@models/common/CommonResponse'
import { targetBidListFilterState, targetBidListState } from '@store/SsRankMaintenance'
import { useAuthority, useDialog, useToast } from '@hooks/common'
import '../rankMaintenance/RankMaintenanceListPage.scss'
import { ActionType } from '@models/common/CommonConstants'
import { FixedLayoutTable, TablePagination, FixedLayoutColumn } from '@components/common/table'
import TargetBidTableFormatter from '@components/manualBidding/TargetBidTableFormatter'
import { YNFlag } from '@models/common'

const RankMaintenanceListPage: React.FC = (): ReactElement => {
  const { advertiser, hasAuthority, getFunctionValue } = useAuthority()
  const { openToast } = useToast()
  const { openDialog } = useDialog()
  const [modalType, setModalType] = useState<ActionType>(ActionType.CREATE)
  const [fnValue, setFnValue] = useState(0)

  const [rmList, setRmList] = useRecoilState(targetBidListState)
  const [rmListActiveFilter, setRmListActiveFilter] = useRecoilState(targetBidListFilterState)
  const [isOpenedSettingModal, setSettingModal] = useState(false)

  const resetRmListActiveFilter = useResetRecoilState(targetBidListFilterState)
  const resetRmList = useResetRecoilState(targetBidListState)

  const [refreshList, setRefreshList] = useState<boolean>(false)
  const [monitoringId, setMonitoringId] = useState<number | null>(null)

  const [initFlag, setInitFlag] = useState<boolean>(true)

  const { isBasicAdvertiser } = useAuthority()

  const handleDetailModalClose = (saveYn: string) => {
    setSettingModal(false)

    if (saveYn === 'Y') {
      setRefreshList(true)
    }
  }

  // NOTE: useRecoilValueLoadable 사용해서 상태관리 해보기

  async function updateBiddingList(param: GetManualBidListQeury) {
    try {
      const response = await getManualBidList(param)
      if (isSuccessResponse(response)) setRmList(response.data)
    } catch (error) {
      openToast(t('common.message.systemError'))
    }
  }

  const ShoppingBidListTable = new TargetBidTableFormatter()
  ShoppingBidListTable.handleDelete = (monitoringId: number) => {
    openDialog({
      message: t('rankMaintenance.message.RankMaintenanceListPage.dialog.deleteAlertMessage'),
      cancelLabel: t('common.label.button.cancel-k'),
      actionLabel: t('common.label.button.delete-k'),
      onAction: async () => {
        try {
          const { successOrNot, statusCode } = await deleteManualBidItem(monitoringId)
          if (successOrNot === YNFlag.Y && statusCode === StatusCode.SUCCESS) {
            openToast(t('common.message.deleteSuccess'))
            const isLastPageIndex =
              rmListActiveFilter.pageIndex > 1 &&
              rmList.totalCount === rmListActiveFilter.pageSize * (rmListActiveFilter.pageIndex - 1) + 1

            if (isLastPageIndex) {
              setRmListActiveFilter({
                ...rmListActiveFilter,
                pageIndex: rmListActiveFilter.pageIndex - 1
              })
            } else {
              updateBiddingList(rmListActiveFilter)
            }
          } else openToast(t('common.message.deleteFail'))
        } catch (error) {
          openToast(t('common.message.systemError'))
        }
      }
    })
  }
  ShoppingBidListTable.handleView = (monitoringId: number) => {
    setMonitoringId(monitoringId)
    setModalType(ActionType.READ)
    setSettingModal(true)
  }
  ShoppingBidListTable.handleEdit = (monitoringId: number) => {
    setMonitoringId(monitoringId)
    setModalType(ActionType.MODIFY)
    setSettingModal(true)
  }
  ShoppingBidListTable.updateBidYn = async (monitoringId: number, checked: boolean) => {
    await updateManualBidMonitoringYn(monitoringId, {
      bidYn: checked ? YNFlag.Y : YNFlag.N
    })
    await updateBiddingList(rmListActiveFilter)
  }
  const handleChangePage = (newPage: number) => {
    if (newPage !== rmListActiveFilter.pageIndex) {
      setRmListActiveFilter({ ...rmListActiveFilter, pageIndex: newPage })
    }
  }

  const handleChangeRowsPerPage = (newRowsPerPage: number) => {
    if (newRowsPerPage !== rmListActiveFilter.pageSize) {
      setRmListActiveFilter({ ...rmListActiveFilter, pageSize: newRowsPerPage, pageIndex: 1 })
    }
  }

  const handleOrderChange = (orderBy: number, orderDirection: 'asc' | 'desc') => {
    setRmListActiveFilter({
      ...rmListActiveFilter,
      orderBy: tableColumns[orderBy]?.field,
      sorting: orderDirection.toUpperCase(),
      pageIndex: 1
    })
  }

  const tableColumns: Array<FixedLayoutColumn<ManualBidListItem>> = ShoppingBidListTable.getColumnFormat(
    rmListActiveFilter.orderBy,
    rmListActiveFilter.sorting
  )

  const openSettingModal = () => {
    if ((rmList.currentCount ?? 0) >= fnValue) {
      openToast(`최대 ${fnValue}개까지 설정하실 수 있습니다`)
      return
    }
    setModalType(ActionType.CREATE)
    setSettingModal(true)
  }

  useEffect(() => {
    let isSubscribed = true
    if (!initFlag && Object.keys(rmListActiveFilter).length !== 0) {
      getManualBidList(rmListActiveFilter).then((response) => {
        if (isSubscribed && isSuccessResponse(response)) {
          setRmList(response.data)
        }
      })
    }

    return () => {
      isSubscribed = false
    }
  }, [rmListActiveFilter]) // eslint-disable-line

  useEffect(() => {
    if (advertiser) {
      const initFilter = {
        advertiserId: advertiser.advertiserId,
        orderBy: 'BID_YN', //FIXME
        sorting: 'DESC',
        pageIndex: 1,
        pageSize: 10
      }
      setRmListActiveFilter(initFilter)
      setFnValue(Number(getFunctionValue('AD_RANK_TARGET_COUNT')))
    }
  }, [advertiser])

  useEffect(() => {
    if (refreshList) {
      updateBiddingList(rmListActiveFilter)
      setRefreshList(false)
    }
  }, [refreshList])

  useEffect(() => {
    setInitFlag(false)
    return () => {
      resetRmListActiveFilter()
      resetRmList()
    }
  }, []) //eslint-disable-line

  return (
    <div id="RankMaintenanceListPage">
      <RankMaintenanceListFilter />
      <div id="RankMaintenanceList">
        <div className="listHeaderWrapper flex items-center gap-1">
          {hasAuthority && (
            <Button
              id="createButton"
              data-testid="createButton"
              variant="contained"
              endIcon={<AddIcon />}
              onClick={openSettingModal}
              disabled={isBasicAdvertiser}
            >
              {t('rankMaintenance.label.RankMaintenanceListPage.list.button.new')}
            </Button>
          )}
          <TableHeaderRowCount current={rmList.currentCount ?? 0} total={fnValue} text={t('rankMaintenance.targetBidding.materialCount')}>
            <>
              <h1>{t('rankMaintenance.targetBidding.materialCount')}</h1>
              <div className="common-style">
                <p style={{ fontWeight: 500 }}>{t('rankMaintenance.targetBidding.title')}</p>
                <div className="!mt-2.5 !py-2.5 align-middle leading-relaxed bg-[#F2F3F6] rounded-sm">
                  <div className="flex flex-col justify-center items-center">
                    <div className="flex justify-start items-center w-[270px]">
                      <ProBadge disabled={false} className="mb-[-3px]" />
                      <p>{t('rankMaintenance.targetBidding.plan.proDescription')}</p>
                    </div>
                  </div>
                  <div className="flex justify-center items-center">
                    <div className="flex justify-start items-center w-[270px]">
                      <LiteBadge disabled={false} className="mb-[-3px]" />
                      <p>{t('rankMaintenance.targetBidding.plan.liteDescription')}</p>
                    </div>
                  </div>
                </div>
              </div>
            </>
          </TableHeaderRowCount>
        </div>
        <FixedLayoutTable
          data-testid="rankMaintenanceTable"
          columns={tableColumns}
          onOrderChange={handleOrderChange}
          data={rmList.ads.map((obj: any) => Object.create(obj) || [])}
          localization={{ body: { emptyDataSourceMessage: t('common.message.list.noData') } }}
        />
        <TablePagination
          id="rank-maintenance-list-pagination"
          totalCount={rmList.totalCount || 0}
          page={rmListActiveFilter.pageIndex || 1}
          rowsPerPage={rmListActiveFilter.pageSize || 10}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          pageOptions={[10, 20, 50]}
        />
      </div>
      {isOpenedSettingModal && (
        <ShoppingBidSettingModal
          open={isOpenedSettingModal}
          modalType={modalType}
          monitoringId={monitoringId}
          onClose={handleDetailModalClose}
          reload={() => setRefreshList(true)}
        />
      )}
    </div>
  )
}

export default RankMaintenanceListPage
