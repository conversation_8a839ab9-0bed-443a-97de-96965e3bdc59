.custom-oauth-link-wrapper {
  background-color: var(--bg-gray-light);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  min-height: 100vh;
  max-width: 100%;

  .custom-oauth-message {
    width: 500px;
  }

  .custom-oauth-link {
    width: 1200px;
    // height: 800px;
    background-color: white;
    box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.4);
    display: flex;
    flex-direction: column;
    padding: 24px 32px;
    color: var(--point_color);
    gap: 20px;

    hr {
      margin: 0;
      width: 100%;
      border: 1px solid var(--gray-light);
    }

    &__header {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 24px;
      padding: 24px 0;
    }
    &__title {
      display: flex;
      align-items: center;
      font-size: 32px;
      gap: 8px;
    }

    &__path {
      display: flex;
      align-items: center;
      padding: 8px 40px;
      border-radius: 9999px;
      background-color: var(--bg-gray-light);
      &-title {
        font-weight: 700;
        padding-right: 4px;
      }

      &-content {
        .highlight {
          color: var(--blue-active);
          font-weight: 700;
        }
      }
    }

    &__content {
      display: flex;
      flex: 1;
      justify-content: space-evenly;
      flex-direction: column;
      align-items: center;
      padding: 32px 0;
      gap: 32px;

      .mop-input-wrapper {
        width: 60%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 16px;

        .mop-input-label {
          width: 20%;
          min-width: 150px;
          font-weight: 700;
          word-break: keep-all;
        }

        .mop-input {
          flex: 1;
          height: 35px !important;
          border-radius: 35px !important;

          .MuiOutlinedInput-input {
            text-align: center;
            padding: 0;
            height: 35px !important;
          }

          .MuiOutlinedInput-notchedOutline {
            border-color: var(--border-input) !important;
            border-radius: 35px !important;
          }

          &:hover .MuiOutlinedInput-notchedOutline,
          &.Mui-focused .MuiOutlinedInput-notchedOutline {
            border-color: var(--blue-active) !important;
          }
        }
      }
    }

    &__notice {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 24px 0;

      &--hidden {
        visibility: hidden;
        padding: 8px 0;
      }

      &-title {
        font-weight: 700;
        display: flex;
        align-items: center;
        gap: 4px;

        .mop-icon-box {
          background-color: var(--bg-gray-light);
          padding: 2px;
          border-radius: 100%;
        }
      }

      &-list {
        width: 70%;
        list-style: disc;
        padding-inline-start: 40px;
        a {
          color: var(--point_color);
          font-weight: 600;
        }
      }
    }
  }

  .integration-guide {
    &::before {
      display: none;
    }
    background-color: var(--bg-gray-light);
    width: 565px;
    box-shadow: none;
    border-radius: 24px !important;

    &.Mui-expanded {
      margin: 0px;
    }

    .MuiAccordionSummary-root {
      min-height: fit-content;
      padding: 0px 32px;

      .MuiAccordionSummary-content {
        margin: 0;
      }

      .custom-oauth-link__path {
        padding: 8px 0px;
      }
    }

    .MuiAccordionDetails-root {
      padding: 0px 32px;
    }

    &__content {
      padding-top: 16px;
      border-top: 2px solid var(--border-input);

      &--step-title {
        font-weight: 600;
        margin-bottom: 6px;
      }

      &--step {
        border-radius: 4px;
        margin: 16px;
        padding: 16px 24px;
        background: var(--color-white);

        p:not(:first-child) {
          margin-left: 12px;
        }
      }

      &--ip-address {
        padding-left: 48px;
      }
    }
  }
}
