/** @type {import('tailwindcss').Config} */

const plugin = require('tailwindcss/plugin')

module.exports = {
  content: ['./src/**/*.{js,jsx,ts,tsx}'],
  theme: {
    extend: {
      colors: {
        primary: {
          black: 'var(--color-black)',
          text: '#333',
          DEFAULT: '#040a45',
          500: '#282F4F',
          400: '#373D69',
          300: '#535972',
          200: '#758BB1'
        },
        active: {
          blue: 'var(--color-blue-lighter)'
        },
        visual: '#272637',
        point: '#040a45',
        error: {
          DEFAULT: 'var(--status-error)',
          light: '#ff4d4f'
        },
        'input-outline': 'var(--border-input)',
        'gray-lighter': 'var(--gray-lighter)',
        'gray-light': 'var(--gray-light)',
        gray: '#909090',
        'gray-dark': 'var(--gray-dark)',
        'blue-active': '#3B5EC9',
        divider: '#D7D8E2',
        intro: {
          'gray-light': '#F2F2F2'
        },
        'landing-black': '#171717',
        'landing-gray': '#555555',
        'landing-red': '#E10106',
        status: {
          active: '#0094ff',
          available: '#56a2a1',
          running: '#56a2a1',
          warning: '#ed6d47',
          error: '#b51b32'
        },
        'slate-lighter': '#f7f7f7',
        campaign: {
          background: '#F9F9FB',
          'border-light': '#EFEFEF'
        }
      },
      fontSize: {
        xxs: '10px',
        header: '50px',
        'sub-header': '40px',
        'mop-base': '15px'
      },
      maxWidth: {
        intro: '1536px'
      },
      height: {
        nav: '90px',
        'mobile-nav': '55px',
        inherit: 'inherit',
        'without-mobile-nav': 'calc(100vh - 55px)',
        'without-nav': 'calc(100vh - 90px)'
      },
      fontFamily: {
        montserrat: ['Montserrat', 'sans-serif'],
        pretendard: ['Pretendard', 'sans-serif'],
        poppins: ['Poppins', 'sans-serif']
      },
      borderRadius: {
        'odd-md': '5px',
        'odd-lg': '10px'
      },
      screens: {
        tablet: '480px',
        labtop: '1024px',
        monitor: '1440px',
        wide: '1920px'
      },
      keyframes: {
        ticker: {
          '0%': { transform: 'translate3d(25%, 0, 0)' },
          '100%': { transform: 'translate3d(-25%, 0, 0)' }
        },
        tickerReverse: {
          '0%': { transform: 'translate3d(-25%, 0, 0)' },
          '100%': { transform: 'translate3d(25%, 0, 0)' }
        }
      },
      animation: {
        ticker: 'ticker 20s linear infinite',
        'ticker-md': 'ticker 30s linear infinite',
        'ticker-lg': 'ticker 40s linear infinite',
        'ticker-reverse': 'tickerReverse 20s linear infinite',
        'ticker-reverse-md': 'tickerReverse 30s linear infinite',
        'ticker-reverse-lg': 'tickerReverse 40s linear infinite'
      }
    }
  },
  plugins: [
    require('@tailwindcss/typography'),
    require('tailwind-scrollbar-hide'),
    plugin(function ({ matchUtilities, theme }) {
      matchUtilities(
        {
          'grid-cols-auto-fill': (value) => ({
            gridTemplateColumns: `repeat(auto-fill, minmax(${value}, 1fr))`
          }),
          'grid-cols-auto-fit': (value) => ({
            gridTemplateColumns: `repeat(auto-fit, minmax(${value}, 1fr))`
          })
        },
        {
          values: theme('spacing', {})
        }
      )
    }),
    plugin(function ({ addVariant }) {
      addVariant('autofill', '&:-webkit-autofill')
    }),
    plugin(function ({ addUtilities }) {
      addUtilities({
        '.scrollbar-custom': {
          '&::-webkit-scrollbar-thumb': {
            width: '8px',
            borderRadius: '8px',
            border: '4px solid transparent',
            backgroundClip: 'padding-box',
            backgroundColor: 'var(--point_color)'
          }
        }
      })
    })
  ]
}
