import React, { ReactElement, useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import tw from 'twin.macro'
import styled from '@emotion/styled'
import {
  OurTechnology,
  Feature,
  Values,
  CoreFunction,
  AgencyPartners,
  AnimatedLandingHero,
  Performance,
  TeamIntroduction,
  SectionInner as Inner
} from '@components/intro'

import { LinkButton } from '@components/common/buttons'
import { ReactComponent as NaverLogo } from '@assets/images/logo-naver-green.svg'
import { ReactComponent as CNSLogo } from '@assets/images/logo-lgcns-white.svg'
import { ReactComponent as Collabo } from '@assets/icon/collaboration-mark.svg'
import { TransText } from '@components/common'
import { useI18n } from '@hooks/common'

const LandingBanner = styled.section`
  ${tw`absolute top-0 left-0 right-0 py-5 bg-[#191919] z-[5] flex-col md:flex-row flex items-center justify-center gap-2.5 md:gap-5`}
`

const LandingPage: React.FC = (): ReactElement => {
  const { isKO } = useI18n()
  const navigation = useNavigate()
  const [conversionEvent, setConversionEvent] = useState<any>(null)

  const onContact = () => {
    setConversionEvent({
      type: 'lead',
      id: '',
      items: [
        {
          name: '도입문의',
          link: 'http://.mop.co.kr/contact'
        }
      ],
      value: 1
    })
    navigation('/contact')
  }

  useEffect(() => {
    // naverPremiumLog
    const wcs = (window as any).wcs
    if (wcs) {
      if (!wcs_add) var wcs_add = {} as any
      wcs_add['wa'] = 's_322dbc64f144'

      conversionEvent && wcs.trans(conversionEvent)
    }
  }, [conversionEvent])

  return (
    <>
      <div className="relative">
        <LandingBanner>
          <span className='flex items-center gap-2.5'>
            <NaverLogo className='w-auto h-[12px] md:[h-19px]' />
            <Collabo className='w-auto h-[15px] md:[h-24px]' />
            <CNSLogo className='w-auto h-[17px] md:[h-19px]' />
          </span>
          <TransText as="span" className="font-smibold text-[17px] md:text-[26px] text-white" i18nKey="landing.banner.text" />
          <LinkButton customStyle={tw`hidden md:inline !px-[18px] !py-2.5 border border-white rounded-full bg-transparent`} href="#">
            <TransText as="a" href="https://news.mt.co.kr/mtview.php?no=2025082808244885436" target="_blank" className="text-[15px] font-bold text-white" i18nKey="landing.common.more" />
          </LinkButton>
        </LandingBanner>
        <section className="overflow-hidden">
          <AnimatedLandingHero setConversionEvent={setConversionEvent} />
        </section>
      </div>
      <section className="bg-[#1D1D1D]">
        <Inner customStyle={tw`py-28`}>
          <OurTechnology />
        </Inner>
      </section>
      <section className="bg-black">
        <Inner customStyle={tw`pt-28 pb-0`}>
          <Feature />
        </Inner>
      </section>
      <section className="bg-[#1D1D1D]">
        <Inner customStyle={tw`py-40`}>
          <Values />
        </Inner>
      </section>
      {/* <section id="key-feature" className="bg-[#F2F2F2]">
        <Inner customStyle={tw`py-[70px] md:py-[140px]`}>
          <CoreFunction />
        </Inner>
      </section> */}
      <Performance />
      <section className="flex flex-col py-10 md:py-20 md:py-28 border-b border-[#D9D9D9]">
        <AgencyPartners />
      </section>
      {/* TODO: 연혁 추가 */}
      {/* <section className='bg-black'>
        <Inner customStyle={tw`text-white py-[100px]`}>
          <div className='flex flex-col items-start gap-6'>
            <span className='text-2xl'>MOP 성장</span>
            <h1 className="text-[64px] font-extralight leading-tight">
              MOP는 <span className='font-semibold'>고객의 성장을 바라보며</span> <br/> 미래를 꿈꿔 왔습니다
            </h1>
            <span className='text-xl'>
            솔루션의 본질, 고객의 성공을 함께 이루어 갑니다</span>
          </div>
        </Inner>
      </section> */}
      <section>
        <TeamIntroduction />
      </section>
      <section className="bg-black bg-[url('assets/images/bg_contact.svg')] bg-center bg-no-repeat">
        <Inner customStyle={tw`h-screen md:h-fit flex flex-col items-center justify-center gap-6 text-center md:py-24`}>
          <TransText
            as="h1"
            className="text-[28px] md:text-[58px] text-white font-extralight leading-tight"
            i18nKey="landing.experience.title"
          />
          <TransText
            as="p"
            className="text-xl text-white opacity-70"
            i18nKey="landing.experience.desc"
          />
          <div className="flex gap-4">
            <TransText
              id="nav-contact"
              as="button"
              className="py-3 px-[25px] md:py-5 md:px-[40px] md:px-[60px] rounded-lg font-bold mt-11 bg-white text-[#171717] text-base md:text-[26px]"
              i18nKey="landing.common.trial"
              onClick={onContact}
            />
            <LinkButton customStyle={tw`mt-11 bg-white text-[#171717] cursor-pointer text-base md:text-[26px]`} href="https://mop.co.kr/register">
              <TransText as="span" className="text-base md:text-2xl" i18nKey="landing.common.startFree" />
            </LinkButton>
            <LinkButton customStyle={tw`mt-11 bg-white text-[#171717] cursor-pointer text-base md:text-[26px]`} href="https://support.mop.co.kr/introduce">
              <TransText as="span" className="text-base md:text-2xl" i18nKey="landing.common.howToUse" />
            </LinkButton>
          </div>
        </Inner>
      </section>
    </>
  )
}

export default LandingPage
