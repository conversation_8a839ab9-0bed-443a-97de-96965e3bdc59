/// <reference types="Cypress" />

import BasePage from '@pages/BasePage';

class OauthPage extends BasePage {
    OAUTH_TABLE = '#connect-oauth-table';
    NEW_CONNECT_BUTTON = '#createButton';

    // CommerceAccountDetailModal selectors
    COMMERCE_ACCOUNT_MODAL = '#CommerceAccountDetailModal';
    COMMERCE_ACCOUNT_MODAL_TITLE = '.commerce-account-modal__title-head';
    COMMERCE_ACCOUNT_MODAL_TABLE = '#commerce-account-modal-table';
    COMMERCE_ACCOUNT_ADD_BUTTON = '.mop-button';
    COMMERCE_DELETE_CANCEL_BUTTON = '[data-qa="close-button"]';
    COMMERCE_DELETE_CONFIRM_BUTTON = '[data-qa="action-button"]';

    assertPageIsVisable(){
        cy.get(this.OAUTH_TABLE).should('be.visible');
    }

    assertOAuthPageLoaded(){
        cy.get(this.OAUTH_TABLE).should('be.visible');
        cy.get('h1').should('be.visible'); // Page title
    }

    clickNewConnectButton(){
        cy.get(this.NEW_CONNECT_BUTTON).click();
    }

    clickCommerceLinkedButton(accountId){
        cy.get(`[data-testid="commerce-linked-${accountId}"]`).click();
    }

    assertNewConnectModalIsVisable(){
        cy.get('.connect-data-modal__title').should('be.visible');
        cy.get('.connect-data-modal__content').should('be.visible');
    }

    assertTableColspanIsNine(){
        cy.get(`${this.OAUTH_TABLE} table td`).should('have.attr', 'colspan', '9');
    }

    assertTextShowTableIsEmpty(){
        cy.get(`${this.OAUTH_TABLE} table tbody tr td`).should('have.text', '데이터가 없습니다.');
    }

    assertCommerceAccountModalIsVisible(){
        cy.get(this.COMMERCE_ACCOUNT_MODAL).should('be.visible');
        cy.get(this.COMMERCE_ACCOUNT_MODAL_TITLE).should('be.visible');
    }

    assertCommerceAccountModalTitle(expectedTitle){
        cy.get(this.COMMERCE_ACCOUNT_MODAL_TITLE).should('contain.text', expectedTitle);
    }

    assertCommerceAccountTableIsVisible(){
        cy.get(this.COMMERCE_ACCOUNT_MODAL_TABLE).should('be.visible');
    }

    assertCommerceAccountAddButtonIsVisible(){
        cy.get(this.COMMERCE_ACCOUNT_ADD_BUTTON).should('be.visible');
    }

    clickCommerceAccountAddButton(){
        cy.get(this.COMMERCE_ACCOUNT_ADD_BUTTON).click();
    }

    assertCommerceAccountTableHasData(expectedRowCount){
        cy.get(`${this.COMMERCE_ACCOUNT_MODAL_TABLE} tbody tr`).should('have.length', expectedRowCount);
    }

    assertCommerceAccountTableColumns(){
        cy.get(`${this.COMMERCE_ACCOUNT_MODAL_TABLE} thead th`).should('have.length', 3);
        cy.get(`${this.COMMERCE_ACCOUNT_MODAL_TABLE} thead th`).each(($th) => {
            cy.wrap($th).should('be.visible').and('not.be.empty');
        });
    }

    clickDeleteIconForAccount(accountUid){
        cy.get(`#delete-${accountUid}`).click();
    }

    assertDeleteConfirmationDialog() {
        cy.get(this.COMMERCE_DELETE_CONFIRM_BUTTON).should('be.visible');
        cy.get(this.COMMERCE_DELETE_CANCEL_BUTTON).should('be.visible');
    }

    clickConfirmDeleteButton(){
        cy.get(this.COMMERCE_DELETE_CONFIRM_BUTTON).click();
    }

    clickCancelDeleteButton(){
        cy.get(this.COMMERCE_DELETE_CANCEL_BUTTON).click();
    }
}

export default OauthPage;