export enum FormFieldCode {
  EMAIL = 'email',
  PASSWORD = 'password',
}

export const pageSizeOptions = [10, 20, 50, 100];
export const chartPageSizeOptions = [10, 20];

export enum FilterSelectType {
  ALL = 'ALL',
  ADMIN_ALL = 'ADMIN_ALL',
}

export enum DateFnsFormat {
  DATE = 'yyyyMMdd',
  DATE_MONTH = 'yyyyMM',
  DISP_DATE = 'yyyy.MM.dd',
  DISP_DATE_MONTH = 'yyyy.MM',
  DAY_OF_WEEK = 'EEEE',
  DAY_OF_WEEK_SHORT = 'EEE',
  DAY_OF_WEEK_INITIAL = 'EEEEE',
  DAY_MONTH_SHORT = 'ddMMM',
  MONTH = 'MMMM',
  MONTH_SHORT = 'MMM',
  DATE_MONTH_DAY_HOUR_MIN = 'yyyy-MM-dd HH:mm:ss',
  DATE_MONTH_DAY_HOUR_MIN_SEC_MSEC = 'yyyy-MM-dd HH:mm:ss.0',
  YEAR_MONTH_DAY_HOUR_MIN = 'yyyy.MM.dd HH:mm',
  YEAR_MONTH = 'yyyy.MM',
  HOUR_MIN = 'HH:mm',
  ISO_DATE = 'yyyy-MM-dd',
}

export enum MemberAttribute {
  USER = 'USER',
  ALL = 'ALL',
}

export const SearchingTextLimitLength = 50;

export const MAX_DATE_VALUE = new Date('9999-12-31');
export const MIN_DATE_VALUE = new Date('1900-01-01');

export enum ActionType {
  CREATE = 'CREATE',
  MODIFY = 'MODIFY',
  READ = 'READ',
  DUPLICATE = 'DUPLICATE',
}

export const MOP_SERVER_IP_ADDRESS = '************'