# Getting Started

## 로컬 환경 구성

로컬 React 환경 구성

### Project Structure

```bash
├─ node_modules/                     # Project dependencies
├─ public/                           # 정적 파일 관리
├─ build/                            # 빌드 결과
├─┬ src
│ ├─ api/                            # Api 호출 모듈
│ ├─ components                      # component 관리
│ ├─ icons                           # icon 이미지 관리
│ ├─ locales                         # 다국어 처리 (default: ko)
│ ├─ models                          # model 관리
│ ├─ pages/                          # page 관리
| ├─ router                          # page router 관리
│ ├─ store                           # recoil로 관리되는 state 관리
│ ├─ utils/                          # 공통 모듈성 Util 관리
│ ├─ App.tsx                         # 최상위 component
│ ├─ index.tsx                       # root element에 App.tsx 컴포넌트를 렌더링
|- .env-cmdrc                        # App 환경변수 관리
|- .eslintrc.yml                     # eslint config 설정
|- .prettierrc.yml                   # prettier config 설정
|- package.json                      # 프로젝트 정보 및 의존성(dependencies)을 관리
|- tsconfig.json                     # typescript config 설정
```

### UI Framework

material design을 사용할 수 있는 https://mui.com/ 를 표준으로 사용

### CI - 소스관리

- GitLab 계정 발급
- GitLab에서 소스코드 clone: git clone https://gitlab.dev.mopapp.net/mop/mop-fe.git

### Prerequisite

- [Install Yarn](https://classic.yarnpkg.com/lang/en/docs/install/)

### Install dependendies

```
yarn install
```

### Start application

```
yarn run start
```

Open http://localhost:3000 in a browser.

### Lint

```
(for MacOS) yarn run lint
(for Windows) yarn run lint:win
```

### CI/CD Pipeline

mop-fe develop branch로 부터 feature branch를 따서 작업 후에 푸시하면 깃랩 파이프라인이 돌면서 실제 웹 자원이 생성됩니다.

파이프라인 성공 여부나 진행 상황은 아래 url 에서 체크 가능합니다.
https://gitlab.dev.mopapp.net/mop/mop-fe/-/pipelines

파이프라인 빌드가 성공하면 아래 url 규칙에 의해 생성된 퍼블릭 FE 페이지를 확인하실 수 있습니다
단, feature branch는 네이밍 룰에 맞게 만들어 주시기 바랍니다. 예시) feature/PP00380210-100-fe-pipeline-init

**배포 환경별 도메인:**
- feature branch: http://pp00380210100.dev.mopapp.net
- PR 생성시: http://mr5.dev.mopapp.net  
- develop branch: https://dev.mopapp.net

#### Pipeline Stages

GitLab CI/CD 파이프라인은 다음 단계들로 구성됩니다:

- **unit-test**: 유닛 테스트 및 린팅 실행
  ```bash
  yarn install
  yarn run lint
  yarn run test:unit:coverage
  ```

- **dev-build**: 개발 환경 빌드 및 S3 배포
  ```bash
  export NODE_OPTIONS="--max-old-space-size=4096"
  yarn run build:dev
  # S3 배포 실행
  ```

- **stg-build**: 스테이징 환경 빌드 및 S3 배포 (master 브랜치만)
  ```bash
  export NODE_OPTIONS="--max-old-space-size=4096"  
  yarn run build:stg
  # S3 배포 실행
  ```

- **prd-build**: 운영 환경 빌드 및 S3 배포 (수동 실행, master 브랜치만)
  ```bash
  export NODE_OPTIONS="--max-old-space-size=4096"
  yarn run build:prd
  # S3 배포 실행
  ```

- **e2e-test**: End-to-End 테스트 실행
  ```bash
  yarn run test:module:default
  ```

### License Management

프로젝트에서 사용하는 의존성 패키지들의 라이센스를 관리하기 위한 스크립트들:

```bash
# 라이센스 요약 정보 확인
yarn run license:check

# 라이센스 목록 조회
yarn run license:list

# 라이센스 정보를 CSV 파일로 출력
yarn run license:csv

# 라이센스 정보를 JSON 파일로 출력
yarn run license:json

# 라이센스 고지문 생성
yarn run license:generate

# 라이센스 고지문 백업
yarn run license:backup

# 금지된 라이센스 검증 (GPL, AGPL, LGPL)
yarn run license:validate

# 백업 파일 정리
yarn run license:cleanup
```
