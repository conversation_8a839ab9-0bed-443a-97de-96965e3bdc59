import { YNFlag, MopMedia } from '@models/common';
import { AdvertiserCurrencyCode } from '@models/common/Advertiser';

export interface CallbackRequest {
  provider: string;
  authorizationCode: string;
  redirectBaseURL: string;
  developerToken: string;
  mccAccountId: string;
  criteoEmail: string;
}

export interface NaverRequest {
  customerId: string;
  customerName: string;
  accessLicense: string;
  secretKey: string;
}

export enum NaverCommerceApplicationType {
  NAVER_COMMERCE = 'NAVER_COMMERCE',
  NAVER_COMMERCE_DATA_SOLUTION = 'NAVER_COMMERCE_DATA_SOLUTION',
}

export interface ConnectOAuthRequest {
  customerId?: string;
  customerName?: string;
  accessLicense?: string;
  secretKey?: string;
  appName?: string;
  apiToken?: string;
  clientId?: string;
  clientName?: string;
  naverAccountId?: string;
  accountName?: string;
  clients?: Array<{
    clientId: string;
    secretKey: string;
    applicationType: NaverCommerceApplicationType;
  }>;
}

export interface OauthAccount {
  media: string;
  accountId: string;
  accountName: string;
  viewId: string;
  viewName: string;
  email: string;
  createdTime: string;
  useYn: string;
  authId: number;
  mediaEmail: string;
}

export interface GoogleConnectRequest {
  mediaLoginEmail: string;
  mccAccountId: string;
}

export interface GoogleAdsRedirectRequest {
  mccAccountId: string;
  developerToken: string;
}

export interface AccountsForAllVendors {
  kakao: boolean;
  google: boolean;
  ga: boolean;
  naver: boolean;
  meta: boolean;
}

export enum AdvertiserType {
  KAKAO = 'kakao',
  GOOGLE_AD = 'google-ad',
  GOOGLE_ANALYTICS = 'google-analytics',
  NAVER = 'naver',
  META = 'meta',
}

export enum OauthMediaType {
  KAKAO = 'kakao',
  GOOGLE = 'google-ad',
  GA = 'google-analytics',
  GA4 = 'google-analytics-4',
  NAVER = 'naver',
  META = 'meta',
  AIRBRIDGE = 'airbridge',
  CRITEO = 'criteo',
  APPSFLYER = 'appsflyer',
}

export interface UpdateOauthAPIParams {
  media: MopMedia;
  apiParams: {
    accessLicense?: string;
    secretKey?: string;
    accountId?: string;
    appName?: string;
    apiToken?: string;
  };
}

// FIXME naming
export interface EditApiParams {
  accountId: string;
  accountName: string;
  media: MopMedia;
}

export enum AccountType {
  MEDIA = 'MEDIA',
  ANALYTICS = 'ANALYTICS',
}

export interface UpdateAccountUseYn {
  accountId: string;
  authId: string;
  viewId?: string;
  propertyId?: string;
  useYn: boolean;
}

export interface Ga4Metric {
  eventId: string;
  propertyId: string;
  eventName: string;
  defaultYn: string;
  selectedYn: string;
}

export interface AnalyticsMetric {
  analyticsType: string;
  platformType: string;
  metricCategory: string;
  metric: string;
  metricAlias: string;
  accountId: string;
  collectYn?: string;
}

export interface MetricItem {
  analyticsType?: MopMedia;
  metricCategory: string; // DEFAULT
  metric?: string;
  metricAlias: string;
}

export interface OauthListItem {
  media: MopMedia;
  accountType: AccountType;
  authId: string;
  accountId: string;
  accountName: string;
  subId: string; // GA - viewId GA4 - propertyId
  subName: string; // GA - viewName GA4 - propertyName
  currencyCode: AdvertiserCurrencyCode;
  platformType?: string;
  mediaEmail: string; // NOTE
  useYn: YNFlag;
  utmRuleYn: YNFlag;
  utmRuleId?: string;
  numberCommerce?: number;
  metrics: MetricItem[];
  etcCodeType: OauthEtcType[];
  requireManualUploadYn?: YNFlag;
}

export enum OauthEtcType {
  NEED_UNITS = 'NEED_UNITS', // 유닛 설정 필요
  INVALID_API_KEY = 'INVALID_API_KEY', // API 에러
  EXCEED_MASTER_REPORT = 'EXCEED_MASTER_REPORT', // 마스터레포트 삭제
  ON_SET = 'ON_SET', // 연동중
  AUTH_REQUESTED = 'AUTH_REQUESTED',
  AUTH_INVITED = 'AUTH_INVITED',
  AUTH_ERROR = 'AUTH_ERROR',
  AUTH_COMPLETED = 'AUTH_COMPLETED',
  MANUAL_UPLOAD_NEEDED = 'MANUAL_UPLOAD_NEEDED',
}

export enum UtmKey {
  AnalyticsTools = 'AnalyticsTools'
}

export interface UtmOrCommerceActions {
  checkField: boolean;
  gtmId?: string;
  set: {
    label: string;
    onClick: () => void;
  };
  unset: {
    label: string;
    onClick: () => void;
  };
}

export interface CommerceAccountInfo {
  accountId: string;
  clientId: string;
  accountName: string;
  applicationType: string;
  integrationDatetime: string;
  accountUid: string;
  grade: string;
}
