import './CustomOAuthLink.scss'
import { useEffect, useState } from 'react'
import { MediaIcon, MopIcon } from '@components/common/icon'
import { MuiInput } from '@components/common/mopUI'
import { MediaType, AnalyticsType, MediaAndCommerce, CommerceMediaType, StatusCode } from '@models/common'
import { useTranslation } from 'react-i18next'
import InnerHtml from '@components/common/InnerHtml'
import { MopActionButton } from '@components/common/buttons'
import { MOPIcon } from '@models/common'
import * as service from '@api/oauth/Oauth'
import { NaverCommerceApplicationType } from '@models/oauth/Oauth'
import { useToast } from '@hooks/common'
import { validateEmail } from '@utils/ValidateUtil'
import { MOP_SERVER_IP_ADDRESS } from '@models/common/CommonConstants'
import { Accordion, AccordionDetails, AccordionSummary } from '@material-ui/core'
import ExpandMoreIcon from '@material-ui/icons/ExpandMore'
import { TransText } from '@components/common'

const AUTH_FORM: { [key in MediaAndCommerce]?: string[] } = {
  [MediaType.NAVER]: ['customerName', 'customerId', 'accessLicense', 'secretKey'],
  [CommerceMediaType.NAVER_COMMERCE]: ['clientName', 'clientId', 'secretKey', 'dataSolutionClientId', 'dataSolutionSecretKey'],
  [AnalyticsType.AIRBRIDGE]: ['appName', 'apiToken'],
  [MediaType.GOOGLE]: ['mediaLoginEmail', 'mediaAccountId'],
  [MediaType.CRITEO]: ['criteoEmail'],
  [AnalyticsType.APPSFLYER]: ['accountName', 'apiToken']
}

type CustomResponse = { type: string; result: string; media: MediaAndCommerce }

const CustomOAuthLink = () => {
  const oauthParams = new URLSearchParams(window.location.search)
  const mediaType = oauthParams.get('type') as MediaAndCommerce
  const customerId = oauthParams.get('customerId')
  const { t, i18n } = useTranslation()
  const hasNotice = i18n.exists(`oauthLink.notice.${mediaType}`)
  const [data, setData] = useState<any>({})
  const { openToast } = useToast()
  const isShowMessage = ([CommerceMediaType.NAVER_COMMERCE] as string[]).includes(mediaType)

  const isInValid = (keys?: string[]) => {
    if (!keys) return true
    return keys.some((key) => !data[key])
  }

  const isSubmitDisabled = isInValid(AUTH_FORM[mediaType])

  const handleSubmit = () => {
    if (AUTH_FORM[mediaType]?.includes('mediaLoginEmail')) {
      const isValidEmail = validateEmail(data.mediaLoginEmail)
      if (!isValidEmail) return openToast(t('login.message.email.format'))
    }
    if (isInValid(AUTH_FORM[mediaType])) return openToast(t('oauthLink.toast.validation.required'))
    requestConnectOAuth(mediaType)
  }

  const getOauthResult = async (mediaType: MediaAndCommerce, data: any) => {
    switch (mediaType) {
      case MediaType.GOOGLE:
        return await service.requestGoogleConnect(mediaType, data)
      case MediaType.CRITEO:
        return await service.oauthLoginURL(mediaType.toLocaleLowerCase(), data)
      default:
        let requestData = { ...data }
        switch (mediaType) {
          case CommerceMediaType.NAVER_COMMERCE: {
            requestData = {
              customerId,
              accountName: data.clientName,
              clients: [
                {
                  clientId: data.clientId,
                  secretKey: data.secretKey,
                  applicationType: NaverCommerceApplicationType.NAVER_COMMERCE
                },
                {
                  clientId: data.dataSolutionClientId,
                  secretKey: data.dataSolutionSecretKey,
                  applicationType: NaverCommerceApplicationType.NAVER_COMMERCE_DATA_SOLUTION
                }
              ]
            }
            break
          }
          default:
            break
        }
        return await service.connectOAuthAccount(mediaType, requestData)
    }
  }

  const getRedirectResponse = async (timerId: number, message: CustomResponse) => {
    if (timerId) clearInterval(timerId)
    const result = document.getElementById('OauthCallbackResult')?.getAttribute('data')
    const openerWindow = window.opener
    message.result = result ?? message.result
    openerWindow.postMessage(message, window.location.origin)
    window.close()
  }

  const requestConnectOAuth = async (mediaType: MediaAndCommerce) => {
    const message: CustomResponse = { type: 'oauth', result: 'FAIL', media: mediaType }
    const result = await getOauthResult(mediaType, data)
    const openerWindow = window.opener
    if (result.successOrNot === 'Y' && mediaType !== MediaType.CRITEO) {
      message.result = 'SUCCESS'
      openerWindow.postMessage(message, window.location.origin)
      window.close()
    } else if (result.successOrNot === 'Y' && mediaType === MediaType.CRITEO) {
      const popup = window.open(`${result.data}&prompt=login`)
      const timerId = ((setInterval(() => popup?.closed && getRedirectResponse(timerId, message), 500) as unknown) ||
        0) as number
    } else {
      if (result.statusCode === StatusCode.INVALID_API_KEY && isShowMessage)
        result.statusCode = StatusCode.NAVER_COMMERCE_INTEGRATION_FAIL

      const hasMessage = i18n.exists(`oauthLink.toast.${result.statusCode}`)
      if (hasMessage) openToast(t(`oauthLink.toast.${result.statusCode}`))
      openerWindow.postMessage(message, window.location.origin)
    }
  }

  const handleChange = (key: string, value: string) => {
    setData((prev: any) => ({ ...prev, [key]: value }))
  }

  const replaceValue = (key: string, value: string) => {
    if (key === 'mediaAccountId') return value.replace(/-/g, '')
    return value
  }

  useEffect(() => {
    window.addEventListener('message', () => {}, false)
    return () => {
      window.addEventListener('message', () => {})
    }
  }, []) // eslint-disable-line

  return (
    <div className="custom-oauth-link-wrapper" id="OauthCallbackResult">
      <div className="custom-oauth-link">
        <section className="custom-oauth-link__header">
          <div className="custom-oauth-link__title">
            <MediaIcon mediaType={mediaType} size={40} />
            <span>{t(`oauthLink.title.${mediaType}`)}</span>
          </div>
          {isShowMessage ? (
            <>
              <p className="custom-oauth-message">{t(`oauthLink.path.${mediaType}.message`)}</p>
              <Accordion className="integration-guide">
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  id="integration-guide-header"
                  IconButtonProps={{ disableRipple: true, size: 'small' }}
                >
                  <div className="custom-oauth-link__path">
                    <span className="custom-oauth-link__path-title">{t(`oauthLink.path.${mediaType}.title`)} |</span>
                    <InnerHtml
                      tagName="span"
                      className="custom-oauth-link__path-content"
                      innerHTML={t(`oauthLink.path.${mediaType}.content`)}
                    />
                  </div>
                </AccordionSummary>
                <AccordionDetails>
                  <div className="integration-guide__content">
                    <TransText as="p" i18nKey="oauthLink.tooltip.naverCommerce.content" />
                    <div className="integration-guide__content--step">
                      <p className="integration-guide__content--step-title">
                        {t('oauthLink.tooltip.naverCommerce.registerMOP.title')}
                      </p>
                      <p>{t('oauthLink.tooltip.naverCommerce.registerMOP.step.1')}</p>
                      <p>{t('oauthLink.tooltip.naverCommerce.registerMOP.step.2')}</p>
                      <p>{t('oauthLink.tooltip.naverCommerce.registerMOP.step.3')}</p>
                      <p>{t('oauthLink.tooltip.naverCommerce.registerMOP.step.4')}</p>
                      <p className="integration-guide__content--ip-address">{MOP_SERVER_IP_ADDRESS}</p>
                      <p>{t('oauthLink.tooltip.naverCommerce.registerMOP.step.5')}</p>
                    </div>

                    <div className="integration-guide__content--step">
                      <p className="integration-guide__content--step-title">
                        {t('oauthLink.tooltip.naverCommerce.registerAPI.title')}
                      </p>
                      <p>{t('oauthLink.tooltip.naverCommerce.registerAPI.step.1')}</p>
                      <p>{t('oauthLink.tooltip.naverCommerce.registerAPI.step.2')}</p>
                      <p>{t('oauthLink.tooltip.naverCommerce.registerAPI.step.3')}</p>
                      <p>{t('oauthLink.tooltip.naverCommerce.registerAPI.step.4')}</p>
                      <p>{t('oauthLink.tooltip.naverCommerce.registerAPI.step.5')}</p>
                    </div>
                  </div>
                </AccordionDetails>
              </Accordion>
            </>
          ) : (
            <div className="custom-oauth-link__path">
              <span className="custom-oauth-link__path-title">{t(`oauthLink.path.${mediaType}.title`)} |</span>
              <InnerHtml
                tagName="span"
                className="custom-oauth-link__path-content"
                innerHTML={t(`oauthLink.path.${mediaType}.content`)}
              />
            </div>
          )}
        </section>
        <hr />
        <section className="custom-oauth-link__content">
          {AUTH_FORM[mediaType]?.map((key: string) => (
            <div key={key} className="mop-input-wrapper">
              <label className="mop-input-label" htmlFor={key}>
                {t(`oauthLink.form.${mediaType}.label.${key}`)}
              </label>
              <MuiInput
                id={key}
                className="mop-input"
                name={key}
                placeholder={t(`oauthLink.form.${mediaType}.placeholder.${key}`)}
                value={data[key] ?? ''}
                inputProps={{ type: key === 'mediaLoginEmail' ? 'email' : 'text' }}
                onChange={(e) => {
                  let nextValue: any = (e.target as HTMLInputElement).value
                  if (nextValue === undefined || nextValue === null) return
                  if (replaceValue) {
                    nextValue = replaceValue(key, nextValue)
                  }
                  if (typeof nextValue === 'string') {
                    nextValue = nextValue.trim()
                  }
                  handleChange(key, nextValue)
                }}
              />
            </div>
          ))}
          <MopActionButton type="submit" label={t(`oauthLink.form.submit.${mediaType}`)} onClick={handleSubmit} disabled={isSubmitDisabled} />
        </section>
        <hr />
        <section
          className={`
              custom-oauth-link__notice
              ${!hasNotice ? 'custom-oauth-link__notice--hidden' : ''}
            `}
        >
          <div className="custom-oauth-link__notice-title">
            <MopIcon name={MOPIcon.NOTICE_TRIANGLE} />
            <span>연동 주의사항</span>
          </div>
          {mediaType === MediaType.CRITEO ? (
            // NOTE & FIXME : a tag target='_blank' 보안이슈로 따로 크리테오만 innerHtml 을 사용하지 않음
            <ul className="custom-oauth-link__notice-list">
              <li>Consent Dashboard에서 연동하고자 하는 광고의 'Permissions'가 'Authorized' 상태인지 확인합니다.</li>
              <li>
                크리테오 로그인 후 Portfolio Access에서 MOP에 연동할 광고 선택 후 Approve 합니다. <br />
                연동에 어려움이 있을 경우,{' '}
                <a href="https://support.mop.co.kr/data_connect" target="_blank" rel="noreferrer">
                  크리테오 연동 방법
                </a>
                을 참고하세요
              </li>
              <li>
                MOP 연동시 통화는 원(KRW)으로 설정됩니다. 달러(USD) 설정이 필요한 경우{' '}
                <a href="mailto:<EMAIL>"><EMAIL></a>으로 문의해주세요.
              </li>
            </ul>
          ) : (
            <InnerHtml
              tagName="ul"
              className="custom-oauth-link__notice-list"
              innerHTML={t(`oauthLink.notice.${mediaType}`)}
            />
          )}
        </section>
      </div>
    </div>
  )
}

export default CustomOAuthLink
