import React, { ReactElement, useState } from 'react'
import { useRecoilState, useSetRecoilState } from 'recoil'
import { MenuItem, OutlinedInput } from '@material-ui/core'
import { CloseRounded } from '@material-ui/icons'
import { RankMaintenanceDetail } from '@models/rankMaintenance/RankMaintenance'
import { getDeviceCodes, getSaMediaCodes } from '@utils/CodeUtil'
import { rankMaintenanceState, rankRegionState } from '@store/RankMaintenance'
import { useToast, useAuthority } from '@hooks/common'
import { t } from 'i18next'
import './RankMaintenanceTarget.scss'
import { getRankMaintenanceList, getKeywordById } from '@api/rankMaintenance/RankMaintenance'
import SelectBottom from '@components/common/SelectBottom'
import { ReactComponent as SearchIcon } from '@components/assets/images/icon_search.svg'
import { SearchingTextLimitLength, ActionType } from '@models/common/CommonConstants'
import { ModalTooltip } from '@components/common/rankMaintenance/RankTooltips'
import { FormField, FormLabel, FormSubLabel, ReadOnlyField } from './BaseComponents'
import { EllipsisText } from '@components/common'

interface Props {
  type: ActionType
}

type SelectChangeEvent = React.ChangeEvent<{
  name?: string
  value: unknown
}>

const RankMaintenanceTarget: React.FC<Props> = ({ type }: Props): ReactElement => {
  const { advertiser } = useAuthority()
  const { openToast, openI18nToast } = useToast()
  const [keywordStatus, setKeywordStatus] = useState<boolean>(false)
  const [rankMaintenanceInfo, setRankMaintenanceInfo] = useRecoilState(rankMaintenanceState)
  const setRegionInfo = useSetRecoilState(rankRegionState)

  const clearKeywordInfo = (opts?: { includeId?: boolean; additionalReset?: { [key: string]: any } }) => {
    const { includeId = true, additionalReset = {} } = opts || {}
    setRankMaintenanceInfo((prev) => ({
      ...prev,
      ...(includeId && { keywordId: '' }),
      keywordName: '',
      campaignName: '',
      adgroupName: '',
      ...additionalReset
    }))
    setKeywordStatus(false)
  }

  const updateTypeValue = ({ target: { name = 'type', value } }: SelectChangeEvent, _child: React.ReactNode) => {
    if (rankMaintenanceInfo.keywordId) openI18nToast('rankMaintenance.toast.resetKeyword')
    clearKeywordInfo({ additionalReset: { [name]: value } })
  }

  async function checkDuplicateKeyword({ advertiserId, keywordId, mediaType }: RankMaintenanceDetail) {
    const response = await getRankMaintenanceList({
      advertiserId,
      keywordNameId: keywordId,
      mediaType,
      pageSize: 10,
      pageIndex: 1
    })

    if (response.totalCount > 0) {
      openI18nToast('rankMaintenance.toast.duplicateKeyword')
      setKeywordStatus(false)
      return true
    }
    return false
  }

  const searchKeyword = async () => {
    const { advertiserId, keywordId, mediaType } = rankMaintenanceInfo
    if (!keywordId) clearKeywordInfo()
    else if (!mediaType) openToast(t('common.message.validation.required', { param: t('common.label.media') }))
    else {
      const isDuplicateKeyword = await checkDuplicateKeyword(rankMaintenanceInfo)
      if (isDuplicateKeyword) return

      const response = await getKeywordById({ keywordId, mediaType, advertiserId })
      if (response) {
        const { keywordName, campaignName = '', adgroupName = '', regionYn, regions } = response
        setRankMaintenanceInfo((prev) => ({ ...prev, keywordName, campaignName, adgroupName, regionYn }))
        setRegionInfo((prev) => ({ ...prev, regionYn, regions }))
        setKeywordStatus(true)
      } else {
        clearKeywordInfo({ includeId: false })
        openI18nToast('rankMaintenance.toast.emptyKeyword')
      }
    }
  }

  const handleOnKeyPress = (event: React.KeyboardEvent<HTMLDivElement>) => {
    if (event.key === 'Enter') {
      event.preventDefault()
      searchKeyword()
    }
  }

  return (
    <div id="RankMaintenanceTarget" className="w-full grid grid-cols-[2fr_3fr] gap-x-6 gap-y-3">
      <FormField id="select-adveriser">
        <FormLabel>
          {t('common.label.advertisement')}
        </FormLabel>
        <ReadOnlyField data-testid="advertiserName" className="!text-opacity-100 !pr-10">
          {advertiser.advertiserName}
        </ReadOnlyField>
      </FormField>
      <FormField id="select-keywordId">
        <FormLabel>
          <ModalTooltip id="keywordId" field="target" />
          <span className="-ml-2">{t('common.label.keywordId')}</span>
        </FormLabel>
        <OutlinedInput
          data-testid="keywordId"
          id="outlined-adornment-weight"
          value={rankMaintenanceInfo.keywordId}
          placeholder={t('common.label.keywordId')}
          onChange={(event) => setRankMaintenanceInfo((prev) => ({ ...prev, keywordId: event.target.value.trim() }))}
          onKeyPress={handleOnKeyPress}
          disabled={type !== ActionType.CREATE}
          endAdornment={
            type !== ActionType.CREATE ? undefined : keywordStatus ? (
              <CloseRounded className="clear-icon" onClick={() => clearKeywordInfo()} />
            ) : (
              <SearchIcon className="search-icon" onClick={searchKeyword} data-testid="searchButton" />
            )
          }
          aria-describedby="outlined-weight-helper-text"
          labelWidth={0}
          readOnly={type === ActionType.CREATE && keywordStatus}
          inputProps={{
            maxLength: SearchingTextLimitLength
          }}
        />
      </FormField>
      <FormField id="select-media">
        <FormLabel>
          <ModalTooltip id="media" field="target" />
          <span className="-ml-2">{t('common.label.media')}</span>
        </FormLabel>
        <SelectBottom
          data-testid="mediaType"
          value={rankMaintenanceInfo.mediaType || ''}
          name="mediaType"
          onChange={updateTypeValue}
          displayEmpty
          disabled={type !== ActionType.CREATE}
          MenuProps={{
            id: 'rank-maintenance-target-select-mediaType',
            anchorOrigin: {
              vertical: 33,
              horizontal: 'left'
            }
          }}
        >
          <MenuItem className="mediaItem" value="">
            {t('common.label.selectOption')}
          </MenuItem>
          {getSaMediaCodes().map((media) => {
            return (
              <MenuItem className="mediaItem" key={media.mediaType} value={media.mediaType}>
                {media.mediaName}
              </MenuItem>
            )
          })}
        </SelectBottom>
      </FormField>
      <FormField id="select-keyword">
        <FormLabel>
          <ModalTooltip id="sa-keyword" field="target" />
          <span className="-ml-2">{t('common.label.keyword')}</span>
        </FormLabel>
        <ReadOnlyField data-testid="keywordName">
          <span className="opacity-40"></span>
          <EllipsisText className="opacity-40">
            {rankMaintenanceInfo.keywordName ? rankMaintenanceInfo.keywordName : t('common.label.keyword')}
          </EllipsisText>
        </ReadOnlyField>
      </FormField>
      <FormField id="select-device">
        <FormLabel>
          <ModalTooltip id="device" field="target" />
          <span className="-ml-2">{t('common.label.device')}</span>
        </FormLabel>
        <SelectBottom
          data-testid="deviceType"
          value={rankMaintenanceInfo.deviceType || ''}
          name="deviceType"
          onChange={updateTypeValue}
          displayEmpty
          disabled={type !== ActionType.CREATE}
          MenuProps={{
            id: 'rank-maintenance-target-select-deviceType',
            anchorOrigin: {
              vertical: 33,
              horizontal: 'left'
            }
          }}
        >
          <MenuItem className="deviceItem" value="">
            {t('common.label.selectOption')}
          </MenuItem>
          {getDeviceCodes().map((device) => {
            return (
              <MenuItem className="deviceItem" key={device.deviceType} value={device.deviceType}>
                {device.deviceName}
              </MenuItem>
            )
          })}
        </SelectBottom>
      </FormField>
      <FormField id="select-campaign-adgroup">
        <FormLabel>
          <ModalTooltip id="sa-adgroup" field="target" />
          <span className="-ml-2">{t('common.label.campaignAdgroup')}</span>
        </FormLabel>
        <ReadOnlyField
          data-testid="campaignAdgroup"
          title={
            rankMaintenanceInfo.campaignName
              ? `${rankMaintenanceInfo.campaignName}/${rankMaintenanceInfo.adgroupName}`
              : ''
          }
        >
          <EllipsisText className="opacity-40">
            {rankMaintenanceInfo.campaignName
              ? `${rankMaintenanceInfo.campaignName}/${rankMaintenanceInfo.adgroupName}`
              : t('common.label.campaignAdgroup')}
          </EllipsisText>
        </ReadOnlyField>
      </FormField>
    </div>
  )
}

export default RankMaintenanceTarget
