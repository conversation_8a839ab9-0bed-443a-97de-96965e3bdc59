import React, { ReactElement, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import SearchOptimizationListFilter from '@components/optimization/SearchOptimizationListFilter';
import {
  deleteSearchOptimization,
  getSearchOptimizations,
  updateSearchOptimizationBidYn,
  getSearchOptimizationNewAdgroup,
} from '@api/optimization/SearchOptimization';
import { Box, Button } from '@material-ui/core';
import { ContextMenuFunctions } from '@models/optimization/SearchOptimization';
import SearchOptimizationList from '@components/optimization/SearchOptimizationList';
import { ActionType } from '@models/common/CommonConstants';
import AddIcon from '@material-ui/icons/Add';
import { useRecoilState, useRecoilValue, useResetRecoilState } from 'recoil';
import { soListActiveFilterState, soListState } from '@store/SearchOptimization';

import './SearchOptimizationListPage.scss';
import SearchOptimizationDetailModal from '@components/optimization/SearchOptimizationDetailModal';
import { StatusCode } from '@models/common/CommonResponse';
import { StatusType } from '@models/optimization/Status';
import { MediaType } from '@models/common/Media';
import { getMediaByAdvertiserId } from '@api/common/Media';
import { PlatformType } from '@models/common/Platform';
import { useAuthority, useToast, useDialog } from "@hooks/common";
import TagManager from 'react-gtm-module'
import { LiteBadge } from '@components/common/BaseChip';
import { ProBadge } from '@components/common/BaseChip';
import TableHeaderRowCount from '@components/common/table/TableHeaderRowCount';
import { TransText } from '@components/common';
import { getFunctionValue } from '@store/Advertiser';

const SearchOptimizationListPage: React.FC = (): ReactElement => {
  const { t } = useTranslation();
  const { advertiser, hasAuthority } = useAuthority();
  const { openToast } = useToast();
  const { openDialog } = useDialog();

  const [soListActiveFilter, setSoListActiveFilter] = useRecoilState(soListActiveFilterState);
  const resetSoListActiveFilter = useResetRecoilState(soListActiveFilterState);
  const [soList, setSoList] = useRecoilState(soListState);
  const resetSoList = useResetRecoilState(soListState);
  const [initFlag, setInitFlag] = useState<boolean>(true);
  const [linkedMedia, setLinkedMedia] = useState<MediaType[]>([]);

  const [optimizationId, setOptimizationId] = useState<number>();
  const [modalType, setModalType] = useState<ActionType>(ActionType.CREATE);
  const [openSearchOptimizationDetail, setOpenSearchOptimizationDetail] = useState<boolean>(false);
  const [refreshList, setRefreshList] = useState<boolean>(false);
  const [deleteOptimization, setDeleteOptimization] = useState<boolean>(false);
  const [hasNewAdgroup, setHasNewAdgroup] = useState<string>('N');

  const totalAdgroupsCount = useRecoilValue(getFunctionValue({ functionType: 'UI', functionId: 'SA_OPT_TARGET_COUNT' })) ?? 50

  const contextMenuFunctions: ContextMenuFunctions = {
    requestEdit: (id: number) => {
      const findOptimization = soList.optimizations.find((optimization) => optimization.optimizationId === Number(id));
      if (findOptimization?.bidYn === 'Y') {
        openToast(t('optimization.message.searchOptimization.toast.impossibleEdit.bidYn'))
      } else if (findOptimization?.status === StatusType.INSPECTING) {
        openToast(t('optimization.message.searchOptimization.toast.impossibleEdit.inspecting'))
      } else {
        setOptimizationId(id);
        setModalType(ActionType.MODIFY);
        setOpenSearchOptimizationDetail(true);
      }
    },
    requestDelete: (id: number) => {
      const findOptimization = soList.optimizations.find((optimization) => optimization.optimizationId === id);
      if (findOptimization?.bidYn === 'Y') {
        openToast(t('optimization.message.searchOptimization.toast.impossibleDelete'))
        return;
      }
      setOptimizationId(id);

      openDialog({
        title: t('common.message.title.notice'),
        message: t('optimization.message.searchOptimization.dialog.deleteAlertMessage'),
        cancelLabel: t('common.label.button.cancel'),
        actionLabel: t('common.label.button.delete'),
        onAction: () => {
          deleteModalOnAction();
        }
      })
    },
    requestBidOnOff: (id: number, onOff: boolean) => {
      updateSearchOptimizationBidYn(id, onOff ? 'Y' : 'N').then((response) => {
        if (response.successOrNot === 'Y' && response.statusCode === StatusCode.SUCCESS) {
          setRefreshList(true);
        } else {
          openToast(t('optimization.message.searchOptimization.toast.updateBidYnFail'))
        }
      });
    },
    requestRead: (id: number) => {
      setOptimizationId(id);
      setModalType(ActionType.READ);
      setOpenSearchOptimizationDetail(true);
    },
    requestDuplicate: (id: number) => {
      setOptimizationId(id);
      setModalType(ActionType.DUPLICATE);
      setOpenSearchOptimizationDetail(true);
    },
  };

  const deleteModalOnAction = () => {
    setDeleteOptimization(true);
  };

  const handleCreateSearchOptimization = () => {
    setModalType(ActionType.CREATE);
    setOpenSearchOptimizationDetail(true);
  };

  const handleDetailModalClose = (saveYn: string) => {
    setOpenSearchOptimizationDetail(false);

    if (saveYn === 'Y') {
      setRefreshList(true);
    }
  };

  useEffect(() => {
    if (deleteOptimization && optimizationId) {
      deleteSearchOptimization(optimizationId).then((response) => {
        if (response.successOrNot === 'Y' && response.statusCode === StatusCode.SUCCESS) {
          openToast(t('optimization.message.searchOptimization.toast.deleteSuccess'))
          if (
            soListActiveFilter.pageIndex > 1 &&
            soList.totalCount === soListActiveFilter.pageSize * (soListActiveFilter.pageIndex - 1) + 1
          ) {
            setSoListActiveFilter({ ...soListActiveFilter, pageIndex: soListActiveFilter.pageIndex - 1 });
          } else {
            soListActiveFilter &&
              getSearchOptimizations(soListActiveFilter).then((optimizations) => {
                setSoList(optimizations)
              })
          }
        } else {
          openToast(t('optimization.message.searchOptimization.toast.deleteFail'))
        }
      });
      setDeleteOptimization(false);
    }

    if (refreshList === true) {
      soListActiveFilter &&
        getSearchOptimizations(soListActiveFilter).then((response) => {
          setSoList(response);
        });
      setRefreshList(false);
    }
  }, [refreshList, deleteOptimization]); // eslint-disable-line

  useEffect(() => {
    let isSubscribed = true;

    if (!initFlag && Object.keys(soListActiveFilter).length !== 0) {
      getSearchOptimizations(soListActiveFilter).then((response) => {
        if (isSubscribed) {
          setSoList(response);
        }
      });
    }
    return () => {
      isSubscribed = false;
    };
  }, [soListActiveFilter]); // eslint-disable-line

  const getLinkedMedia = async () => {
    const response = await getMediaByAdvertiserId(advertiser.advertiserId);
    setLinkedMedia(response?.[PlatformType.SA] || []);
  };

  const getNewAdgroup = async () => {
    const result = await getSearchOptimizationNewAdgroup({advertiserId: advertiser.advertiserId});
    setHasNewAdgroup(result);
  }

  useEffect(() => {
    setInitFlag(false);
    getLinkedMedia();
    getNewAdgroup();
    return () => {
      resetSoListActiveFilter();
      resetSoList();
    };
  }, []); //eslint-disable-line

  return (
    <div id="SearchOptimizationListPage">
      <SearchOptimizationListFilter />
      <Box className="listHeaderWrapper">
        <div className="listLeftWrapper">
          {hasAuthority && (
            <Button
              id="createButton"
              className={hasNewAdgroup === 'Y' ? 'new' : ''}
              data-testid="createButton"
              data-gtm-id="sa-opt-create-click"
              variant="contained"
              onClick={(e) => {
                const gtmEventId = e.currentTarget.dataset.gtmId
                TagManager.dataLayer({
                  dataLayer: {
                    event: 'click',
                    gtm_id: gtmEventId
                  }
                })
                
                if (soList.currentItemsCount === undefined || (soList.maxItemsCount !== -1 && soList.currentItemsCount >= soList.maxItemsCount)) {
                  openDialog({
                    title: t('optimization.message.searchOptimization.dialog.maxItem.title'),
                    actionLabel: t('common.button.confirm'),
                    render: (
                      <>
                        <TransText
                          className="text-left"
                          as="p"
                          i18nKey={'optimization.message.searchOptimization.dialog.maxItem.contents.0'}
                        />
                        <div className="!mt-2.5 !p-2.5 leading-relaxed bg-[#F2F3F6] rounded-sm">
                          <div className="flex flex-col gap-y-2 justify-center items-center w-[360px]">
                            <div className="flex gap-3 justify-start items-center">
                              <ProBadge disabled={false} className="mb-[-3px]" />
                              <TransText
                                className="text-left"
                                as="p"
                                i18nKey={'optimization.message.searchOptimization.dialog.maxItem.contents.1'}
                              />
                            </div>
                            <div className="flex gap-3 justify-start items-center w-[304px]">
                              <LiteBadge disabled={false} className="mb-[-3px]" />
                              <TransText
                                className="text-left"
                                as="p"
                                i18nKey={'optimization.message.searchOptimization.dialog.maxItem.contents.2'}
                              />
                            </div>
                          </div>
                        </div>
                      </>
                    ),
                    onAction: () => {}
                  });
                } else {
                  handleCreateSearchOptimization()
                }
              }}
              color="primary"
              endIcon={<AddIcon />}
            >
              {t('common.label.button.create')}
            </Button>
          )}
          <TableHeaderRowCount
            current={soList.currentAdgroupsCount}
            total={totalAdgroupsCount}
            text={t('optimization.message.searchOptimization.list.numberOfBidOptAdGroups')}
            plan={advertiser.subscriptionProductType}
          >
            <>
              <TransText
                as="h1"
                i18nKey={'optimization.message.searchOptimization.tooltip.title'}
              />
              <div className="common-style">
                <TransText
                  as="p"
                  i18nKey={'optimization.message.searchOptimization.tooltip.contents.0'}
                />
                <div className="!mt-2.5 !py-2.5 align-middle leading-relaxed bg-[#F2F3F6] rounded-sm">
                  <div className="flex flex-col justify-center items-center">
                    <div className="flex justify-start items-center w-[260px]">
                      <ProBadge disabled={false} className="mb-[-3px]" />
                      <TransText
                        as="p"
                        i18nKey={'optimization.message.searchOptimization.tooltip.contents.1'}
                      />
                    </div>
                  </div>
                  <div className="flex justify-center items-center">
                    <div className="flex justify-start items-center w-[260px]">
                      <LiteBadge disabled={false} className="mb-[-3px]" />
                      <TransText
                      as="p"
                      i18nKey={'optimization.message.searchOptimization.tooltip.contents.2'}
                    />
                    </div>
                  </div>
                </div>
              </div>
            </>
        </TableHeaderRowCount>  
        </div>
      </Box>
      <SearchOptimizationList contextMenuFunctions={contextMenuFunctions} />
      {openSearchOptimizationDetail && (
        <SearchOptimizationDetailModal
          open={openSearchOptimizationDetail}
          type={modalType}
          optimizationId={optimizationId}
          linkedMedia={linkedMedia}
          onClose={(saveYn) => handleDetailModalClose(saveYn)}
          remainAdgroupsCount={Number(totalAdgroupsCount) - Number(soList.currentAdgroupsCount)}
        />
      )}
    </div>
  )
};

export default SearchOptimizationListPage;
