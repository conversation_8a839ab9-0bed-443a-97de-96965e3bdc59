import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Tabs, Tab, Box } from '@material-ui/core'
import { ReactComponent as IconClose } from '@components/assets/images/close.svg'
import { ReactComponent as IconArrowElboxDown } from '@components/assets/images/arrow-elbow-down.svg'
import './KeywordTabs.scss'
import TruncatedText from '@components/common/TruncatedText'

interface KeywordItem {
  id: string
  keyword: string
  count?: number
  subItems?: KeywordItem[]
}

interface KeywordTabsProps {
  addedExcludedKeywords?: KeywordItem[]
  recoveredKeywords?: KeywordItem[]
  onRemoveKeyword?: (keywordId: string, tabType: 'excluded' | 'recovered') => void
  onClose?: () => void
}

const KeywordTabs: React.FC<KeywordTabsProps> = ({
  addedExcludedKeywords = [],
  recoveredKeywords = [],
  onRemoveKeyword,
  onClose
}) => {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState(0)
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())

  const handleTabChange = (_event: React.ChangeEvent<{}>, newValue: number) => {
    setActiveTab(newValue)
  }

  const handleRemoveKeyword = (keywordId: string) => {
    const tabType = activeTab === 0 ? 'excluded' : 'recovered'
    onRemoveKeyword?.(keywordId, tabType)
  }

  const toggleExpanded = (keywordId: string) => {
    setExpandedItems((prev) => {
      const newSet = new Set(prev)
      if (newSet.has(keywordId)) {
        newSet.delete(keywordId)
      } else {
        newSet.add(keywordId)
      }
      return newSet
    })
  }

  const renderKeywordItem = (item: KeywordItem, isSubItem = false) => {
    const isExpanded = expandedItems.has(item.id)
    return (
      <div
        key={item.id}
        className={`keyword-item ${isSubItem ? 'sub-item' : ''} ${!isSubItem && isExpanded ? 'bg-[#F2F5FF]' : ''}`}
      >
        <div className="keyword-row">
          <div
            className={`keyword-content cursor-pointer ${isSubItem ? 'flex-row' : 'flex-col'}`}
            onClick={() => toggleExpanded(item.id)}
          >
            {isSubItem && <IconArrowElboxDown />}
            <div className="keyword-text-container">
              <TruncatedText className="keyword-text">{item.keyword}</TruncatedText>
              {item.count && !isSubItem && <div className="keyword-count">{item.count}</div>}
            </div>
            {!isSubItem && <div className="keyword-id">{item.id}</div>}
          </div>
          <button className="remove-button" onClick={() => handleRemoveKeyword(item.id)} aria-label="Remove keyword">
            <IconClose />
          </button>
        </div>
      </div>
    )
  }

  const renderKeywordList = (keywords: KeywordItem[]) => (
    <div className="keyword-list">
      {keywords.map((keyword) => {
        const isExpanded = expandedItems.has(keyword.id)
        const hasSubItems = keyword.subItems && keyword.subItems.length > 0

        return (
          <div key={keyword.id}>
            {renderKeywordItem(keyword)}
            {hasSubItems && isExpanded && (
              <div>
                <div className="sub-items">{keyword.subItems!.map((subItem) => renderKeywordItem(subItem, true))}</div>
              </div>
            )}
          </div>
        )
      })}
    </div>
  )

  return (
    <div className="keyword-tabs max-h-[600px]">
      <div className="tabs-header">
        <Tabs value={activeTab} onChange={handleTabChange} className="keyword-tabs-component">
          <Tab
            label={
              <div className="tab-label">
                {t('report.label.KeywordTabs.addedExcludedKeywords')}
                <span className="tab-count">({addedExcludedKeywords.length})</span>
              </div>
            }
          />
          <Tab
            label={
              <div className="tab-label">
                {t('report.label.KeywordTabs.recoveredKeywords')}
                <span className="tab-count">({recoveredKeywords.length})</span>
              </div>
            }
          />
        </Tabs>
        {onClose && (
          <button className="close-button" onClick={onClose} aria-label="Close">
            <IconClose />
          </button>
        )}
      </div>

      <Box className="tab-content">
        {activeTab === 0 && renderKeywordList(addedExcludedKeywords)}
        {activeTab === 1 && renderKeywordList(recoveredKeywords)}
      </Box>
    </div>
  )
}

export default KeywordTabs
