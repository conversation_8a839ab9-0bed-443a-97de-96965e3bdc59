{"label": {"MopHeader": {"welcome": "Welcome Back, <strong>{{memberName}}</strong>", "logout": "Sign Out", "resetPW": "Reset Password", "emailAlerts": "Email Preferences"}, "MopNav": {"menuGroup": {"tools": "Tools", "budgetOpt": "Budget Opt", "report": "Report", "contribution": "Attribution", "competition": "Competition", "setting": "Setting", "sa": "Search Ad", "saShopping": "Shopping Ad", "adBudget": "Ad Budget", "apiCenter": "API Center"}, "menu": {"title": "MENU", "adminTitle": "ADMIN MENU", "insightTitle": "MENU", "report": {"sa": "Search Ad", "shop": "Shopping Ad", "dva": "Display & Video Ad", "anomalyDetectionUrl": "URL Anomaly Detection", "anomalyDetectionUtm": "UTM Anomaly Detection", "anomalyDetectionSpa": "SPA Anomaly Detection", "anomaly_detection": "Anomaly Detection", "campaign": "Campaign", "optimization": "Optimization", "raw_data": "Raw Data", "createCampaignNaverReport": "Naver Campaign", "negativeKeyword": "Negative Keyword"}, "bid_opt": {"sa": "Search Ad", "shop": "Shopping Ad"}, "tools": {"sa": "Search Ad Rank Target", "shop": "Shopping Ad Rank Target", "creative": "Creative Efficiency"}, "budget_opt": {"dva": "Display & Video Ad"}, "detection": "이상 감지", "analysis": "기여도 분석", "competition": {"sa": "Search Ad", "shop": "Shopping Ad"}, "budget": {"sa": "Search Ad", "shop": "Shopping Ad"}, "setting": {"circle": "Ad Circle", "dataConnect": "Data Connect", "subscription": "Subscription"}, "shop": {"bid_opt": "<PERSON><PERSON>", "anomalyDetectionSpa": "Anomaly Detection", "budget": "Budget Sensitivity", "competition": "Competition", "createCampaign": "Campaign Creation", "targetBid": "Target Bidding", "negativeKeyword": "Negative Keyword"}, "sa": {"bid_opt": "<PERSON><PERSON>", "rankMaintenance": "Ad Rank Target", "budget": "Budget Sensitivity", "competition": "Competition"}, "adBudget": {"spendPacing": "Spend Pacing", "budgetOpt": "Budget Opt", "contribution": "Attribution"}}}, "optimizationAdgroup": {"title": "Campaign/Ad Group", "upload": "Upload", "statusOn": "ON", "statusOff": "OFF", "statusInBidding": "In Bidding", "statusNew": "New", "budgetNoLimit": "No Limit"}}}