import React, { ReactElement, useCallback, useEffect, useState, useMemo } from 'react'
import { useRecoilState } from 'recoil'
import { t } from 'i18next'
import { MenuItem } from '@material-ui/core'
import Selecto from 'react-selecto'
import { cloneDeep, sortBy } from 'lodash'
import { BidSchedule, RankPlan } from '@models/rankMaintenance/RankMaintenance'
import { bidScheduleState, rankIncrementalState, rankMaintenanceState, rankRegionState } from '@store/RankMaintenance'
import { makeHourLable, makeColors } from '@utils/RankMaintenanceUtils'
import { ModalTooltip } from '@components/common/rankMaintenance/RankTooltips'
import { useAuthority, useToast } from '@hooks/common'
import useConstraint from '@hooks/maintenace/useConstraint'
import { FormField, FormLabel } from './BaseComponents'
import {
  BetaBadge,
  ColorDot,
  DecimalNumberFormat,
  MopSwitch,
  MopIcon,
  MopInputWithLabel,
  Radio,
  RoundedSelect,
  TransButton
} from '@components/common'
import { MOPIcon, ButtonUI, ActionType, DayOfWeek, YNFlag } from '@models/common'
import { ConstraintType } from '@models/common/ContraintsByCurrency'
import tw from 'twin.macro'
import styled from '@emotion/styled'
import { cn } from '@utils/index'
import { useTranslation } from 'react-i18next'

interface ListProps {
  editable: boolean
}

const Wrapper = styled.section`
  ${tw`flex flex-col gap-2`};
  .mop-input-wrapper .MuiInputBase-input {
    font-weight: 700;
  }
`
const OptionalWrapper = tw.section`w-full py-3 flex justify-center gap-10 bg-gray-light`
const ScheduleWrapper = tw.section`flex justify-between`
const RankWrapper = tw.div`w-[360px] border-y border-y-primary-black`
const RankSetting = tw.div`grid auto-rows-max gap-4 bg-gray-light px-3 py-5`
const RankListTable = tw.div`w-full flex flex-col`
const RankListItem = styled.div<ListProps>`
  ${tw`grid items-center py-1 text-base min-h-10`};
  ${({ editable }) => (editable ? tw`grid-cols-[1fr_3fr_3fr_1fr]` : tw`grid-cols-[1fr_2fr]`)}
`
const RankListHeader = styled(RankListItem)`
  ${tw`border-b border-b-gray-dark font-medium place-items-center`};
`
const RankListContent = tw.div`scrollbar-custom overflow-y-auto max-h-[260px]`
const RankListLabel = tw.span`flex items-center gap-1`
const ScheduleSetting = tw.div`border-y border-y-primary-black`
const ScheduleLabel = tw.div`w-11 first:w-[78px] h-14 leading-[56px] text-center text-primary-text text-lg`
const ScheduleDate = tw.div`w-[78px] h-[60px] leading-[60px] text-center text-primary-text text-lg bg-gray-light font-bold`
const ScheduleCube = tw.div`w-11 h-[60px] bg-white box-border`

interface Props {
  rankPlan: RankPlan[]
  reset: (callback: () => void) => void
  modalType: ActionType
}

const RankMaintenanceSchedule: React.FC<Props> = ({ rankPlan, reset, modalType }: Props): ReactElement => {
  const { openToast, openI18nToast } = useToast()
  const { hasAuthority } = useAuthority()
  const { getConstraint, checkConstraintValue } = useConstraint()
  const [targetRank, setTargetRank] = useState<number>()
  const [maxCpc, setMaxCpc] = useState<number>()
  const [colors, setColors] = useState<string[]>([])
  const [curIndex, setCurIndex] = useState<number>(-1)
  const [rankMaintenanceInfo, setRankMaintenanceInfo] = useRecoilState(rankMaintenanceState)
  const [bidSchedule, setBidSchedule] = useRecoilState(bidScheduleState)
  const [regionInfo, setRegionInfo] = useRecoilState(rankRegionState)
  const [incremental, setIncremental] = useRecoilState(rankIncrementalState)
  const { i18n } = useTranslation();

  const isEditable = useMemo(() => hasAuthority && modalType !== ActionType.READ, [hasAuthority])
  const rankOptions = useMemo(() => [...Array(10).keys()], [])

  const updateSetting = (name: string, value: string) => {
    const convertedVal = value ? Number(value) : 0
    if (name === 'maxCpc') setMaxCpc(convertedVal)
    if (name === 'incremental') {
      setIncremental((prev) => ({ ...prev, incrementalValue: convertedVal }))
      setRankMaintenanceInfo((prev) => ({ ...prev, incrementalValue: convertedVal }))
    }
  }

  const createRankChip = async () => {
    const isValid = await checkValidChip()
    if (isValid && targetRank && maxCpc) {
      const rankPlans = bidSchedule.rankPlan.concat([{ targetRank, maxCpc }])
      setColors(makeColors(rankPlans, colors))
      setBidSchedule((prev) => ({ ...prev, rankPlan: rankPlans }))
    }
  }

  const handleDeleteChip = (index: number) => {
    const newBidSchedule = cloneDeep(bidSchedule)
    newBidSchedule.rankPlan.splice(index, 1)

    Object.keys(newBidSchedule).forEach((key) => {
      if (key !== 'rankPlan') {
        newBidSchedule[key as keyof BidSchedule].forEach((el, inx) => {
          if (el === index) {
            newBidSchedule[key as keyof BidSchedule][inx] = -1
          } else if ((el as number) > index) {
            newBidSchedule[key as keyof BidSchedule][inx] = (el as number) - 1
          }
        })
      }
    })

    const newColors = colors.slice()
    newColors.splice(index, 1)
    setColors(newColors)
    setBidSchedule(newBidSchedule)
    setCurIndex(-1)
  }

  const checkValidChip = async () => {
    const isValidValue = await checkConstraintValue(maxCpc ?? 0, ConstraintType.RANK_MAX_CPC)
    if (!isValidValue) return false
    const isExitingRank = bidSchedule.rankPlan.filter(
      (plan) => plan.maxCpc === maxCpc && plan.targetRank === targetRank
    ).length
    if (!targetRank || isExitingRank) {
      openI18nToast(`rankMaintenance.toast.${isExitingRank ? 'duplicateChip' : 'emptyTargetRank'}`)
      return false
    }
    return true
  }

  const handleInit = () => {
    setColors(makeColors(rankPlan))
    setBidSchedule(rankMaintenanceInfo.bidSchedule)
  }

  const resetCallback = useCallback((): void => {
    handleInit()
  }, [])

  useEffect(() => {
    reset && reset(resetCallback)
  }, [reset, resetCallback])

  useEffect(() => {
    setColors(makeColors(rankPlan))
  }, [rankPlan])
  const getOrdinalSuffix= (n: number): React.ReactElement => {
    if(i18n.language ==="ko"){
      return <span>순위</span>
    }
    const lastTwo = n % 100;
    const lastOne = n % 10;

    if (lastTwo >= 11 && lastTwo <= 13) {
      return <sup>th</sup>;
    }

    switch (lastOne) {
      case 1: return <sup>st</sup>;
      case 2: return <sup>nd</sup>;
      case 3: return <sup>rd</sup>;
      default: return <sup>th</sup>;
    }
  }
  return (
    <Wrapper>
      <OptionalWrapper>
        <FormField className="gap-4">
          <FormLabel>
            <ModalTooltip id="region" field="target" />
            <span className="-ml-2 text-sm">{t('common.label.region')}</span>
          </FormLabel>
          <RoundedSelect
            options={sortBy(regionInfo.regions).map((regionItem, index) => ({
              label: regionItem.dictionaryCodeName,
              value: regionItem.dictionaryCode
            }))}
            value={regionInfo.regionCode ?? ''}
            isDisabled={!isEditable || regionInfo.regionYn === YNFlag.N}
            updateValue={(newValue) => {
              const target = regionInfo.regions.find((item) => item.dictionaryCode === newValue)
              setRegionInfo((prev) => ({
                ...prev,
                regionCode: newValue,
                regionName: target.dictionaryCodeName
              }))
              setRankMaintenanceInfo({
                ...rankMaintenanceInfo,
                regionCode: newValue as string,
                regionName: target.dictionaryCodeName
              })
            }}
          >
            <MenuItem value="">
              <div className="rounded-select-item text-xs">{t('common.label.selectOption')}</div>
            </MenuItem>
          </RoundedSelect>
        </FormField>
        <FormField className="gap-4">
          <FormLabel>
            <ModalTooltip id="incremental" field="target" />
            <span className="-ml-2 text-sm">{t('common.label.customIncrememtal')}</span>
            <BetaBadge size="sm" />
            <MopSwitch
              checked={incremental.incrementalUseYn === YNFlag.Y}
              onChange={(value) => {
                const incrementalUseYn = value ? YNFlag.Y : YNFlag.N
                setIncremental((prev) => ({ ...prev, incrementalUseYn }))
                setRankMaintenanceInfo((prev) => ({ ...prev, incrementalUseYn }))
              }}
              disabled={!isEditable}
            />
          </FormLabel>
          <MopInputWithLabel
            name="incremental"
            handleChange={updateSetting}
            autoComplete="off"
            originValue={incremental.incrementalValue}
            disabled={!isEditable || incremental.incrementalUseYn === YNFlag.N}
            inputComponent={DecimalNumberFormat}
            hasFocus={false}
          />
        </FormField>
      </OptionalWrapper>
      <ScheduleWrapper>
        <RankWrapper>
          <RankSetting>
            <FormField>
              <FormLabel>
                <ModalTooltip id="rank" field="target" />
                <span className="-ml-2 text-base">{t('common.label.rank')}</span>
              </FormLabel>
              <RoundedSelect
                data-testid="targetRank"
                options={rankOptions.map((index) => ({
                  label: index + 1,
                  value: index + 1
                }))}
                fontSize={16}
                value={targetRank || ''}
                isDisabled={!isEditable}
                updateValue={(newValue) => setTargetRank(newValue)}
              >
                <MenuItem key="emptry-target-rank" value="">
                  <div className="rounded-select-item">-</div>
                </MenuItem>
              </RoundedSelect>
            </FormField>
            <FormField>
              <FormLabel>
                <ModalTooltip id="maxBid" field="target" options={getConstraint(ConstraintType.RANK_MAX_CPC, true)} />
                <span className="-ml-2 text-base">{t('common.label.maxCpcbid')}</span>
              </FormLabel>
              <MopInputWithLabel
                name="maxCpc"
                handleChange={updateSetting}
                autoComplete="off"
                disabled={!isEditable}
                inputComponent={DecimalNumberFormat}
                hasFocus={false}
              />
            </FormField>
            {isEditable && (
              <TransButton
                data-testid="createPlanButton"
                className="px-5 text-xs rounded-full place-self-center"
                ui={ButtonUI.Contained}
                onClick={createRankChip}
                i18nKey="common.button.create-e"
              />
            )}
          </RankSetting>
          <RankListTable>
            <RankListHeader editable={isEditable}>
              {isEditable && <span>{t('common.label.select')}</span>}
              <span>{t('common.label.rank')}</span>
              <span>{t('common.label.maxCpcbid')}</span>
              {isEditable && <span>{t('common.label.delete')}</span>}
            </RankListHeader>

            <RankListContent data-testid="rankPlanList">
              {isEditable && (
                <RankListItem editable className="border-b border-b-gray-lighter place-items-center">
                  <Radio
                    size={20}
                    checked={curIndex === -1}
                    onChange={() => setCurIndex(-1)}
                    value={-1}
                    name="radio-chip"
                  />

                  <RankListLabel className="col-span-3">
                    <MopIcon aria-label="erager" name={MOPIcon.ERAGER} size={24} />
                    {t('rankMaintenance.label.RankMaintenanceSchedule.eraser')}
                  </RankListLabel>
                </RankListItem>
              )}
              {bidSchedule.rankPlan.map((plan, index) => (
                <RankListItem
                  key={`chip-list-${index}`}
                  editable={isEditable}
                  className="border-b border-b-gray-lighter font-light place-items-center"
                >
                  {isEditable && (
                    <Radio
                      size={20}
                      checked={curIndex === index}
                      onChange={() => setCurIndex(index)}
                      value={index}
                      name={`radio-chip-${index}`}
                    />
                  )}
                  <RankListLabel data-testid="chip-list-item-rank" className="justify-center">
                    <ColorDot color={colors[index]} size={16} customStyle={tw`rounded-sm`} />
                    <strong>{`${plan.targetRank}`}</strong> {getOrdinalSuffix(plan.targetRank)}
                  </RankListLabel>
                  <RankListLabel data-testid="chip-list-item-cpc">
                    <p className="w-full text-end">
                      <strong>{`${Number(plan.maxCpc).toLocaleString('ko-KR')}`}</strong> 원
                    </p>
                  </RankListLabel>
                  {isEditable && (
                    <MopIcon
                      aria-label="delete"
                      name={MOPIcon.DELETE}
                      customClass="cursor-pointer"
                      size={20}
                      onClick={() => handleDeleteChip(index)}
                    />
                  )}
                </RankListItem>
              ))}
            </RankListContent>
          </RankListTable>
        </RankWrapper>

        <ScheduleSetting>
          {isEditable && (
            <Selecto
              dragContainer={'.schedule-container'}
              selectableTargets={['.schedule-cube']}
              hitRate={100}
              selectByClick={false}
              selectFromInside={true}
              ratio={0}
              onSelectEnd={(e) => {
                const newBidSchedule = cloneDeep(bidSchedule)
                e.afterAdded.forEach((el) => {
                  const key = el.getAttribute('data-key')
                  if (key) {
                    const day = key.split('-')[0]
                    const time = Number(key.split('-')[1])
                    newBidSchedule[day as keyof BidSchedule][time] = curIndex
                  }
                })
                setBidSchedule(newBidSchedule)
              }}
            ></Selecto>
          )}
          <div className="schedule-container">
            <div className="flex bg-gray-light border-b border-b-primary-black">
              {[...Array(25).keys()].map((index) => {
                return (
                  <ScheduleLabel
                    key={`schedule-time-${index}`}
                    className={cn('text-center font-normal -indent-11', (index - 1) % 12 === 0 && 'font-bold')}
                  >
                    {index > 0 ? makeHourLable(index - 1) : ''}
                  </ScheduleLabel>
                )
              })}
            </div>
            {Object.keys(DayOfWeek).map((el) => {
              return (
                <div key={`week-${el}`} className="flex">
                  <ScheduleDate>{t(`common.code.week.${el}`)}</ScheduleDate>
                  {bidSchedule[el as keyof BidSchedule].map((planIndex, timeIndex) => {
                    if (isNaN(Number(planIndex))) return <></>
                    const bgColor = planIndex === -1 ? '#fff' : colors[planIndex as number]
                    return (
                      <ScheduleCube
                        key={`schedule-${el}-${timeIndex}`}
                        data-key={`${el}-${timeIndex}`}
                        style={{ backgroundColor: bgColor }}
                        className={cn(
                          'schedule-cube border-l border-b border-[#90a6b9]',
                          el === 'SUN' && 'border-b-0',
                          timeIndex === 12 && 'border-l-primary-black'
                        )}
                      />
                    )
                  })}
                </div>
              )
            })}
          </div>
        </ScheduleSetting>
      </ScheduleWrapper>
    </Wrapper>
  )
}

export default RankMaintenanceSchedule
