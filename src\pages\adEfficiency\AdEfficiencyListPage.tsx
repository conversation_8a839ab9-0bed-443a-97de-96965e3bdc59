import React, { useState, useEffect } from 'react';
import { Grid, FormLabel, MenuItem, Box, Button } from '@material-ui/core';
import { useTranslation } from 'react-i18next';
import SelectBottom from '@components/common/SelectBottom';
import AdEfficiencyTableFormatter from '@components/adEfficiency/AdEfficiencyTableFormatter';
import AdEfficiencyResultOffHistoryModal from '@components/adEfficiency/AdEfficiencyResultOffHistoryModal'
import { FixedLayoutTable, TablePagination } from '@components/common/table'
import { SelectDropdownIcon } from '@components/common/icon'
import { ContainedCreateButton } from '@components/common/buttons'
import { MopSearch } from '@components/common'
import {
  getAdEfficiencies,
  createAdEfficiency,
  deleteAdEfficiency,
  copyAdEfficiency,
  getAdEfficiencyOffHistory,
} from '@api/adEfficiency/AdEfficiency';
import {
  AdEfficiencyListItem,
  AdEfficiencyStatus,
  AdEfficiencyAnalysisOffHistoryItem,
} from '@models/adEfficiency/AdEfficiency';
import './AdEfficiencyListPage.scss';
import { pageSizeOptions } from '@models/common/CommonConstants';
import { useAuthority, useDialog, useToast } from "@hooks/common";
import { ReactComponent as RankIcon } from '@components/assets/images/icon_rank.svg';

const adEfficiencyTableFormatter = new AdEfficiencyTableFormatter();

const AdEfficiencyListPage: React.FC = () => {
  const { t } = useTranslation();
  const { advertiser, hasAuthority } = useAuthority();
  const { openToast } = useToast();
  const { openDialog, openFormDialog } = useDialog();
  const allColumns: any[] = adEfficiencyTableFormatter.getColumnFormat();
  const [analyses, setAnalyses] = useState<AdEfficiencyListItem[]>([]);
  const [searchQuery, setSearchQuery] = useState('')
  const [searchText, setSearchText] = useState('')
  const [statusFilter, setStatusFilter] = useState('ALL');
  const [recommendLevelFilter, setRecommendLevelFilter] = useState('ALL')
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(pageSizeOptions[0]);
  const [open, setOpen] = useState(false)
  const [historyData, setHistoryData] = useState<AdEfficiencyAnalysisOffHistoryItem[]>([])

  const recommendLevel = (analysis: AdEfficiencyListItem) => {
    // const changeRate = analysis.budgetChangeRate * 0.01
    // const improveRate = analysis.performImproveRate
    // const lowRange = 0 < improveRate &&  improveRate < (0.5 * changeRate)
    // const range = (0.5 * changeRate) < improveRate && improveRate < changeRate
    // const highRange = changeRate < improveRate
    // if (lowRange) return 'LOW'
    // if (range) return 'NORMAL'
    // if (highRange) return 'HIGH'
    return ''
  }
  const filteredItmes = analyses
    .filter(analysis => statusFilter === 'ALL' || statusFilter === analysis.status)
    .filter(analysis => recommendLevelFilter === 'ALL' || recommendLevelFilter === recommendLevel(analysis))
    .filter(analysis => searchText === '' || analysis.analysisName.toLowerCase().includes(searchText.toLowerCase()))

  const changeFilter = (event: any) => {
    if (event.target.name === 'statusFilter') {
      setStatusFilter(event.target.value)
    } else if (event.target.name === 'recommendLevelFilter'){
      setRecommendLevelFilter(event.target.value)
    }
  };

  const handleSearch = () => {
    setSearchText(searchQuery)
  }

  const getAnalysisList = async() => {
    const data = await getAdEfficiencies(advertiser.advertiserId)
    setAnalyses(data)
  }

  const handleCreate = async() => {
    openFormDialog<{inputValue: string}>({
      title: '분석명 입력',
      inputOptions: {
        placeholder: '분석명을 입력해 주세요.'
      },
      onAction: async ({ inputValue: analysisName }) => {
        if (!analysisName.trim()) return
        await createAdEfficiency({
          advertiserId: advertiser.advertiserId,
          analysisName,
        })
        getAnalysisList()
      }
    })
  }

  const handleDelete = async(id: number) => {
    openDialog({
      message: t('common.message.deleteConfirm'),
      cancelLabel: t('common.label.button.no'),
      actionLabel: t('common.label.button.delete'),
      onAction: async () => {
        const result = await deleteAdEfficiency(id)
        openToast(t(`optimization.label.budgetOpt.deleteOpt.${result ? 'success' : 'failed'}`))
        if (result) {
          const isLastPageIndex = page > 1 && filteredItmes.length === rowsPerPage * (page - 1) + 1
          if (isLastPageIndex) {
            setPage((prev) => prev - 1)
          }
          getAnalysisList()
        }
      },
    })
  }

  const handleCopy = async(id: number) => {
    openDialog({
      message: t('common.message.copyConfirm'),
      cancelLabel: t('common.label.button.no'),
      actionLabel: t('common.label.button.copy'),
      onAction: async () => {
        const result = await copyAdEfficiency(id)
        openToast(result ? t('common.message.copySuccess') : t('common.message.copyFail'))
        if(result) {
          getAnalysisList()
        }
      },
    })
  }

  const openHistoryModal = async() => {
    const result = await getAdEfficiencyOffHistory(advertiser.advertiserId)
    setHistoryData(result)
    setOpen(true)
  }

  adEfficiencyTableFormatter.handleDelete = handleDelete
  adEfficiencyTableFormatter.handleCopy = handleCopy

  useEffect(() => {
    getAnalysisList()
  }, []) //eslint-disable-line

  return (
    <div id="AdEfficiencyListPage">
      <Grid container justifyContent="center" className="budget-label-container">
        <Box className="budget-filter-label">
          <FormLabel>{t('common.label.filter.status')}</FormLabel>
        </Box>
      </Grid>
      <Grid container justifyContent="center" className="budget-select-container">
        <Box className="budget-filter-select">
          <SelectBottom
            name="statusFilter"
            value={statusFilter}
            onChange={changeFilter}
            displayEmpty
            MenuProps={{
              className: 'outlined-filter-popover',
              anchorOrigin: { vertical: 30, horizontal: 'left' }
            }}
            IconComponent={(props) => <SelectDropdownIcon {...props} />}
          >
            <MenuItem value={'ALL'}>{t('common.label.filter.all')}</MenuItem>
            {Object.keys(AdEfficiencyStatus).map((status) => {
              return (
                <MenuItem key={status} value={status}>
                  {t(`adEfficiency.label.adEfficiencyOpt.list.status.${status}`)}
                </MenuItem>
              )
            })}
          </SelectBottom>
        </Box>
      </Grid>
      <Grid container justifyContent="space-between" className="budget-opt-list-header">
        <div>
          { hasAuthority &&
            <ContainedCreateButton label={t(`adEfficiency.label.adEfficiencyList.button.create`)} onClick={() => handleCreate()} />
          }
          <Button
            id="historyButton"
            color="primary"
            onClick={openHistoryModal}
            endIcon={<RankIcon />}
          >{t(`adEfficiency.label.adEfficiencyList.button.offHistory`)}</Button>
        </div>
        <div className='budget-filter-search'>
          <MopSearch
            placeholder={t('adEfficiency.label.adEfficiencyList.placeholder.analysisName')}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onSearch={handleSearch}
          />
        </div>
      </Grid>
      <FixedLayoutTable
        id="adEfficiencyTable"
        columns={allColumns}
        data={filteredItmes.slice((page - 1) * rowsPerPage, page * rowsPerPage)}
        localization={{ body: { emptyDataSourceMessage: t('common.message.list.noData') } }}
      />
      {analyses.length > 0 && (
        <TablePagination
          totalCount={filteredItmes.length}
          page={page}
          rowsPerPage={rowsPerPage}
          onPageChange={(page) => setPage(page)}
          onRowsPerPageChange={(rowsPage) => setRowsPerPage(rowsPage)}
        />
      )}
      <AdEfficiencyResultOffHistoryModal
        open={open}
        onClose={() => setOpen(false)}
        data={historyData}
      />
    </div>
  )
}

export default AdEfficiencyListPage