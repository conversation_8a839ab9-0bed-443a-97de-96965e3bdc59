import './OauthListTableFormatter.scss'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'
import { OauthListItem, OauthMediaType, OauthEtcType, UtmKey, UtmOrCommerceActions } from '@models/oauth/Oauth'
import { Tooltip } from '@material-ui/core'
import { TableTitle, FixedLayoutColumn } from '@components/common/table'
import { BaseChip } from '@components/common'
import { TextIcon, MediaIcon, MopIcon } from '@components/common/icon'
import { MopSwitch } from '@components/common/mopUI'
import { ArrowRightButton } from '@components/common/buttons'
import CommonTooltip from '@components/common/CommonTooltip'
import InnerHtml from '@components/common/InnerHtml'
import { ReactComponent as AdviceMarkIcon } from '@components/assets/images/advicemark.svg'
import { AnalyticsType, MOPIcon, YNFlag, MopMedia, MediaType, COMMERCE_PATH_CONNECTION } from '@models/common'
import * as service from '@api/oauth/Oauth'
import { useToast } from '@hooks/common'
import { isAnalytics } from '@utils/common/MediaType'
import SelectedMetricsList from '@components/common/SelectedMetricsList'
import TagManager from 'react-gtm-module'

type TableRow = FixedLayoutColumn<OauthListItem>

const subLabelMedias: MopMedia[] = [AnalyticsType.GA, AnalyticsType.GA4]

export default class TableFormatter {
  constructor() {
    this.field = ''
    this.order = 'asc'
  }
  field: string
  order: 'asc' | 'desc'
  requestUpdateList() {}
  handleAuthModal(_advertiserId: number) {}
  openEditApiModal(_params: any) {}
  openCreateMetricsModal(_params: any) {}
  openCommerceAccountModal(_params: any) {}
  getColumnFormat = ({ canSettingUTM }: { canSettingUTM: boolean }): Array<TableRow> => {
    const { openToast } = useToast()
    const { t } = useTranslation()
    const navigate = useNavigate()

    const columnYn = (): TableRow => {
      return {
        title: (
          <TableTitle titleStr={t('oauthLink.label.list.connect')}>
            <CommonTooltip
              id={`connect-info-tooltip`}
              title={
                <>
                  <h1>{t('oauthLink.tooltip.conect.title')}</h1>
                  <ul>
                    <li className="bid-desc">
                      <div className="bid-img bid-disable"></div>
                      <div>{t('oauthLink.tooltip.conect.content.0')}</div>
                    </li>
                    <li className="bid-desc">
                      <div className="bid-img bid-off"></div>
                      <div>{t('oauthLink.tooltip.conect.content.1')}</div>
                    </li>
                    <li className="bid-desc">
                      <div className="bid-img bid-on"></div>
                      <div>{t('oauthLink.tooltip.conect.content.2')}</div>
                    </li>
                  </ul>
                </>
              }
              placement="right-start"
              arrow
            >
              <AdviceMarkIcon className="tooltip-icon" />
            </CommonTooltip>
          </TableTitle>
        ),
        align: 'center',
        sorting: false,
        cellStyle: { width: '7%' },
        render: (rowData) => {
          const { accountId, authId, subId, media, metrics, utmRuleId, utmRuleYn } = rowData
          const isNeedMetric = metrics.length === 0
          const isNeedUTM = utmRuleYn === YNFlag.Y && !utmRuleId
          const isDisabled = isNeedMetric || isNeedUTM
          const oauthMedia = OauthMediaType[media as keyof typeof OauthMediaType]

          const switchYn = async (useYn: boolean) => {
            if (media === AnalyticsType.APPSFLYER && rowData.useYn === YNFlag.N && utmRuleId && !canSettingUTM) {
              openToast(t('oauthLink.toast.maxAppsflyer'))
              return
            }
            await service.updateAccountUseYn(oauthMedia, {
              accountId,
              authId,
              ...(media === AnalyticsType.GA && { viewId: subId, propertyId: '' }),
              ...(media === AnalyticsType.GA4 && { viewId: '', propertyId: subId }),
              ...(media === AnalyticsType.APPSFLYER && { appId: subId }),
              useYn
            })
            this.requestUpdateList()
          }
          const showMetricToast = () => {
            if (isDisabled) {
              openToast(isAnalytics(media) ? '전환값과 UTM을 먼저 설정해주세요' : '전환값을 먼저 설정해주세요')
            }
          }
          return (
            <div className="cell-body-box" onClick={showMetricToast}>
              <MopSwitch
                checked={rowData.useYn === YNFlag.Y}
                onChange={() => {
                  switchYn(rowData.useYn === YNFlag.N)
                  TagManager.dataLayer({
                    dataLayer: {
                      event: 'click',
                      gtm_id: 'data-connect-useYn',
                      use_yn: rowData.useYn === YNFlag.Y ? 'Y' : 'N',
                      account_id: accountId,
                      media_type: media
                    }
                  })
                }}
                disabled={isDisabled}
              />
            </div>
          )
        }
      }
    }

    const columnType = (): TableRow => {
      return {
        title: <TableTitle titleStr={t('oauthLink.label.list.accountType')} />,
        field: 'accountType',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'accountType' ? this.order : undefined,
        cellStyle: {
          width: '7%',
          minWidth: 120
        },
        render: (rowData) => {
          return (
            <div className={`cell-body-box accountType--${rowData.accountType}`}>
              <BaseChip rounded="sm" size="lg">
                {t(`oauthLink.label.accountType.${rowData.accountType}`)}
              </BaseChip>
            </div>
          )
        }
      }
    }

    const columnMedia = (): TableRow => {
      return {
        title: <TableTitle titleStr={t('oauthLink.label.list.media')} />,
        field: 'media',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'media' ? this.order : undefined,
        cellStyle: { width: '7%' },
        render: (rowData) => {
          return (
            <div className="cell-body-box">
              <MediaIcon mediaType={rowData.media} size={28} />
            </div>
          )
        }
      }
    }

    const columnAccountId = (): TableRow => {
      return {
        title: <TableTitle titleStr={t('oauthLink.label.list.accountId')} />,
        field: 'accountId',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'accountId' ? this.order : undefined,
        cellStyle: { width: '15%' },
        render: ({ accountId, media, subId }) => {
          const hasSub = subLabelMedias.includes(media)
          const subNameLabel = (media: any) => {
            if (media === AnalyticsType.GA4) return 'Property ID'
            if (media === AnalyticsType.GA) return 'View ID'
            return ''
          }
          return (
            <div className="cell-body-box has-sub-label">
              {media === AnalyticsType.APPSFLYER ? subId : accountId}
              {hasSub && (
                <BaseChip size="sm" bgColor="#F2F3F6">
                  {`${subNameLabel(media)}: `}
                  <strong>{subId}</strong>
                </BaseChip>
              )}
            </div>
          )
        }
      }
    }

    const columnAccountName = (): TableRow => {
      return {
        title: <TableTitle titleStr={t('oauthLink.label.list.accountName')} />,
        field: 'accountName',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'accountName' ? this.order : undefined,
        cellStyle: { width: '20%', minWidth: 120 },
        render: ({ media, accountName, subId, subName, platformType }) => {
          const hasSub = subLabelMedias.includes(media)
          const showPlatform = media === AnalyticsType.APPSFLYER && platformType
          const subNameLabel = (media: any) => {
            if (media === AnalyticsType.GA4) return 'Property Name'
            if (media === AnalyticsType.GA) return 'View Name'
            return ''
          }
          return (
            <div className="cell-body-box has-sub-label">
              {media === AnalyticsType.APPSFLYER ? subName : accountName}
              {hasSub && (
                <BaseChip size="sm" bgColor="#F2F3F6">
                  <span title={subName}>
                    {`${subNameLabel(media)}: `} <strong>{subName}</strong>
                  </span>
                </BaseChip>
              )}
              {showPlatform && (
                <BaseChip size="sm" bgColor="#F2F3F6">
                  <span title={platformType}>
                    OS: <strong>{platformType}</strong>
                  </span>
                </BaseChip>
              )}
            </div>
          )
        }
      }
    }

    const columnCurrency = (): TableRow => {
      return {
        title: (
          <TableTitle
            titleStr={t('oauthLink.label.list.currencyCode')}
            children={
              <CommonTooltip
                id={`currency-info-tooltip`}
                title={
                  <>
                    <h1>{t('oauthLink.tooltip.currency.title')}</h1>
                    <div className="common-style">
                      <p>{t('oauthLink.tooltip.currency.content.0')}</p>
                      <p className="indent2">{t('oauthLink.tooltip.currency.content.1')}</p>
                      <p className="indent2">
                        <InnerHtml innerHTML={t('oauthLink.tooltip.currency.content.2')} />
                      </p>
                      <h2>{t('oauthLink.tooltip.currency.content.3')}</h2>
                      <ul className="indent2">
                        <li>{t('oauthLink.tooltip.currency.content.4')}</li>
                      </ul>
                      <p>{t('oauthLink.tooltip.currency.content.5')}</p>
                    </div>
                  </>
                }
                placement="right-start"
                arrow
              >
                <AdviceMarkIcon className="tooltip-icon" />
              </CommonTooltip>
            }
          />
        ),
        field: 'currencyCode',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'currencyCode' ? this.order : undefined,
        cellStyle: { width: '5%' },
        render: (rowData) => {
          return (
            <div className="cell-body-box">
              <TextIcon code={rowData.currencyCode} size={28} />
            </div>
          )
        }
      }
    }

    const columnMetrics = (): TableRow => {
      return {
        title: (
          <TableTitle
            titleStr={t('oauthLink.label.list.metrics')}
            children={
              <CommonTooltip
                id={`metrics-info-tooltip`}
                title={
                  <>
                    <h1>{t('oauthLink.tooltip.metrics.title')}</h1>
                    <div className="common-style">
                      <p>{t('oauthLink.tooltip.metrics.content.0')}</p>
                      <p className="indent2">{t('oauthLink.tooltip.metrics.content.1')}</p>
                      <p className="indent2">{t('oauthLink.tooltip.metrics.content.2')}</p>
                      <p className="indent2">{t('oauthLink.tooltip.metrics.content.3')}</p>
                      <h2>{t('oauthLink.tooltip.metrics.content.4')}</h2>
                      <ul className="indent2">
                        <li>{t('oauthLink.tooltip.metrics.content.5')}</li>
                        <li>{t('oauthLink.tooltip.metrics.content.6')}</li>
                      </ul>
                    </div>
                  </>
                }
                placement="right-start"
                arrow
              >
                <AdviceMarkIcon className="tooltip-icon" />
              </CommonTooltip>
            }
          />
        ),
        field: 'metrics',
        align: 'center',
        sorting: false,
        cellStyle: { width: '15%' },
        render: (rowData) => {
          const metricsNum = rowData.metrics.length
          const needMetrics = metricsNum === 0
          const hasMore = metricsNum > 1

          return (
            <div className="cell-body-box metrics">
              {needMetrics ? (
                <ArrowRightButton
                  contained={false}
                  label="전환값 설정하기"
                  onClick={() => this.openCreateMetricsModal(rowData)}
                  gtmId="data-connect-metrics-setting"
                />
              ) : (
                <Tooltip
                  id="selected-metrics-tooltip"
                  title={<SelectedMetricsList metrics={rowData.metrics} />}
                  placement="bottom"
                >
                  <div>
                    <BaseChip size="lg" bgColor="#F2F3F6">
                      {rowData.metrics[0].metricAlias}
                    </BaseChip>
                    {hasMore && <span>+ {metricsNum - 1}</span>}
                  </div>
                </Tooltip>
              )}
            </div>
          )
        }
      }
    }

    const columnUTMOrCommerce = (): TableRow => {
      return {
        title: (
          <TableTitle
            titleStr={t('oauthLink.label.list.utmOrCommerce')}
            children={
              <CommonTooltip
                id={`metrics-info-tooltip`}
                title={
                  <>
                    <h1>{t('oauthLink.tooltip.utmOrCommerce.title')}</h1>
                    <div className="common-style">
                      <h2>{t('oauthLink.tooltip.utmOrCommerce.content.0')}</h2>
                      <p>{t('oauthLink.tooltip.utmOrCommerce.content.1')}</p>
                      <p className="indent2">{t('oauthLink.tooltip.utmOrCommerce.content.2')}</p>
                      <p className="indent2">{t('oauthLink.tooltip.utmOrCommerce.content.3')}</p>
                      <h2>{t('oauthLink.tooltip.utmOrCommerce.content.4')}</h2>
                      <ul className="indent2">
                        <li>{t('oauthLink.tooltip.utmOrCommerce.content.5')}</li>
                      </ul>
                      <h2>{t('oauthLink.tooltip.utmOrCommerce.content.6')}</h2>
                      <p>{t('oauthLink.tooltip.utmOrCommerce.content.7')}</p>
                    </div>
                  </>
                }
                placement="right-start"
                arrow
              >
                <AdviceMarkIcon className="tooltip-icon" />
              </CommonTooltip>
            }
          />
        ),
        field: 'utmOrCommerce',
        align: 'center',
        sorting: false,
        cellStyle: { width: '13%' },
        render: ({ media, subId, accountId, utmRuleYn, utmRuleId, numberCommerce }) => {
          const subIdPath = media === AnalyticsType.AIRBRIDGE ? accountId : subId
          const routeSettingUTM = () => {
            navigate(`../utm-rule/${subIdPath}`, {
              state: { media, subId: subIdPath, accountId, utmRuleId }
            })
          }
          const openSettingNaverCommerce = () => {
            window.open(
              `${window.location.origin}/oauth-link?type=${COMMERCE_PATH_CONNECTION[media]}&customerId=${accountId}`,
              '_blank'
            )
          }
          const openCommerceAccountModal = () => {
            this.openCommerceAccountModal({ customerId: accountId, media })
          }
          const renderSettingButton = (action?: UtmOrCommerceActions) => {
            if (!action) return <></>

            const { set, unset, checkField, gtmId } = action
            return (
              <div className="cell-body-box">
                {checkField ? (
                  <button
                    className="utm-rule-setting-button"
                    onClick={set.onClick}
                    data-testid={`commerce-linked-${accountId}`}
                  >
                    {set.label}
                    <MopIcon name={MOPIcon.EDIT} />
                  </button>
                ) : (
                  <ArrowRightButton contained={false} label={unset.label} onClick={unset.onClick} gtmId={gtmId} />
                )}
              </div>
            )
          }
          const ACTION_BUTTONS: { [Key in UtmKey | MopMedia]?: UtmOrCommerceActions } = {
            [UtmKey.AnalyticsTools]: {
              checkField: !!utmRuleId,
              gtmId: 'data-connect-utm-setting',
              set: {
                label: 'UTM 설정완료',
                onClick: routeSettingUTM
              },
              unset: {
                label: 'UTM 설정하기',
                onClick: routeSettingUTM
              }
            },
            [MediaType.NAVER]: {
              checkField: !!numberCommerce,
              set: {
                label: t('oauthLink.label.button.naverCommerce.linked'),
                onClick: openCommerceAccountModal
              },
              unset: {
                label: t('oauthLink.label.button.naverCommerce.notLinked'),
                onClick: openSettingNaverCommerce
              }
            }
            //When you need to add commerce settings for Google, Meta... just declare more here
          }

          return renderSettingButton(ACTION_BUTTONS[utmRuleYn === YNFlag.Y ? UtmKey.AnalyticsTools : media])
        }
      }
    }

    const columnETC = (): TableRow => {
      const routeCreateCircle = (code: OauthEtcType) => {
        if (code !== OauthEtcType.NEED_UNITS) return
        navigate('../circle')
      }
      const openAPIModal = ({ accountName, accountId, media }: OauthListItem) => {
        this.openEditApiModal({ accountName, accountId, media })
      }
      return {
        title: (
          <TableTitle
            titleStr={t('oauthLink.label.list.etc')}
            children={
              <CommonTooltip
                id={`etc-info-tooltip`}
                title={
                  <>
                    <h1>{t('oauthLink.tooltip.etc.title')}</h1>
                    <div className="common-style">
                      <dl>
                        <dt>{t('oauthLink.tooltip.etc.content.0.type1')}</dt>
                        <dd>{t('oauthLink.tooltip.etc.content.0.detail1')}</dd>
                        <dt>{t('oauthLink.tooltip.etc.content.0.type2')}</dt>
                        <dd>
                          <InnerHtml innerHTML={t('oauthLink.tooltip.etc.content.0.detail2')} />
                        </dd>
                      </dl>
                      <h2 className="indent1 mb-1">{t('oauthLink.tooltip.etc.content.1.title')}</h2>
                      <dl className="40_60">
                        <dt>{t('oauthLink.tooltip.etc.content.1.type1')}</dt>
                        <dd>{t('oauthLink.tooltip.etc.content.1.detail1')}</dd>
                        <dt>{t('oauthLink.tooltip.etc.content.1.type2')}</dt>
                        <dd>{t('oauthLink.tooltip.etc.content.1.detail2')}</dd>
                      </dl>
                      <h2 className="indent1 mb-1">{t('oauthLink.tooltip.etc.content.2.title')}</h2>
                      <dl>
                        <dt>{t('oauthLink.tooltip.etc.content.2.type1')}</dt>
                        <dd>{t('oauthLink.tooltip.etc.content.2.detail1')}</dd>
                        <dt>{t('oauthLink.tooltip.etc.content.2.type2')}</dt>
                        <dd>{t('oauthLink.tooltip.etc.content.2.detail2')}</dd>
                      </dl>
                      <p>{t('oauthLink.tooltip.etc.content.3')}</p>
                    </div>
                  </>
                }
                placement="right-start"
                arrow
              >
                <AdviceMarkIcon className="tooltip-icon" />
              </CommonTooltip>
            }
          />
        ),
        field: 'etc',
        align: 'center',
        sorting: false,
        cellStyle: {
          width: '10%'
        },
        render: (rowData) => {
          const hideCode = rowData.useYn === YNFlag.N
          const needUpload = rowData.requireManualUploadYn === YNFlag.Y
          const routeUpload = () => {
            window.open('https://tally.so/r/3EPDdN', '_blank', 'noreferrer')
          }
          if (needUpload) {
            return (
              <div className="cell-body-box">
                <ArrowRightButton contained={false} label="리포트 업로드" onClick={routeUpload} />
              </div>
            )
          }
          return (
            <div className="cell-body-box">
              {!hideCode &&
                rowData.etcCodeType.map((etc) => (
                  <>
                    <span className={`oauth-status oauth-status--${etc}`} onClick={() => routeCreateCircle(etc)}>
                      {t(`oauthLink.etcCode.${etc}`)}
                    </span>
                    {etc === OauthEtcType.INVALID_API_KEY && (
                      <MopIcon name={MOPIcon.EDIT} onClick={() => openAPIModal(rowData)} />
                    )}
                  </>
                ))}
            </div>
          )
        }
      }
    }

    return [
      columnYn(),
      columnType(),
      columnMedia(),
      columnAccountId(),
      columnAccountName(),
      columnCurrency(),
      columnMetrics(),
      columnUTMOrCommerce(),
      columnETC()
    ]
  }
}
