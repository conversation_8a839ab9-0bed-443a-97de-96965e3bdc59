import React from 'react';
import './ShoppingOptimizationListFormatter.scss';
import { useTranslation } from 'react-i18next';
import {
  ContextMenuFunctions,
  ShoppingOptimizationInfo,
  ShoppingOptimizationList,
  ShoppingOptimizationListColumn,
} from '@models/shoppingOptimization/ShoppingOptimization';
import Switch from '@material-ui/core/Switch';
import Tooltip from '@material-ui/core/Tooltip';
import { withStyles } from '@material-ui/core/styles';
import { Chip } from '@material-ui/core';

import { Status, StatusType } from '@models/optimization/Status';
import { getSaStatusCodes, getErrorStatusCodes, getMediaCodes, getOptimizationGoalCodes, getSaShoppingCodes } from '@utils/CodeUtil';
import { Media } from '@models/common/Media';
import { OptimizationGoal } from '@models/optimization/OptimizationGoal';
import { ErrorStatus, ErrorStatusType } from '@models/optimization/ErrorStatus';
import { ReactComponent as DeleteIcon } from '@components/assets/images/icon_delete.svg';
import { ReactComponent as EditIcon } from '@components/assets/images/icon_edit.svg';
import { ReactComponent as DuplicateIcon } from '@components/assets/images/icon_duplicate.svg';
import { ReactComponent as BudgetOverflowIcon } from '@components/assets/images/budgetOverflow.svg';
import { ReactComponent as BudgetShortageIcon } from '@components/assets/images/budgetShortage.svg';
import { ReactComponent as ErrorIcon } from '@components/assets/images/warning_red.svg';
import { ReactComponent as OptimizationErrorIcon } from '@components/assets/images/optimizationError_red.svg';
import { ReactComponent as AdviceMarkIcon } from '@components/assets/images/advicemark.svg';
import { ReactComponent as OptimizationResultIcon } from '@components/assets/images/icon_optimization_result.svg';
import { ReactComponent as NegativeKeywordIcon } from '@components/assets/images/icon_negative_keyword.svg';
import { ReactComponent as OptimizationHistoryIcon } from '@components/assets/images/icon_optimization_history.svg';
import { advertiserState } from '@store/Advertiser';
import { useRecoilValue } from 'recoil';
import { AuthorityType } from '@models/common/Advertiser';
import { checkAuthority } from '../../utils/AuthorityUtil'
import { FixedLayoutColumn } from '@components/common/table'
import InnerHtml from '@components/common/InnerHtml';
import { compareDate, currentDate, convertFormatToDate } from '@utils/DateUtil';
import { toCamelCase } from '@utils/StringUtil';
import { SaShoppingStatus } from '@models/shoppingOptimization/ShoppingOptimization';
import { DateFnsFormat } from '@models/common/CommonConstants';
import { format } from 'date-fns';
import TagManager from 'react-gtm-module'
import { useAuthority } from '@hooks/common';

export default class ShoppingOptimizationListFormatter {
  getColumnFormat = (
    contextMenuFunctions?: ContextMenuFunctions,
    orderBy?: string | undefined,
    sorting?: string | undefined,
    ssList?: ShoppingOptimizationList,
  ): Array<FixedLayoutColumn<ShoppingOptimizationInfo>> => {
    const { t } = useTranslation();
    const advertiser = useRecoilValue(advertiserState);
    const { isProAdvertiser } = useAuthority();

    const getMediaName = (mediaCode: string) => {
      const mediaCodes: Media[] = getMediaCodes();

      for (const item of mediaCodes) {
        if (item.mediaType === mediaCode) {
          return item.mediaName;
        }
      }

      return '';
    };

    const getAdTypeName = (adTypeCode: string) => {
      const adCodes: SaShoppingStatus[] = getSaShoppingCodes();

      for (const item of adCodes) {
        if (item.statusType === adTypeCode) {
          return item.statusName;
        }
      }

      return '';
    };

    const getStatusName = (statusCode: string) => {
      const statusCodes: Status[] = getSaStatusCodes();

      for (const item of statusCodes) {
        if (item.status === statusCode) {
          return item.statusName;
        }
      }

      return '';
    };

    const getOptimizationGoalName = (optimizationGoalCode: string) => {
      const optimizationGoalCodes: OptimizationGoal[] = getOptimizationGoalCodes();

      for (const item of optimizationGoalCodes) {
        if (item.optimizationGoal === optimizationGoalCode) {
          return item.optimizationGoalName;
        }
      }

      return '';
    };

    const getErrorStatusName = (errorStatusCode: string) => {
      const errorStatusCodes: ErrorStatus[] = getErrorStatusCodes();

      for (const item of errorStatusCodes) {
        if (item.errorStatus === errorStatusCode) {
          return item.errorStatusName;
        }
      }

      return '';
    };

    const handleRequestRead = (event?: any) => {
      event?.stopPropagation();
      contextMenuFunctions?.requestRead?.(Number(event.currentTarget.dataset.id));
    };

    const handleRequestEdit = (event?: any) => {
      event?.stopPropagation();
      contextMenuFunctions?.requestEdit?.(Number(event.currentTarget.dataset.id));
    };

    const handleRequestDelete = (event?: any) => {
      event && event.stopPropagation();
      contextMenuFunctions?.requestDelete &&
        contextMenuFunctions.requestDelete(Number(event.currentTarget.dataset.id));
    };

    const handleRequestDuplicate = (event?: any) => {
      event?.stopPropagation();
      contextMenuFunctions?.requestDuplicate?.(Number(event.currentTarget.dataset.id))

      const gtmId = event.currentTarget.dataset.gtmId
      TagManager.dataLayer({
        dataLayer: {
          event: 'click',
          gtm_id: gtmId
        }
      })
    }

    const handleBidSwitchChange = (
      event?: any,
      bidYn?: string,
      optimizationId?: number,
      mediaType?: string,
      saShoppingType?: string,
      optimizationGoal?: string
    ) => {
      event && event.stopPropagation()
      contextMenuFunctions?.requestBidOnOff &&
        contextMenuFunctions.requestBidOnOff(Number(event.target.name), event.target.checked as boolean)

      TagManager.dataLayer({
        dataLayer: {
          event: 'click',
          gtm_id: 'spa-opt-bidYn',
          bid_yn: bidYn,
          optimization_id: optimizationId,
          media_type: mediaType,
          ad_type: saShoppingType,
          optimization_goal: optimizationGoal
        }
      })
    }

    const handleRequestNegativeKeyword = (event?: any) => {
      event?.stopPropagation()
      contextMenuFunctions?.requestNegativeKeyword?.(Number(event.currentTarget.dataset.id))

      const gtmId = event.currentTarget.dataset.gtmId
      TagManager.dataLayer({
        dataLayer: {
          event: 'click',
          gtm_id: gtmId
        }
      })
    }

    const AdviceTooltip = withStyles((theme) => ({
      tooltip: {
        backgroundColor: '#ffffff',
        color: '#2b2b2b',
        minWidth: '360px',
        boxShadow: theme.shadows[2],
        fontSize: 13,
      },
      arrow: {
        '&:before': {
          border: '1px solid #E6E8ED',
        },
        color: '#ffffff',
      },
    }))(Tooltip);

    const getColumnTitle = (columnType: string, sorting: boolean) => {
      columnType = toCamelCase(columnType);

      if (sorting) {
        return (
          <>
            <AdviceTooltip
              id="ss-optimization-advice-tooltip"
              title={<InnerHtml innerHTML={t(`optimization.message.shoppingOptimization.list.${columnType}`) || ''} />}
              placement="right-start"
              arrow
            >
              <span id="adviceIcon">
                <AdviceMarkIcon />
              </span>
            </AdviceTooltip>
            {t(`optimization.label.list.${columnType}`)}
          </>
        );
      } else {
        return (
          <span className="no-sorting-advice">
            <AdviceTooltip
              id="ss-optimization-advice-tooltip"
              title={<InnerHtml innerHTML={t(`optimization.message.shoppingOptimization.list.${columnType}`) || ''} />}
              placement="right-start"
              arrow
            >
              <span id="adviceIcon">
                <AdviceMarkIcon />
              </span>
            </AdviceTooltip>
            {t(`optimization.label.list.${columnType}`)}
          </span>
        );
      }
    };

    const CustomTooltip = withStyles((theme) => ({
      tooltip: {
        backgroundColor: '#56606b',
        color: 'white',
        boxShadow: theme.shadows[1],
        fontSize: 11,
        margin: '0px 0px 0px 0px',
        whiteSpace: 'pre-wrap',
      },
    }))(Tooltip);
    // NOTE: 쇼핑검색 자동입찰 임시로 disable 한 상태 추후에 필요함
    const disabledBidYn = () => {
      // rowData.bidYn === 'N'
      //   ? rowData.status === StatusType.INSPECTING ||
      //     rowData.status === StatusType.INSPECTION_ERROR ||
      //     rowData.status === StatusType.READY ||
      //     rowData.status === StatusType.BIDDING ||
      //     rowData.status === StatusType.END ||
      //     compareDate(rowData.bidEndDate, currentDate()) < 0 ||
      //     !checkAuthority(AuthorityType.OPERATE, advertiser.authorityType)
      //   : rowData.status === StatusType.INSPECTING ||
      //     rowData.status === StatusType.INSPECTION_COMPLETED ||
      //     rowData.status === StatusType.INSPECTION_ERROR ||
      //     rowData.status === StatusType.STOP ||
      //     rowData.status === StatusType.END ||
      //     !checkAuthority(AuthorityType.OPERATE, advertiser.authorityType)
    }
    const getColumnBidYn = () => {
      return {
        title: getColumnTitle(ShoppingOptimizationListColumn.BID_YN, false),
        field: ShoppingOptimizationListColumn.BID_YN,
        cellStyle: {
          width: 100
        },
        sorting: false,
        render: (rowData) => {
          if (rowData.bidYn) {
            return (
              <div id={`bidYn-${rowData.optimizationId}`} className={`bidYn`}>
                <Switch
                  data-testid={`bidYnSwitch-${rowData.optimizationId}`}
                  data-gtm-id="spa-opt-bidYn"
                  className={`bidYnSwitch`}
                  edge="end"
                  color="primary"
                  disabled={
                    rowData.bidYn === 'N'
                      ? (ssList && ssList.maxItemsCount !== -1 && ssList.currentItemsCount > ssList.maxItemsCount) ||
                        rowData.status === StatusType.INSPECTING ||
                        rowData.status === StatusType.INSPECTION_ERROR ||
                        rowData.status === StatusType.READY ||
                        rowData.status === StatusType.BIDDING ||
                        rowData.status === StatusType.END ||
                        compareDate(rowData.bidEndDate, currentDate()) < 0 ||
                        !checkAuthority(AuthorityType.OPERATE, advertiser.authorityType)
                      : rowData.status === StatusType.INSPECTING ||
                        rowData.status === StatusType.INSPECTION_COMPLETED ||
                        rowData.status === StatusType.INSPECTION_ERROR ||
                        rowData.status === StatusType.STOP ||
                        rowData.status === StatusType.END ||
                        !checkAuthority(AuthorityType.OPERATE, advertiser.authorityType)
                  }
                  onChange={(e) =>
                    handleBidSwitchChange(
                      e,
                      rowData.bidYn,
                      rowData.optimizationId,
                      rowData.mediaType,
                      rowData.saShoppingType,
                      rowData.optimizationGoal
                    )
                  }
                  name={rowData.optimizationId.toString()}
                  checked={rowData.bidYn === 'Y'}
                />
              </div>
            )
          }
        }
      } as FixedLayoutColumn<ShoppingOptimizationInfo>
    }

    const getColumnStatus = () => {
      return {
        title: getColumnTitle(ShoppingOptimizationListColumn.STATUS, true),
        field: ShoppingOptimizationListColumn.STATUS,
        sorting: true,
        cellStyle: {
          width: 80
        },
        defaultSort: orderBy === ShoppingOptimizationListColumn.STATUS ? sorting?.toLowerCase() : undefined,
        render: (rowData) => {
          if (rowData.status) {
            return (
              <div id={`status-${rowData.optimizationId}`} className={`status`}>
                <span
                  data-testid={`status-${rowData.optimizationId}`}
                  className={`statusLabel ${rowData.bidYn === 'Y' ? 'color-on' : ''}`}
                >
                  {getStatusName(rowData.status)}
                </span>
              </div>
            )
          }
        }
      } as FixedLayoutColumn<ShoppingOptimizationInfo>
    }

    const getColumnOptimizationResult = () => {
      return {
        title: getColumnTitle(ShoppingOptimizationListColumn.OPTIMIZATION_RESULT, false),
        field: ShoppingOptimizationListColumn.OPTIMIZATION_RESULT,
        sorting: false,
        cellStyle: {
          width: 100
        },
        render: (rowData) => {
          if (rowData.status) {
            return (
              <div id={`optimization-result-${rowData.optimizationId}`} className={`optimization-result`}>
                <span className="icon">
                  <OptimizationResultIcon
                    onClick={() => {}}
                    data-id={rowData.optimizationId.toString()}
                    data-testid={`optimization-result-${rowData.optimizationId}`}
                  />
                </span>
              </div>
            )
          }
        }
      } as FixedLayoutColumn<ShoppingOptimizationInfo>
    }

    const getColumnOptimizationId = () => {
      return {
        title: t('optimization.label.list.optimizationId'),
        field: ShoppingOptimizationListColumn.OPTIMIZATION_ID,
        sorting: true,
        cellStyle: {
          width: 100
        },
        defaultSort: orderBy === ShoppingOptimizationListColumn.OPTIMIZATION_ID ? sorting?.toLowerCase() : undefined,

        render: (rowData) => {
          // const iconVisible = {visibility: compareDate(rowData.bidStartDate, currentDate()) > 0 ? 'hidden': 'visible'};
          const iconVisible = {
            visibility:
              compareDate(rowData.bidStartDate, currentDate()) > 0 ||
              !checkAuthority(AuthorityType.OPERATE, advertiser.authorityType)
                ? 'hidden'
                : 'visible'
          }
          if (rowData.status) {
            return (
              <div id={`status-${rowData.optimizationId}`} className={`optimizationId`}>
                <span data-testid={`mediaType-${rowData.optimizationId}`} className={`optimizationId`}>
                  {rowData.optimizationId}
                </span>
                <span className="icon duplicate" style={iconVisible as React.CSSProperties}>
                  <DuplicateIcon
                    onClick={handleRequestDuplicate}
                    data-id={rowData.optimizationId.toString()}
                    data-testid={`duplicate-${rowData.optimizationId}`}
                    data-gtm-id="spa-opt-copy-click"
                  />
                </span>
              </div>
            )
          }
        }
      } as FixedLayoutColumn<ShoppingOptimizationInfo>
    }

    const getColumnMediaType = () => {
      return {
        title: t('optimization.label.list.mediaType'),
        field: ShoppingOptimizationListColumn.MEDIA_TYPE,
        sorting: false,
        cellStyle: {
          width: 80,
        },

        render: (rowData) => {
          if (rowData.mediaType !== null) {
            return (
              <div id={`mediaType-${rowData.optimizationId}`} className={`mediaType`}>
                <span data-testid={`mediaType-${rowData.optimizationId}`} className={`mediaTypeLabel`}>
                  {getMediaName(rowData.mediaType)}
                </span>
              </div>
            );
          }
        },
      } as FixedLayoutColumn<ShoppingOptimizationInfo>;
    };

    const getColumnAdType = () => {
      return {
        title: getColumnTitle(ShoppingOptimizationListColumn.AD_TYPE, false),
        field: 'SA_SHOPPING_TYPE',
        sorting: true,
        cellStyle: {
          width: 180,
        },
        defaultSort: orderBy === ShoppingOptimizationListColumn.AD_TYPE ? sorting?.toLowerCase() : undefined,

        render: (rowData) => {
          if (rowData.saShoppingType !== null) {
            return (
              <div id={`adType-${rowData.optimizationId}`} className={`adType`}>
                <span data-testid={`adType-${rowData.optimizationId}`} className={`adTypeLabel`}>
                  {getAdTypeName(rowData.saShoppingType)}
                </span>
              </div>
            );
          }
        },
      } as FixedLayoutColumn<ShoppingOptimizationInfo>;
    };

    const getColumnOptimizationName = () => {
      return {
        title: t('optimization.label.list.optimizationName'),
        field: ShoppingOptimizationListColumn.OPTIMIZATION_NAME,
        sorting: true,
        cellStyle: {
          minWidth: 210,
        },
        defaultSort: orderBy === ShoppingOptimizationListColumn.OPTIMIZATION_NAME ? sorting?.toLowerCase() : undefined,

        render: (rowData) => {
          if (rowData.optimizationName !== null) {
            return (
              <div id={`optimizationName-${rowData.optimizationId}`} className={`optimizationName`}>
                <CustomTooltip
                  data-testid={`optimizationNameTooltip-${rowData.optimizationId}`}
                  title={rowData.optimizationName ? rowData.optimizationName : ''}
                  placement="bottom"
                  onClick={handleRequestRead}
                >
                  <p
                    data-testid={`optimizationName-${rowData.optimizationId}`}
                    data-id={rowData.optimizationId.toString()}
                    style={{
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      cursor: 'pointer',
                    }}
                  >
                    {rowData.optimizationName}
                  </p>
                </CustomTooltip>
              </div>
            );
          }
        },
      } as FixedLayoutColumn<ShoppingOptimizationInfo>;
    };

    const getColumnAdgroupsCount = () => {
      return {
        title: t('optimization.label.list.adgroupsCount'),
        field: ShoppingOptimizationListColumn.ADGROUPS_COUNT,
        sorting: true,
        cellStyle: {
          width: 110,
        },
        defaultSort: orderBy === ShoppingOptimizationListColumn.ADGROUPS_COUNT ? sorting?.toLowerCase() : undefined,

        render: (rowData) => {
          if (rowData.adgroupsCount !== null) {
            return (
              <div id={`adgroupsCount-${rowData.adgroupsCount}`} className={`adgroupsCount`}>
                <span data-testid={`adgroupsCount-${rowData.adgroupsCount}`} className={`adgroupsCount`}>
                  {rowData.adgroupsCount}
                </span>
              </div>
            );
          }
        },
      } as FixedLayoutColumn<ShoppingOptimizationInfo>;
    };

    const getColumnBidStartDate = () => {
      return {
        title: t('optimization.label.list.bidStartDate'),
        field: ShoppingOptimizationListColumn.BID_START_DATE,
        defaultSort: orderBy === ShoppingOptimizationListColumn.BID_START_DATE ? sorting?.toLowerCase() : undefined,
        cellStyle: {
          width: 120,
        },
        render: (rowData) => {
          if (rowData.bidStartDate !== null) {
            return (
              <div id={`bidStartDate-${rowData.optimizationId}`} className={`bidStartDate`}>
                <span data-testid={`bidStartDate-${rowData.optimizationId}`} className={`bidStartDateLabel`}>
                  {format(convertFormatToDate(rowData.bidStartDate), DateFnsFormat.DISP_DATE)}
                </span>
              </div>
            );
          }
        },
      } as FixedLayoutColumn<ShoppingOptimizationInfo>;
    };

    const getColumnBidEndDate = () => {
      return {
        title: t('optimization.label.list.bidEndDate'),
        field: ShoppingOptimizationListColumn.BID_END_DATE,
        defaultSort: orderBy === ShoppingOptimizationListColumn.BID_END_DATE ? sorting?.toLowerCase() : undefined,
        cellStyle: {
          width: 120,
        },
        render: (rowData) => {
          if (rowData.bidEndDate !== null) {
            return (
              <div id={`bidEndDate-${rowData.optimizationId}`} className={`bidEndDate`}>
                <span data-testid={`bidEndDate-${rowData.optimizationId}`} className={`bidEndDateLabel`}>
                  {rowData.bidEndDate === '99991231' ? t('common.datePeriodPicker.unsetEndDate') : format(convertFormatToDate(rowData.bidEndDate), DateFnsFormat.DISP_DATE)}
                </span>
              </div>
            );
          }
        },
      } as FixedLayoutColumn<ShoppingOptimizationInfo>;
    };

    const getColumnDailyBudget = () => {
      return {
        title: t('optimization.label.list.dailyBudget'),
        field: ShoppingOptimizationListColumn.DAILY_BUDGET,
        defaultSort: orderBy === ShoppingOptimizationListColumn.DAILY_BUDGET ? sorting?.toLowerCase() : undefined,
        cellStyle: {
          width: 150,
        },
        render: (rowData) => {
          if (rowData.dailyBudget !== null) {
            return (
              <div id={`dailyBudget-${rowData.optimizationId}`} className={`dailyBudget`}>
                <span data-testid={`dailyBudget-${rowData.optimizationId}`} className={`dailyBudgetLabel`}>
                  {rowData.dailyBudget.toLocaleString('ko-KR')}
                </span>
              </div>
            );
          }
        },
      } as FixedLayoutColumn<ShoppingOptimizationInfo>;
    };

    const getColumnOptimizationGoal = () => {
      return {
        title: t('optimization.label.list.optimizationGoal'),
        field: ShoppingOptimizationListColumn.OPTIMIZATION_GOAL,
        defaultSort: orderBy === ShoppingOptimizationListColumn.OPTIMIZATION_GOAL ? sorting?.toLowerCase() : undefined,
        cellStyle: {
          width: 100,
        },
        render: (rowData) => {
          if (rowData.optimizationGoal !== null) {
            return (
              <div id={`optimizationGoal-${rowData.optimizationId}`} className={`optimizationGoal`}>
                <span data-testid={`optimizationGoal-${rowData.optimizationId}`} className={`optimizationGoalLabel`}>
                  {getOptimizationGoalName(rowData.optimizationGoal)}
                </span>
              </div>
            );
          }
        },
      } as FixedLayoutColumn<ShoppingOptimizationInfo>;
    };

    const getColumnNegativeKeyword = () => {
      return {
        title: getColumnTitle(ShoppingOptimizationListColumn.NEGATIVE_KEYWORD, false),
        field: ShoppingOptimizationListColumn.NEGATIVE_KEYWORD,
        sorting: false,
        cellStyle: {
          width: 160,
        },
        render: (rowData) => {
          if (rowData.status) {
            if(rowData.status === 'INSPECTING' || rowData.status === 'INSPECTION_ERROR') return;
            return (
              <div id={`negative-keyword-${rowData.optimizationId}`} className={'negative-keyword'}>
                <span className="icon">
                  <NegativeKeywordIcon
                    onClick={handleRequestNegativeKeyword}
                    data-id={rowData.optimizationId.toString()}
                    data-testid={`negative-keyword-${rowData.optimizationId}`}
                    data-gtm-id="spa-opt-negative-keyword-click"
                  />
                </span>
              </div>
            )
          }
        },
      } as FixedLayoutColumn<ShoppingOptimizationInfo>;
    };

    const getColumnErrorStatus = () => {
      return {
        title: getColumnTitle(ShoppingOptimizationListColumn.ERROR_STATUS, true),
        field: ShoppingOptimizationListColumn.ERROR_STATUS,
        defaultSort: orderBy === ShoppingOptimizationListColumn.ERROR_STATUS ? sorting?.toLowerCase() : undefined,
        cellStyle: {
          width: 160,
        },
        render: (rowData) => {
          if (rowData.errorStatus !== null) {
            switch (rowData.errorStatus) {
              case ErrorStatusType.BUDGET_LACK:
                return (
                  <div id={`errorStatus-${rowData.optimizationId}`} className={`errorStatus`}>
                    <Chip
                      data-testid={`errorStatus-${rowData.optimizationId}`}
                      className={`errorStatusLabel-green`}
                      icon={<BudgetShortageIcon />}
                      label={getErrorStatusName(rowData.errorStatus)}
                      color="secondary"
                      variant="outlined"
                    />
                  </div>
                );
              case ErrorStatusType.BUDGET_OVER:
                return (
                  <div id={`errorStatus-${rowData.optimizationId}`} className={`errorStatus`}>
                    <Chip
                      data-testid={`errorStatus-${rowData.optimizationId}`}
                      className={`errorStatusLabel-yellow`}
                      icon={<BudgetOverflowIcon />}
                      label={getErrorStatusName(rowData.errorStatus)}
                      color="secondary"
                      variant="outlined"
                    />
                  </div>
                );
              case ErrorStatusType.OPTIMIZATION_ERROR:
                return (
                  <div id={`errorStatus-${rowData.optimizationId}`} className={`errorStatus`}>
                    <Chip
                      data-testid={`errorStatus-${rowData.optimizationId}`}
                      className={`errorStatusLabel-red`}
                      icon={<OptimizationErrorIcon />}
                      label={getErrorStatusName(rowData.errorStatus)}
                      color="secondary"
                      variant="outlined"
                    />
                  </div>
                );
              case ErrorStatusType.OFF_BY_BATCH:
                return (
                  <div id={`errorStatus-${rowData.optimizationId}`} className={`errorStatus`}>
                    <Chip
                      data-testid={`errorStatus-${rowData.optimizationId}`}
                      className={`errorStatusLabel-red`}
                      icon={<OptimizationErrorIcon />}
                      label={getErrorStatusName(rowData.errorStatus)}
                      color="secondary"
                      variant="outlined"
                    />
                  </div>
                );
              default:
                return (
                  <div id={`errorStatus-${rowData.optimizationId}`} className={`errorStatus`}>
                    <Chip
                      data-testid={`errorStatus-${rowData.optimizationId}`}
                      className={`errorStatusLabel-red`}
                      icon={<ErrorIcon />}
                      label={getErrorStatusName(rowData.errorStatus)}
                      color="secondary"
                      variant="outlined"
                    />
                  </div>
                );
            }
          }
        },
      } as FixedLayoutColumn<ShoppingOptimizationInfo>;
    };

    const getColumnContextMenu = () => {
      return {
        field: ShoppingOptimizationListColumn.CONTEXT_MENU,
        sorting: false,
        cellStyle: {
          width: 160,
        },
        render: (rowData) => {
          return (
            <div className="delete-modify-icons">
              <span className="icon">
                <EditIcon
                  onClick={(e) => {
                    handleRequestEdit(e)

                    const gtmId = e.currentTarget.dataset.gtmId
                    TagManager.dataLayer({
                      dataLayer: {
                        event: 'click',
                        gtm_id: gtmId,
                        bid_yn: rowData.bidYn,
                        status: rowData.status,
                        optimization_id: rowData.optimizationId,
                        media_type: rowData.mediaType,
                        ad_type: rowData.saShoppingType,
                        optimization_goal: rowData.optimizationGoal
                      }
                    })
                  }}
                  data-id={rowData.optimizationId.toString()}
                  data-testid={`edit-${rowData.optimizationId}`}
                  data-gtm-id="spa-opt-modify"
                />
              </span>
              <span className="icon">
                <DeleteIcon
                  onClick={(e) => {
                    handleRequestDelete(e)

                    const gtmId = e.currentTarget.dataset.gtmId
                    TagManager.dataLayer({
                      dataLayer: {
                        event: 'click',
                        gtm_id: gtmId,
                        bid_yn: rowData.bidYn,
                        status: rowData.status,
                        optimization_id: rowData.optimizationId,
                        media_type: rowData.mediaType,
                        ad_type: rowData.saShoppingType,
                        optimization_goal: rowData.optimizationGoal
                      }
                    })
                  }}
                  data-id={rowData.optimizationId.toString()}
                  data-testid={`delete-${rowData.optimizationId}`}
                  data-gtm-id="spa-opt-delete"
                />
              </span>
              {/* <span className="icon">
                <OptimizationHistoryIcon
                  onClick={() => {}}
                  data-id={rowData.optimizationId.toString()}
                  data-testid={`history-${rowData.optimizationId}`}
                />
              </span> */}
            </div>
          )
        },
      } as FixedLayoutColumn<ShoppingOptimizationInfo>;
    };

    const columns: FixedLayoutColumn<ShoppingOptimizationInfo>[] = [
      getColumnBidYn(),
      getColumnStatus(),
      // getColumnOptimizationResult(),
      getColumnOptimizationId(),
      getColumnOptimizationName(),
      getColumnAdgroupsCount(),
      getColumnMediaType(),
      getColumnAdType(),
      getColumnBidStartDate(),
      getColumnBidEndDate(),
      getColumnDailyBudget(),
      getColumnOptimizationGoal(),
      ...(isProAdvertiser ? [getColumnNegativeKeyword()] : []),
      getColumnErrorStatus(),
    ];

    if (checkAuthority(AuthorityType.OPERATE, advertiser.authorityType)) {
      columns.push(getColumnContextMenu());
    }

    return columns;
  };
}
