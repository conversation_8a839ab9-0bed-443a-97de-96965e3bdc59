import { useState } from 'react'
import { Dialog } from '@material-ui/core'
import * as Oauth<PERSON><PERSON> from '@api/oauth/Oauth';
import { MopInputWithLabel } from '@components/common/mopUI';
import { MopActionButton } from '@components/common/buttons';
import { MediaIcon, MopIcon } from '@components/common';
import { MediaType, AnalyticsType, MOPIcon } from '@models/common';
import { useToast } from "@hooks/common"
import { useTranslation } from 'react-i18next';
import { StatusCode } from '@models/common/CommonResponse';
import './OauthCommonModal.scss'

export interface Props {
  onClose: any;
  open: boolean;
  accountId: string;
  accountName: string;
  media: MediaType|AnalyticsType;
}

interface EditApiForm {
  accessLicense: string;
  secretKey: string;
  appName: string;
  apiToken: string;
  accountName: string;
}

const NAVER_API = [
  { label: '엑세스라이선스', name: 'accessLicense' },
  { label: '비밀키', name: 'secret<PERSON><PERSON>'}
]
const AIRBRIDGE_API = [
  { label: 'App Name', name: 'appName' },
  { label: 'API Token', name: 'apiToken'}
]
const APSSFLYER_API = [
  { label: 'Account Name', name: 'accountName' },
  { label: 'API Token', name: 'apiToken'}
]

const editInputs = (media: MediaType|AnalyticsType) => {
  switch (media) {
    case MediaType.NAVER:
      return NAVER_API
    case AnalyticsType.AIRBRIDGE:
      return AIRBRIDGE_API
    case AnalyticsType.APPSFLYER:
      return APSSFLYER_API
    default:
      return []
  }
}

const EditOauthApiModal = ({ open, onClose, accountId, accountName, media }: Props) => {
  const { t } = useTranslation()
  const { openToast } = useToast()
  const [apiEdit, setApiEdit] = useState({
    accessLicense: '',
    secretKey: '',
    appName: '',
    apiToken: '',
    accountName: ''
  })

  const isValidApiForm = (media: MediaType|AnalyticsType, form: EditApiForm) => {
    let isValid = true
    for(const value of editInputs(media)) {
      const key = value.name as keyof EditApiForm
      if (!form[key]) {
        openToast(t(`oauthLink.toast.editApi.validation.${key}`))
        isValid = false
        return
      }
    }
    return isValid
  }


  const handleChange = (name: string, value: string) => {
    setApiEdit({ ...apiEdit, [name]: value })
  }
  const requestEditApi = async () => {
    const { accessLicense, secretKey, appName, apiToken, accountName } = apiEdit
    if (!isValidApiForm(media, apiEdit)) return
    const response = await OauthApi.updateOauthAPI({
      media,
      apiParams: {
        ...(media === MediaType.NAVER && { accountId, accessLicense, secretKey }),
        ...(media === AnalyticsType.AIRBRIDGE && { appName, apiToken }),
        ...(media === AnalyticsType.APPSFLYER && { accountName, apiToken })
      }
    })

    if (response.successOrNot === 'Y') {
      openToast(t('oauthLink.toast.editApi.response.success'))
      onClose();
    } else {
      if (response.statusCode === StatusCode.INVALID_API_KEY) {
        openToast(t('oauthLink.toast.editApi.response.invalidApiKey'))
        return;
      }
      openToast(t('oauthLink.toast.editApi.response.fail'))
    }
  }
  return (
    <Dialog open={open} onClose={onClose} className="oauth-common-modal edit-api-modal">
      <section className="oauth-common-modal__title">
        <MopIcon name={MOPIcon.CLOSE} onClick={onClose} bgColor="white" />
        <div className='oauth-common-modal__media'>
          <MediaIcon mediaType={media} size={40} />
          <span>{ t(`oauthLink.title.${media}`) }</span>
        </div>
        <div className='oauth-common-modal__account'>
          <span className='oauth-common-modal__account-name'>{ accountName }</span>
          <span className='oauth-common-modal__account-id'>{ accountId }</span>
        </div>
      </section>
      <section className="oauth-common-modal__content">
        {
          editInputs(media).map((apiInput) => (
            <MopInputWithLabel
              key={apiInput.name}
              label={apiInput.label}
              name={apiInput.name}
              handleChange={handleChange}
            />
          ))
        }
        <MopActionButton label='Save' onClick={requestEditApi} />
      </section>
    </Dialog>
  )
}

export default EditOauthApiModal