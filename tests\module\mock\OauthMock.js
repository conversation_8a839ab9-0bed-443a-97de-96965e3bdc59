/// <reference types='Cypress' />

class OauthMock {
  mockCommerceAccounts = []

  initialData() {
    this.mockCommerceAccounts = [
      {
        accountId: '1',
        accountUid: '1a',
        accountName: 'Commerce Account 1',
        applicationType: 'NAVER_COMMERCE',
        integrationDatetime: '2025-07-29 12:59:57',
        clientId: '1123',
      },
      {
        accountId: '2',
        accountUid: '2a',
        accountName: 'Commerce Account 2',
        applicationType: 'NAVER_COMMERCE_DATA_SOLUTION',
        integrationDatetime: '2025-07-29 12:59:57',
        clientId: '1124',
      }
    ]
  }

  successWhenGetOAuthList() {
    const mockOAuthList = [
      {
        media: 'NAVER',
        accountType: 'MEDIA',
        authId: '1',
        accountId: '2232364',
        accountName: '시',
        subId: '',
        subName: '',
        currencyCode: 'KRW',
        platformType: '',
        mediaEmail: '',
        useYn: 'Y',
        utmRuleYn: 'N',
        metrics: [],
        etcCodeType: [],
        numberCommerce: 1
      },
      {
        media: 'NAVER',
        accountType: 'MEDIA',
        authId: '1',
        accountId: '2232365',
        accountName: '시',
        subId: '',
        subName: '',
        currencyCode: 'KRW',
        platformType: '',
        mediaEmail: '',
        useYn: 'Y',
        utmRuleYn: 'N',
        metrics: [],
        etcCodeType: [],
        numberCommerce: 2
      }
    ]

    cy.intercept('GET', `/v1/setting/oauth/account`, {
      body: {
        successOrNot: 'Y',
        statusCode: 'SUCCESS',
        data: mockOAuthList
      }
    }).as('successWhenGetOAuthList')
  }

  successWhenGetCommerceAccounts(customerId) {
    cy.intercept('GET', `/v1/setting/naver-commerce/${customerId}`, (req) => {
      req.reply({
        statusCode: 200,
        body: {
          successOrNot: 'Y',
          statusCode: 'SUCCESS',
          data: this.mockCommerceAccounts
        }
      })
    }).as('getCommerceAccounts')
  }

  successWhenDeleteCommerceAccount(customerId, accountUid) {
    this.mockCommerceAccounts = this.mockCommerceAccounts.filter(
      (account) => account.accountUid !== accountUid
    )
    cy.intercept('DELETE', `/v1/setting/naver-commerce/${customerId}/${accountUid}`, {
      statusCode: 200,
      body: {
        successOrNot: 'Y',
        statusCode: 'SUCCESS'
      }
    }).as('deleteCommerceAccount')
  }
}

export default OauthMock;