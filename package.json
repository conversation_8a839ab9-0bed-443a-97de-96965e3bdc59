{"name": "mop-fe", "version": "0.1.0", "private": true, "dependencies": {"@channel.io/channel-web-sdk-loader": "^2.0.0", "@cypress/request": "^3.0.0", "@date-io/date-fns": "^1.3.13", "@headlessui/react": "1.7", "@heroicons/react": "^2.1.5", "@material-ui/core": "^4.12.4", "@material-ui/data-grid": "^4.0.0-alpha.37", "@material-ui/icons": "^4.11.3", "@material-ui/lab": "^4.0.0-alpha.61", "@material-ui/pickers": "^3.3.10", "@react-spring/parallax": "^9.7.4", "@react-spring/web": "^9.7.4", "@sentry/cli": "^2.31.0", "@sentry/react": "^7.119.0", "@stripe/react-stripe-js": "^3.6.0", "@stripe/stripe-js": "^7.0.0", "@tanstack/react-query": "^4.36.1", "@tanstack/react-table": "^8.19.3", "@tanstack/react-virtual": "^3.13.12", "@use-gesture/react": "^10.3.1", "axios": "^1.7.4", "chart.js": "^3.7.1", "chartjs-plugin-annotation": "^2.2.1", "clsx": "^2.1.1", "date-fns": "^2.28.0", "dompurify": "3.2.4", "env-cmd": "^10.1.0", "i18next": "^23.15.1", "i18next-browser-languagedetector": "^6.1.3", "lodash": "^4.17.21", "material-table": "^1.69.3", "papaparse": "^5.4.1", "plotly.js": "^2.25.2", "plotly.js-basic-dist-min": "^2.25.2", "qs": "^6.11.0", "react": "^17.0.2", "react-chartjs-2": "^4.1.0", "react-date-range": "^2.0.1", "react-device-detect": "^2.2.3", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^17.0.2", "react-gtm-module": "^2.0.11", "react-i18next": "^15.0.2", "react-number-format": "^4.9.3", "react-plotly.js": "^2.6.0", "react-router-dom": "^6.2.2", "react-scripts": "^5.0.0", "react-selecto": "^1.16.2", "react-spring": "^9.7.4", "react-spring-carousel": "^2.0.19", "react-virtualized": "^9.22.6", "react-window": "^1.8.11", "recoil": "^0.7.3-alpha.2", "sass": "^1.85.1", "stripe": "^18.0.0", "tailwind-merge": "^2.5.5", "tailwind-scrollbar-hide": "^1.1.7", "tsconfig-paths": "^3.12.0", "typescript": "^4.5.5", "use-deep-compare-effect": "^1.8.1", "web-vitals": "^2.1.4", "yup": "^0.32.11"}, "devDependencies": {"@babel/polyfill": "^7.6.0", "@cypress/webpack-preprocessor": "^5.11.1", "@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "@sentry/webpack-plugin": "^2.16.0", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-interactions": "^8.6.14", "@storybook/addon-onboarding": "8.6.14", "@storybook/blocks": "^8.6.14", "@storybook/preset-create-react-app": "8.6.14", "@storybook/react": "8.6.14", "@storybook/react-webpack5": "8.6.14", "@storybook/test": "^8.6.14", "@tailwindcss/typography": "^0.5.13", "@testing-library/jest-dom": "^5.16.2", "@testing-library/react": "^12.1.3", "@testing-library/react-hooks": "^7.0.2", "@types/crypto-js": "^4.1.1", "@types/enzyme": "^3.10.4", "@types/enzyme-adapter-react-16": "^1.0.5", "@types/jest": "^27.4.0", "@types/lodash": "^4.14.178", "@types/node": "^17.0.18", "@types/node-sass": "^4.11.2", "@types/papaparse": "^5.3.14", "@types/react": "^17.0.39", "@types/react-date-range": "^1.4.10", "@types/react-dom": "^17.0.11", "@types/react-gtm-module": "^2.0.4", "@types/react-plotly.js": "^2.6.0", "@types/react-router-dom": "^5.3.3", "@types/react-virtualized": "^9.22.2", "@types/react-window": "^1.8.8", "@types/uuid": "^8.3.0", "@typescript-eslint/eslint-plugin": "^5.13.0", "@typescript-eslint/parser": "^5.13.0", "autoprefixer": "^10.4.19", "axios-mock-adapter": "^1.20.0", "babel-plugin-macros": "^3.1.0", "babel-plugin-root-import": "^6.6.0", "css-loader": "^7.1.2", "customize-cra": "^1.0.0", "cypress": "12.17.4", "enzyme": "^3.10.0", "enzyme-adapter-react-16": "^1.15.1", "eslint": "^8.10.0", "eslint-config-prettier": "^8.4.0", "eslint-config-react": "^1.1.7", "eslint-plugin-cypress": "^2.12.1", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.29.2", "eslint-plugin-react-hooks": "^4.3.0", "eslint-plugin-sonarjs": "^0.12.0", "eslint-plugin-storybook": "^0.8.0", "husky": "^3.1.0", "jest-canvas-mock": "^2.4.0", "jest-svg-transformer": "^1.0.0", "license-checker": "^25.0.1", "postcss": "^8.4.38", "prettier": "^2.5.1", "prop-types": "^15.8.1", "react-app-rewired": "^2.2.1", "sass-loader": "^16.0.5", "storybook": "8.6.14", "style-loader": "^4.0.0", "tailwindcss": "^3.4.4", "tsconfig-paths-webpack-plugin": "^4.2.0", "twin.macro": "^3.4.1", "wait-on": "^8.0.3", "webpack": "^5.100.2", "webpack-merge": "^5.8.0"}, "resolutions": {"tough-cookie": "^4.1.3", "@cypress/request": "^3.0.0", "jspdf": "^2.3.1", "postcss": "^8.4.31", "debug": "^4.4.0", "serialize-javascript": "6.0.2", "axios": "^1.7.4", "nth-check": "2.0.1", "ejs": "3.1.10", "react-scripts/@pmmmwh/react-refresh-webpack-plugin/loader-utils": "2.0.4", "react-scripts/@svgr/webpack/loader-utils": "2.0.4", "react-scripts/babel-loader/loader-utils": "2.0.4", "react-scripts/file-loader/loader-utils": "2.0.4", "react-scripts/resolve-url-loader/loader-utils": "2.0.4", "react-scripts/resolve-url-loader/adjust-sourcemap-loader/loader-utils": "2.0.4", "react-scripts/react-dev-utils/loader-utils": "^3.2.1", "webpack": "^5.94.0", "@babel/traverse": "^7.23.2", "json5": "^2.2.2", "portfinder/async": "^2.6.4", "path-to-regexp": "^0.1.12", "@babel/core": "^7.18.6", "body-parser": "^1.20.3", "braces": "3.0.3", "cross-spawn": "^7.0.5", "decode-uri-component": "^0.2.1", "http-proxy-middleware": "^2.0.7", "react-scripts/react-dev-utils/recursive-readdir/minimatch": "^3.0.5", "semver": "^7.5.2", "terser": "^5.14.2", "word-wrap": "^1.2.4", "ws": "^8.17.1", "webpack-dev-middleware": "^5.3.4", "jspdf/dompurify": "3.2.4", "@sideway/formula": "3.0.1", "follow-redirects": "^1.15.6", "nanoid": "^3.3.8", "express": "^4.20.0", "rollup": "^2.79.2", "es5-ext": "^0.10.63", "micromatch": "^4.0.8"}, "scripts": {"start": "env-cmd -e local react-app-rewired start", "start:local": "env-cmd -e local react-app-rewired start", "start:dev": "env-cmd -e dev react-app-rewired start", "start:stage": "env-cmd -e stage react-app-rewired start", "start:prod": "env-cmd -e prod react-app-rewired start", "build": "env-cmd -e local react-app-rewired build && yarn sentry:sourcemaps", "build:local": "env-cmd -e local react-app-rewired build", "build:dev": "env-cmd -e dev react-app-rewired build", "build:stg": "env-cmd -e stage react-app-rewired build", "build:prd": "GENERATE_SOURCEMAP=false env-cmd -e prod react-app-rewired build", "eject": "react-app-rewired eject", "lint": "yarn run lint:src && yarn run lint:spec", "lint:init": "eslint --config .eslintrc-for-source.yaml --init", "lint:src": "eslint --config .eslintrc-for-source.yaml --ignore-pattern '**/*.spec.ts' --ignore-pattern '**/*.spec.tsx'", "lint:spec": "eslint --config .eslintrc-for-spec.yaml", "lint:win": "yarn run lint:src:win && yarn run lint:spec:win", "lint:src:win": "eslint --config .eslintrc-for-source.yaml --ignore-pattern \"*.spec.*\"", "lint:spec:win": "eslint --config .eslintrc-for-spec.yaml", "lint:noFix": "yarn run lint:src:noFix && yarn run lint:spec:noFix", "lint:src:noFix": "eslint --config .eslintrc-for-source.yaml --ignore-pattern '**/*.spec.ts' --ignore-pattern '**/*.spec.tsx' './src/**/*.{ts,tsx}'", "lint:spec:noFix": "eslint --config .eslintrc-for-spec.yaml './src/**/*.spec.{ts,tsx}'", "lint:noFix:win": "yarn run lint:src:noFix:win && yarn run lint:spec:noFix:win", "lint:src:noFix:win": "eslint --config .eslintrc-for-source.yaml --ignore-pattern \"*.spec.*\" \"src\\**\\*.{ts,tsx}\"", "lint:spec:noFix:win": "eslint --config .eslintrc-for-spec.yaml \"src\\**\\*.spec.{ts,tsx}\"", "test": "yarn run test:unit:coverage", "test:unit": "react-scripts test --verbose --watchAll=false --testPathIgnorePatterns ./src/pages/*", "test:unit:coverage": "react-scripts test --coverage --watchAll=false --testPathIgnorePatterns ./src/pages/*", "test:module": "yarn run cypress:run:module", "test:module:default": "yarn run cypress:run:module:default", "test:health": "yarn run cypress:run:health", "test:smoke": "yarn run cypress:run:smoke", "test:smoke:prod": "yarn run cypress:run:smoke:prod", "cypress:version": "cypress version", "cypress:verify": "cypress verify", "cypress:open": "export CYPRESS_BASE_URL=http://localhost:3000 && cypress open --config-file cypress-module.config.js --config supportFile=tests/support/index.local.js", "cypress:open:delayMode": "export CYPRESS_BASE_URL=http://localhost:3000 && cypress open --config-file cypress-module.config.js --config supportFile=tests/support/index.delayMode.js", "cypress:open:window": "set CYPRESS_BASE_URL=http://localhost:3000&& wait-on http://localhost:3000&& cypress open --config-file cypress-module.config.js --config supportFile=tests/support/index.local.js", "cypress:open:delayMode:window": "cypress open --config-file cypress-module.config.js --config supportFile=tests/support/index.delayMode.js", "cypress:open:smoke": "export CYPRESS_BASE_URL=http://localhost:3000 && cypress open --config-file cypress-smoke.config.js --config supportFile=tests/support/index.smoke.js --env timestamp=$(date +%Y%m%d%H%M%S)", "cypress:open:smoke:window": "set CYPRESS_BASE_URL=http://localhost:3000 && cypress open --config-file cypress-smoke.config.js --config supportFile=tests/support/index.smoke.js --env timestamp=$(date +%Y%m%d%H%M%S)", "cypress:run:module": "export CYPRESS_BASE_URL=http://localhost:3000 && wait-on http://localhost:3000 && cypress run --config-file cypress-module.config.js --config supportFile=tests/support/index.module.js", "cypress:run:module:default": "cypress run --config-file cypress-module.config.js --config supportFile=tests/support/index.module.js", "cypress:run:health": "cypress run --config-file cypress-health.config.js", "cypress:run:smoke": "cypress run --config-file cypress-smoke.config.js --config supportFile=tests/support/index.smoke.js", "cypress:run:smoke:prod": "cypress run --config-file cypress-smoke-prod.config.js", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org lg-cns-hb --project mop ./build && sentry-cli sourcemaps upload --org lg-cns-hb --project mop ./build", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "license:check": "license-checker --summary", "license:list": "license-checker --plainVertical", "license:csv": "license-checker --csv --out licenses.csv", "license:json": "license-checker --json --out licenses.json", "license:generate": "chmod u+x generate-license.sh && ./generate-license.sh", "license:backup": "cp LEGAL_NOTICE.TXT LEGAL_NOTICE_$(date +%Y%m%d_%H%M%S).backup", "license:validate": "license-checker --failOn 'GPL;AGPL;LGPL'", "license:cleanup": "rm -f LEGAL_NOTICE_BACKUP.TXT LEGAL_NOTICE_*.backup"}, "eslintConfig": {"extends": ["react-app", "react-app/jest", "plugin:cypress/recommended", "plugin:storybook/recommended"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"moduleNameMapper": {"@api/(.*)$": "<rootDir>/src/api/$1", "@assets/(.*)$": "<rootDir>/src/assets/$1", "@components/(.*)$": "<rootDir>/src/components/$1", "@images/(.*)$": "<rootDir>/src/components/assets/images/$1", "@models/(.*)$": "<rootDir>/src/models/$1", "@pages/(.*)$": "<rootDir>/src/pages/$1", "@router/(.*)$": "<rootDir>/src/router/$1", "@store/(.*)$": "<rootDir>/src/store/$1", "@utils/(.*)$": "<rootDir>/src/utils/$1", "@hooks/(.*)$": "<rootDir>/src/hooks/$1", "^.+\\.svg$": "jest-svg-transformer", "^axios$": "axios/dist/node/axios.cjs"}, "coverageThreshold": {"global": {"branches": 85, "functions": 85, "lines": 85, "statements": 85}}, "coveragePathIgnorePatterns": ["src/components/*", "src/models/*", "src/pages/*", "src/assets/*", "src/hooks/*", "src/router/*", "src/store/*", "src/stories", ".stories.tsx", "src/i18n.tsx", "src/index.tsx", "src/reportWebVitals.ts", "src/App.tsx"], "transformIgnorePatterns": ["/node_modules/(?!react-dnd|dnd-core|@react-dnd)"]}, "husky": {"hooks": {"commit-msg": "sh husky_commit_msg.sh .git/COMMIT_EDITMSG"}}}