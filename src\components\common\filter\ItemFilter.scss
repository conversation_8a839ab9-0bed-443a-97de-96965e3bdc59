.item-filter {
  --dropdown-width: 40px;
  --width: 100%;
  --font-size: 14px;

  width: var(--width);
  border: 1px solid #b5b7c9;

  &:not(:first-child) {
    border-left: none;
  }

  .MuiInputBase-root.MuiInput-root {
    font-size: var(--font-size);
    width: 100%;
    font-weight: 300;
    color: var(--point_color);
    box-sizing: border-box;
    text-align: center;

    .MuiSelect-select.MuiSelect-select {
      padding-right: var(--dropdown-width);
    }

    #select-dropdown-icon {
      width: var(--dropdown-width);
      min-width: none;
      height: 100%;
    }

    &.MuiInput-underline {
      &:before, &:after, &:hover:not(.Mui-disabled):before {
        border-bottom: none;
      }
    }

    &:focus {
      background: none;
    }
  }
}
