/* istanbul ignore file */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import SessionUtil from './SessionUtil'
import CommonResponse, { StatusCode } from '@models/common/CommonResponse'
import { v4 as uuidv4 } from 'uuid'
import { Service, ServicePort } from '@models/common/Service'
import { openCommonDialog } from '@components/common/mopUI/CommonDialog'
import { YNFlag } from '@models/common'
import { t } from '@utils/translationUtils'

export enum Method {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE',
  PATCH = 'PATCH'
}

export interface QueryParams {
  [key: string]: string | number | boolean | undefined
}

export interface ParamObject {
  queryParams?: QueryParams
  bodyParams?: object
}

export interface Config {
  isLoading?: boolean
}

export interface ApiRequest {
  service: Service
  url: string
  method: Method
  params?: ParamObject
  config?: Config
  signal?: AbortSignal
}

/* istanbul ignore next */
const getInstance = (serviceName: string, isLoading: boolean, params?: any, signal?: AbortSignal): AxiosInstance => {
  if (isLoading) window.loadingSpinner.setChange(true)

  axios.defaults.headers.post['Content-Type'] = 'application/json'
  const apiUrl = process.env.REACT_APP_API_URL ? JSON.parse(process.env.REACT_APP_API_URL) : {}
  let baseURL: string = apiUrl['MOP_BE'] || ''

  const sessionUtil = new SessionUtil()

  if (process.env.REACT_APP_NODE_ENV === 'local') {
    switch (serviceName) {
      case Service.MOP_BE:
        baseURL += ':' + ServicePort.MOP_BE.toString()
        break
      default:
        break
    }
  }

  const instance = axios.create({
    baseURL: baseURL,
    params: params || {},
    signal: signal
  })

  // 공통 요청 처리
  instance.interceptors.request.use(
    (config: InternalAxiosRequestConfig): InternalAxiosRequestConfig => {
      const newConfig = { ...config }
      if (newConfig?.headers) {
        newConfig.headers['x-correlation-id'] =
          window.location.pathname === '/'
            ? 'root'.concat('_').concat(uuidv4())
            : window.location.pathname?.concat('_').concat(uuidv4()) || ''
        if (sessionUtil.getSessionInfo().sessionId) {
          newConfig.headers['x-session-id'] = sessionUtil.getSessionInfo().sessionId || ''
        }
      }
      return newConfig
    },
    (error: any): Promise<any> => {
      return Promise.reject(error)
    }
  )

  // success / error 공통 처리
  instance.interceptors.response.use(
    (response: any): any => {
      if (isLoading) window.loadingSpinner.setChange(false)

      let commonResponse: CommonResponse
      if (response.config.responseType === 'arraybuffer' && response.headers['content-disposition']) {
        return response
      } else if (response.config.responseType === 'arraybuffer') {
        const jsonString = String.fromCharCode.apply(null, [...new Uint8Array(response.data as number[])])
        try {
          commonResponse = JSON.parse(jsonString) as CommonResponse
        } catch(e) {
          return response
        }
      } else {
        commonResponse = response.data as CommonResponse
      }

      if (commonResponse.statusCode && commonResponse.statusCode === StatusCode.SESSION_EXPIRE) {
        const action = () => {
          sessionUtil.deleteSessionInfo()
          window.location.assign('/login')
        }
        openCommonDialog({
          actionLabel: t('common.label.button.confirm-k'),
          message: t('common.message.sessionExpired'),
          onAction: action,
          onClose: action
        })
      }
      return commonResponse
    },

    (error: any): any => {
      if (isLoading) window.loadingSpinner.setChange(false)

      const unknownError: CommonResponse = {
        successOrNot: 'N',
        statusCode: StatusCode.UNKNOWN_ERROR,
        data: {}
      }

      // eslint-disable-next-line
      if (error.response) {
        const statusCode = error.response.status as number
        if (statusCode === 401 || statusCode === 403) {
          window.location.assign('/unauthorized')
        } else if (statusCode >= 400) {
          window.location.assign('/error')
        }
      }
      return unknownError
    }
  )

  return instance
}

const getQueryStringFormat = (queryParams?: QueryParams) => {
  if (!queryParams) return ''
  const keys = Object.keys(queryParams)
  const queryString = keys
    .filter((key) => queryParams[key] !== undefined)
    .map((key) => `${key}=${encodeURIComponent(queryParams[key] as string)}`) // eslint-disable-line
    .join('&')
  return queryString ? `?${queryString}` : ''
}

export const cleanQueryParams = (params: Record<string, any>): QueryParams => {
  const cleaned: QueryParams = {}
  
  Object.entries(params).forEach(([key, value]) => {
    // Skip undefined, null, empty strings, and 'ALL' values
    if (value !== undefined && value !== null && value !== '' && value !== 'ALL') {
      cleaned[key] = value
    }
  })
  
  return cleaned
}

export const callApi = async <T = any>(apiRequest: ApiRequest): Promise<CommonResponse<T>> => {
  const url: string = apiRequest.url + getQueryStringFormat(apiRequest.params?.queryParams)
  const isLoading = apiRequest.config?.isLoading || false
  let response: CommonResponse = {
    successOrNot: 'N',
    statusCode: StatusCode.UNKNOWN_ERROR,
    data: {}
  }

  switch (apiRequest.method) {
    case Method.GET:
      response = await getInstance(apiRequest.service, isLoading, undefined, apiRequest.signal).get(url)
      break
    case Method.POST:
      response = await getInstance(apiRequest.service, isLoading, undefined, apiRequest.signal).post(
        url,
        apiRequest.params?.bodyParams
      )
      break
    case Method.PUT:
      response = await getInstance(apiRequest.service, isLoading, undefined, apiRequest.signal).put(
        url,
        apiRequest.params?.bodyParams
      )
      break
    case Method.DELETE:
      response = await getInstance(apiRequest.service, isLoading, undefined, apiRequest.signal).delete(url)
      break
    case Method.PATCH:
      response = await getInstance(apiRequest.service, isLoading, undefined, apiRequest.signal).patch(
        url,
        apiRequest.params?.bodyParams
      )
      break
    default:
      break
  }
  return response
}

/* istanbul ignore next */
export const downloadByteArray = async (apiRequest: ApiRequest) => {
  const url: string = apiRequest.url + getQueryStringFormat(apiRequest.params?.queryParams)
  const isLoading = apiRequest.config?.isLoading || false

  return await getInstance(apiRequest.service, isLoading).get(url, { responseType: 'arraybuffer' })
}

/* istanbul ignore next */
export const openDownloadLink = (response: AxiosResponse) => {
  const matches = response.headers['content-disposition']?.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
  const fileName = matches?.[1].replace(/['"]/g, '') || 'download.csv'

  const blob = new Blob([response.data], { type: 'application/octet-stream' })
  const url = window.URL.createObjectURL(blob)
  const a = document.createElement('a')

  a.href = url
  a.download = fileName
  a.click()
  a.remove()
  window.URL.revokeObjectURL(url)
}

/* istanbul ignore next */
export const handleResultWithDefault = <T>(response: CommonResponse<T>, defaultValue: T) => {
  return response.successOrNot === YNFlag.Y && response.data ? response.data : defaultValue
}
