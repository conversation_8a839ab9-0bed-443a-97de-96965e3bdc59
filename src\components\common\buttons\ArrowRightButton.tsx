import React from 'react'
import { ReactComponent as ArrowRightIcon } from '@components/assets/images/icon_arrow_right.svg';
import './ArrowRightButton.scss'
import TagManager from 'react-gtm-module'

interface Props {
  label?: string
  onClick: () => void
  value?: string | number
  rounded?: string // half, sm, md
  contained?: boolean
  gtmId?: string
}
const ArrowRightButton = ({
  label,
  onClick,
  value,
  rounded = 'half',
  contained = true,
  gtmId
}: React.PropsWithChildren<Props>) => {
  const handleClick: React.MouseEventHandler<HTMLButtonElement> = (event) => {
    const gtmEventId = event.currentTarget.dataset.gtmId

    if (gtmEventId) {
      try {
        TagManager.dataLayer({
          dataLayer: {
            event: 'click',
            gtm_id: gtmEventId
          }
        })
      } catch (error) {
        console.error('GTM event error:', error)
      }
    }
    onClick()
  }

  return (
    <button
      className={`arrow-right-button ${rounded} ${contained ? 'contained' : ''}`}
      value={value}
      onClick={handleClick}
      data-gtm-id={gtmId}
    >
      {label && <span>{label}</span>}
      <ArrowRightIcon />
    </button>
  )
}

export default ArrowRightButton