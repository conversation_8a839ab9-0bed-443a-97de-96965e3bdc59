import React, { FormEvent, useCallback, useEffect, useState } from 'react'
import { debounce } from 'lodash'

import * as sessionService from '@api/common/Session'
import SessionUtil from '@utils/SessionUtil'
import { MenuItem, Tooltip, Popper } from '@material-ui/core/'
import { useTranslation } from 'react-i18next'
import './MopHeader.scss'
import { useSetRecoilState } from 'recoil'
import { Advertiser, SubscriptionProductType } from '@models/common/Advertiser'
import { saReportState, saReportListState } from '@store/SaReport'
import { Report } from '@models/common/Report'
import { Link, useNavigate } from 'react-router-dom'
import { ReactComponent as Logo } from '@components/assets/images/main_logo.svg'
import { ReactComponent as IconLang } from '@components/assets/images/icon_lang.svg'
import { ReactComponent as IconEn } from '@components/assets/images/lang_en.svg'
import { ReactComponent as IconKo } from '@components/assets/images/lang_ko.svg'
import IconMemberInfo from '@components/assets/images/menu_icon_member.svg'
import SelectBottom from '@components/common/SelectBottom'
import BookmarkAdvertiserToggle from '@components/common/BookmarkAdvertiserToggle'
import IconSearch from '@components/assets/images/icon_search.png'
import IconSupport from '@components/assets/images/support.svg'
import { useAuthority, useLayout } from '@hooks/common'
import { ProBadge, TransText } from '@components/common'
import { LiteBadge } from '@components/common/BaseChip'
import { ReactComponent as NaverLogo } from '@assets/images/logo-naver-green.svg'
import { ReactComponent as CNSLogo } from '@assets/images/logo-lgcns-white.svg'
import { ReactComponent as Collabo } from '@assets/icon/collaboration-mark.svg'

const MopHeader: React.FC = () => {
  const sessionUtil = new SessionUtil()
  const { t, i18n } = useTranslation()
  const navigation = useNavigate()
  const { showAdSelect } = useLayout()
  const { advertiser, setAdvertiser, advertiserList, setAdvertiserList, setAdvertiserLoaded, getAdvertisers } =
    useAuthority()
  const [filterKeyword, setFilterKeyword] = useState<string>('')
  const setSaReport = useSetRecoilState(saReportState)
  const setSaReportList = useSetRecoilState(saReportListState)
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const [isOpenUpdatePasswordModal, setOpenUpdatePasswordModal] = useState(false)
  const [bookmarked, setBookmarked] = useState(false)
  const [isLanguageMenuOpen, setLanguageMenuOpen] = useState<null | HTMLElement>(null)
  const [loadedListLength, setLoadedListLength] = useState(100)

  const email = sessionUtil.getSessionInfo().email

  const sortFunc = (a: Advertiser, b: Advertiser) => {
    if (a.advertiserName < b.advertiserName) {
      return -1
    } else if (a.advertiserName > b.advertiserName) {
      return 1
    }
    return 0
  }

  const sortedList = advertiserList.slice().sort(sortFunc)
  let filteredAdvertiserList = sortedList.slice(0, loadedListLength)

  if (filterKeyword.trim().length > 0) {
    filteredAdvertiserList = sortedList.filter(
      (item) =>
        item.advertiserName.toLocaleLowerCase().includes(filterKeyword.toLocaleLowerCase()) ||
        item.advertiserId === advertiser.advertiserId
    )
  } else if (bookmarked) {
    filteredAdvertiserList = advertiserList.filter((item) => item.bookmarkYn === 'Y')
    if (filteredAdvertiserList.indexOf(advertiser) === -1) {
      filteredAdvertiserList.unshift(advertiser)
    }
  } else {
    const currentAdvertiserInList = filteredAdvertiserList.some(
      item => item.advertiserId === advertiser.advertiserId
    )
    
    if (!currentAdvertiserInList) {
      filteredAdvertiserList.unshift(advertiser)
    }
  }

  const increaseLoadedListLength = useCallback(() => {
    setLoadedListLength((prev) => prev + 100)
  }, [])

  const handleScroll = useCallback(
    debounce((e: React.UIEvent<HTMLElement>) => {
      const target = e.target as HTMLElement
      if (target.scrollHeight - target.scrollTop <= target.clientHeight + 20) {
        increaseLoadedListLength()
      }
    }, 200),
    [increaseLoadedListLength]
  )

  const handleLanguageMenu = (event: React.MouseEvent<HTMLElement>) => {
    setLanguageMenuOpen(isLanguageMenuOpen ? null : event.currentTarget)
  }

  const handleLanguage = (lang: string) => {
    setLanguageMenuOpen(null)
    i18n.changeLanguage(lang)
    localStorage.setItem('lang', lang)
  }

  const handleAccountMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(anchorEl ? null : event.currentTarget)
  }

  const routeToHome = () => {
    navigation('/dashboard')
  }

  const handleLogout = async () => {
    await sessionService.logout()
    sessionUtil.deleteSessionInfo()
    window.location.assign('/')
  }

  const handleAdvertiserSelectChange = (event: any) => {
    const id = event.target.value as number
    if (id !== advertiser.advertiserId) {
      const findAdvertiser = advertiserList.find((ad) => ad.advertiserId === id)
      setAdvertiser(findAdvertiser!)
      setSaReport({} as Report)
      setSaReportList([] as Report[])
    }
    resetAdvertiserNameFilter()
  }

  const resetAdvertiserNameFilter = useCallback(() => {
    setFilterKeyword('')
    setLoadedListLength(100)
  }, [])

  const handleChangeAdvertiserNameFilter = useCallback((e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setFilterKeyword((e.target as any).keyword.value.trim())
  }, [])

  useEffect(() => {
    if (advertiser?.advertiserId) {
      sessionUtil.setAdvertiserId(advertiser.advertiserId)
    }
  }, [advertiser]) // eslint-disable-line

  useEffect(() => {
    setAdvertiserLoaded(false)
    getAdvertisers().then((response) => {
      if (response.successOrNot === 'Y' && response.data) {
        // FIXME: useYN null 경우 확인 필요
        const advertisers: Advertiser[] = response.data.filter((adv) => adv.useYn !== 'N')
        const advertiserId = sessionUtil.getAdvertiserId()
        if (advertisers?.length) {
          const findAdvertiser = advertisers.find((advertiser) => advertiser.advertiserId === advertiserId)
          if (findAdvertiser) {
            setAdvertiser(findAdvertiser)
          } else {
            setAdvertiser(response.data[0] as Advertiser)
          }
        }

        setAdvertiserList(advertisers)
        setAdvertiserLoaded(true)
        if (response.data.length === 0 && sessionUtil.isSystemAdmin()) {
          navigation('/admin')
        }
      }
      //TODO: Advertisement 조회 실패시 처리
    })

    const savedLanguage = localStorage.getItem('lang')
    if (savedLanguage) {
      i18n.changeLanguage(savedLanguage)
    }
  }, []) // eslint-disable-line

  const toggleBookmarkedAdvertisers = () => {
    setBookmarked(!bookmarked)
  }

  const togglePasswordModal = () => {
    setOpenUpdatePasswordModal(!isOpenUpdatePasswordModal)
  }

  const routeEmailAlert = () => {
    navigation('/email-alerts')
    setAnchorEl(null)
  }

  return (
    <header
      id="MopHeader"
      className="px-[40px] h-[110px] bg-landing-black flex justify-between items-center font-light text-white"
    >
      <div className="flex items-center gap-8">
        <h1>
          <Logo
            data-testid="logoIcon"
            id="logoIcon"
            className="w-[125px] h-[73px] hover:cursor-pointer"
            onClick={routeToHome}
          />
          <span className="sr-only">MOP (Marketing Optimization Platform)</span>
        </h1>
        {showAdSelect && (
          <SelectBottom
            id="advertiserSelectButton"
            data-testid="advertiserSelectButton"
            value={advertiser.advertiserId}
            onChange={handleAdvertiserSelectChange}
            onClose={resetAdvertiserNameFilter}
            disableUnderline
            MenuProps={{
              id: 'mop-header-select-advertiser',
              PaperProps: {
                onScroll: handleScroll
              }
            }}
            header={
              <div id="AdvertiserNameFilter">
                <BookmarkAdvertiserToggle
                  advertiserId={-1}
                  type="filter"
                  isFiltered={bookmarked}
                  filterBookmarked={toggleBookmarkedAdvertisers}
                />
                <form onSubmit={handleChangeAdvertiserNameFilter}>
                  <input
                    name="keyword"
                    placeholder={t('common.label.advertiser')}
                    onKeyDown={(e) => e.stopPropagation()}
                  />
                  <button type="submit">
                    <img src={IconSearch} />
                  </button>
                </form>
              </div>
            }
          >
            {filteredAdvertiserList.map((item, index) => (
              <MenuItem
                data-testid={`advertiserSelectMenu-${index}`}
                key={item.advertiserId}
                value={item.advertiserId}
                className="advertiser-menu"
                disableRipple
              >
                <BookmarkAdvertiserToggle advertiserId={item.advertiserId} />
                <div className="advertiser-info">
                  {item.subscriptionProductType === SubscriptionProductType.PRO && <ProBadge size="sm" />}
                  {item.subscriptionProductType === SubscriptionProductType.LITE && <LiteBadge size="sm" />}
                  <label>{item.advertiserName}</label>
                  <label className="font-medium text-xxs">{item.advertiserCurrencyCode}</label>
                  <div className="gap"></div>
                  {item.authorityType && (
                    <label className={`auth-type-label ${item.authorityType.toLowerCase()}`}>
                      {t(`setting.authority.name.${item.authorityType}`)}
                    </label>
                  )}
                </div>
              </MenuItem>
            ))}
          </SelectBottom>
        )}
      </div>
      <div className="flex items-center justify-center gap-4">
        <span className='flex items-center gap-2.5'>
          <NaverLogo className='w-auto h-[15px]' />
          <Collabo className='w-auto h-[18px]' />
          <CNSLogo className='w-auto h-[22px]' />
        </span>
        <TransText as="span" className="font-smibold text-xl text-white" i18nKey="landing.banner.text" />
      </div>
      <div className="flex items-center gap-[12px] text-xl">
        <TransText
          className="px-2.5"
          i18nKey="layout.label.MopHeader.welcome"
          values={{
            memberName: sessionUtil.getSessionInfo().memberName
          }}
        />
        <button onClick={() => window.open('https://support.mop.co.kr', '_blank')} color="inherit">
          <img src={IconSupport} alt="SUPPORT" />
        </button>
        <Tooltip title="Language" arrow>
          <>
            <button
              data-testid="lgnIconButton"
              aria-controls="accountMenu"
              aria-haspopup="true"
              onClick={handleLanguageMenu}
              color="inherit"
            >
              <IconLang className="w-[35px] h-[35px]" />
            </button>
            <Popper
              id="account-menu-popper"
              open={!!isLanguageMenuOpen}
              anchorEl={isLanguageMenuOpen}
              placement="bottom"
            >
              <div className="account-menu-item" onClick={() => handleLanguage('ko')}>
                KO <IconKo /> KOREAN
              </div>
              <div className="account-menu-item" onClick={() => handleLanguage('en')}>
                EN <IconEn /> ENGLISH
              </div>
            </Popper>
          </>
        </Tooltip>
        <Tooltip title="Account">
          <Link data-testid="accountIconButton" to="/profile">
            <img src={IconMemberInfo} alt="MEMBER" data-testid="icon-menu-member" />
          </Link>
        </Tooltip>
        <Tooltip title={t('layout.label.MopHeader.logout') || ''}>
          <div id="logout" onClick={handleLogout} className="px-2.5">
            {t('layout.label.MopHeader.logout')}
          </div>
        </Tooltip>
      </div>
    </header>
  )
}

export default MopHeader
