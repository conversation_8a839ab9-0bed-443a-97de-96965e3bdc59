import './ConnectOauthPage.scss'
import React, { useState, useEffect } from 'react'
import { useLocation, Location } from 'react-router-dom'
import { MenuItem } from '@material-ui/core'
import { useTranslation } from 'react-i18next'
import * as OauthApi from '@api/oauth/Oauth'
import { useToast } from '@hooks/common'
import { ConnectDataModal, CreateMetricsModal, EditOauthApiModal, OauthListTableFormatter } from '@components/oauth'
import { FixedLayoutTable, TablePagination } from '@components/common/table'
import { MediaIcon, MopIcon, TextIcon } from '@components/common/icon'
import { PageListFilter, PageListFilterGroup, PageListHeader } from '@components/common/page'
import { pageSizeOptions } from '@models/common/CommonConstants'
import { OauthListItem, AccountType } from '@models/oauth/Oauth'
import { AdvertiserCurrencyCode } from '@models/common/Advertiser'
import { AnalyticsType, MOPIcon, MediaType, YNFlag } from '@models/common'
import { sortBy } from 'lodash'
import { MopMedia } from '@utils/common/MediaType'
import SessionUtil from '@utils/SessionUtil'
import { MemberStatusCode } from '@models/member/Member'
import { TransText } from '@components/common'
import CommerceAccountDetailModal from '@components/oauth/CommerceAccountDetailModal'

interface OauthPageState {
  state: {
    filter?: string
  }
}

const sessionUtil = new SessionUtil()

const statusFilterItems = [
  { name: MOPIcon.SWITCH_ON, value: YNFlag.Y },
  { name: MOPIcon.SWITCH_OFF, value: YNFlag.N },
  { name: MOPIcon.SWITCH_DISABLE, value: 'disable' }
]
const oauthTable = new OauthListTableFormatter()

const ConnectOauthPage: React.FC = () => {
  const { t } = useTranslation()
  const { state } = useLocation() as Location & OauthPageState
  const { openI18nToast, openExistingI18nToast } = useToast()
  const [showConnectDataModal, setConnectDataModal] = useState(false)
  const [showEditApiModal, setEditApiModal] = useState(false)
  const [showMetricsModal, setMetricsModal] = useState(false)
  const [showCommerceAccountModal, setCommerceAccountModal] = useState(false)
  const [modalData, setModalData] = useState<any>({
    accountId: '',
    accountName: '',
    media: MediaType.NAVER
  })
  const [canSettingUTM, setCanSettingUTM] = useState(true)
  const allColumns: any[] = oauthTable.getColumnFormat({ canSettingUTM: canSettingUTM })
  const [searchText, setSearchText] = useState('')
  const [statusFilter, setStatusFilter] = useState('ALL')
  const [mediaFilter, setMediaFilter] = useState('ALL')
  const [currencyFilter, setCurrencyFilter] = useState('ALL')
  const [typeFilter, setTypeFilter] = useState('ALL')
  const [page, setPage] = useState(1)
  const [rowsPerPage, setRowsPerPage] = useState(pageSizeOptions[0])
  const [OauthList, setOAuthList] = useState<OauthListItem[]>([])

  const filteredItmes = OauthList.filter((item) =>
    statusFilter === 'disable' ? item.metrics.length === 0 : statusFilter === 'ALL' || statusFilter === item.useYn
  )
    .filter((item) => mediaFilter === 'ALL' || mediaFilter === item.media)
    .filter((item) => currencyFilter === 'ALL' || currencyFilter === item.currencyCode)
    .filter((item) => typeFilter === 'ALL' || typeFilter === item.accountType)
    .filter(
      (item) =>
        searchText === '' ||
        (item.accountName && item.accountName.toLowerCase().includes(searchText.toLowerCase())) ||
        item.accountId.includes(searchText)
    )

  const changeFilter = (event: any) => {
    const { name, value } = event.target
    if (name === 'statusFilter') {
      setStatusFilter(value)
    } else if (name === 'currencyFilter') {
      setCurrencyFilter(value)
    } else if (name === 'mediaFilter') {
      setMediaFilter(value)
    } else if (name === 'typeFilter') {
      setTypeFilter(value)
    }
    setPage(1)
  }

  const handleSearch = (searchQuery: string) => {
    setSearchText(searchQuery)
  }

  const openCreateModal = () => {
    setConnectDataModal(true)
  }

  const getOAuthList = async () => {
    const data = await OauthApi.getOAuthList()
    if (data) {
      setOAuthList(data)
      const connectedAppsflyer = data.filter(
        (oauth) => oauth.useYn === YNFlag.Y && oauth.media === AnalyticsType.APPSFLYER
      )
      setCanSettingUTM(connectedAppsflyer.length < 10)
    } else {
      openI18nToast('setting.toast.oauth.searchFail')
    }
  }

  oauthTable.openEditApiModal = (params: any) => {
    setModalData(params)
    setEditApiModal(true)
  }

  oauthTable.openCreateMetricsModal = (params: any) => {
    setModalData(params)
    setMetricsModal(true)
  }

  oauthTable.requestUpdateList = async () => {
    await getOAuthList()
  }

  oauthTable.openCommerceAccountModal = (params: any) => {
    setModalData(params)
    setCommerceAccountModal(true)
  }

  const receiveLinkCommerceResponse = ({ data }: MessageEvent) => {
    //If Commerce Account Detail modal is open, no need reload list
    if (data.type !== 'oauth' || showCommerceAccountModal) return
    openExistingI18nToast(`oauthLink.toast.${data.result}`)
    getOAuthList()
  }

  useEffect(() => {
    getOAuthList()
    if (state && state.filter === AnalyticsType.APPSFLYER) {
      setMediaFilter(AnalyticsType.APPSFLYER)
    }

    if (sessionUtil.getSessionInfo().memberStatusCode === MemberStatusCode.INITIAL) {
      openCreateModal()
    }
  }, []) // eslint-disable-line

  useEffect(() => {
    window.addEventListener('message', receiveLinkCommerceResponse)
    return () => {
      window.removeEventListener('message', receiveLinkCommerceResponse)
    }
  }, []) //eslint-disable-line

  return (
    <div id="connect-oauth-page">
      <PageListFilterGroup>
        <PageListFilter
          label={t('oauthLink.label.oauthStatus')}
          name="statusFilter"
          value={statusFilter}
          onChange={changeFilter}
        >
          {statusFilterItems.map((status) => {
            return (
              <MenuItem key={status.value} value={status.value}>
                <MopIcon name={status.name} size={32} />
              </MenuItem>
            )
          })}
        </PageListFilter>
        <PageListFilter label={t('oauthLink.label.type')} name="typeFilter" value={typeFilter} onChange={changeFilter}>
          {Object.keys(AccountType).map((accountType) => {
            return (
              <MenuItem key={accountType} value={accountType}>
                <TransText as="span" i18nKey={`oauthLink.label.accountType.${accountType}`} />
              </MenuItem>
            )
          })}
        </PageListFilter>
        <PageListFilter
          label={t('oauthLink.label.media')}
          name="mediaFilter"
          value={mediaFilter}
          onChange={changeFilter}
        >
          {MopMedia.map((media: string) => {
            return (
              <MenuItem key={media} value={media} className="mediaFilter-item">
                <MediaIcon mediaType={media} size={24} />
                <TransText as="span" i18nKey={`common.code.media.${media}`} />
              </MenuItem>
            )
          })}
        </PageListFilter>
        <PageListFilter
          label={t('oauthLink.label.currency')}
          name="currencyFilter"
          value={currencyFilter}
          onChange={changeFilter}
        >
          {Object.keys(AdvertiserCurrencyCode).map((currency) => {
            return (
              <MenuItem key={currency} value={currency} className="currencyFilter-item">
                <TextIcon code={currency} size={24} />
                <TransText as="span" i18nKey={`common.label.currency.${currency}`} />
              </MenuItem>
            )
          })}
        </PageListFilter>
      </PageListFilterGroup>
      <PageListHeader
        buttonLabel={t('oauthLink.label.button.new')}
        needAuth={false}
        placeHolder={t('oauthLink.label.placeholder.account')}
        handleCreate={openCreateModal}
        handleSearch={handleSearch}
        gtmId="data-connect-create-click"
      />

      <FixedLayoutTable
        id="connect-oauth-table"
        tableType="list-table"
        columns={allColumns}
        data={sortBy(filteredItmes.slice((page - 1) * rowsPerPage, page * rowsPerPage), 'authId').reverse()}
        localization={{ body: { emptyDataSourceMessage: t('common.message.list.noData') } }}
      />
      {OauthList.length > 0 && (
        <TablePagination
          totalCount={filteredItmes.length}
          page={page}
          rowsPerPage={rowsPerPage}
          onPageChange={(page) => setPage(page)}
          onRowsPerPageChange={(rowsPage) => setRowsPerPage(rowsPage)}
        />
      )}
      <ConnectDataModal
        open={showConnectDataModal}
        onClose={() => setConnectDataModal(false)}
        callback={getOAuthList}
      />

      {showMetricsModal && (
        <CreateMetricsModal
          {...modalData}
          open={showMetricsModal}
          callback={getOAuthList}
          onClose={() => setMetricsModal(false)}
        />
      )}
      {showEditApiModal && (
        <EditOauthApiModal {...modalData} open={showEditApiModal} onClose={() => setEditApiModal(false)} />
      )}
      {showCommerceAccountModal && (
        <CommerceAccountDetailModal
          {...modalData}
          open={showCommerceAccountModal}
          onClose={() => setCommerceAccountModal(false)}
          refetchOAuthList={getOAuthList}
        />
      )}
    </div>
  )
}

export default ConnectOauthPage
