export const toCamelCase = (str: string): string => {
  const regExp = /[^a-zA-Z0-9]+(.)/g;

  return str.toLowerCase().replace(regExp, (m, chr) => chr.toUpperCase());
};

export function getOnlyCapitalizedFirstLetter(str: string): string {
  return str.charAt(0).toUpperCase();
}

export function capitalizeFirstLetter(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

/**
 * Strips HTML tags from a string
 * @param html - The string containing HTML tags
 * @returns The string with HTML tags removed
 */
export function stripHtmlTags(html: string): string {
  return html.replace(/<[^>]*>/g, '');
}