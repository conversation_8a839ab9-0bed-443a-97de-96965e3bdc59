.CreationCampaignPage {
    display: flex;
    box-sizing: border-box;
    height: calc(100vh - 125px);
    width: 100%;
    position: relative;
  
    .left-site-create-new-campaign {
      padding: 20px 30px 6px 30px;
      width: 80%;
      display: flex;
      flex-direction: column;
    }
  
    .right-site-create-new-campaign {
      padding: 40px 24px 90px 24px;
      max-height: calc(100vh - 125px);
      width: 20%;
      min-width: 420px;
      background-color: #f9f9fb;
    }
  
    .mop-input {
      width: 100%;
      height: 42px;
      border-radius: 4px !important;
  
      fieldset {
        border: 1px solid #efefef;
      }
    }
  
    .view-selected-products {
      font-size: 14px;
      color: #7e7e7e;
    }
  
    .MuiOutlinedInput-input {
      padding: 8px !important;
    }
  
    .text-field-bg-white {
      & .MuiInputBase-root {
        background-color: #fff;
        box-shadow: '0 2px 6px rgba(0,0,0,0.15)';
        border: none;
      }
    }
  }