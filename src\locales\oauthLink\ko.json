{"label": {"currency": "통화", "media": "매체", "type": "유형", "oauthStatus": "연동", "accountType": {"MEDIA": "매체", "ANALYTICS": "전환툴​"}, "placeholder": {"account": "계정ID / 계정명"}, "button": {"new": "신규연동", "naverCommerce": {"linked": "커머스 계정 연동 완료", "notLinked": "커머스 계정 연동"}}, "list": {"connect": "연동", "accountType": "유형", "media": "매체", "accountId": "계정 ID", "accountName": "계정명", "currencyCode": "통화", "metrics": "전환값", "etc": "비고", "utmOrCommerce": "UTM / 커머스 연동"}}, "etcCode": {"NEED_UNITS": "유닛 설정 필요", "ON_SET": "연동중", "CAMPAIGN_COLLECT_FAIL": "연동 실패", "INVALID_API_KEY": "API에러", "EXCEED_MASTER_REPORT": "마스터 레포트 삭제", "AUTH_REQUESTED": "초대 예정", "AUTH_INVITED": "초대 수락 필요", "AUTH_ERROR": "연동 실패", "AUTH_COMPLETED": ""}, "metric": {"selectable": "설정 가능 전환값", "selected": "선택 전환값"}, "toast": {"maxAppsflyer": "앱스플라이어 계정 당 앱 최대 10개 까지 연동할 수 있습니다. 운영 담당자에게 연락주세요.", "validation": {"apiToken": "API Token을 입력해주세요.", "appName": "App Name을 입력해주세요.", "accessLicense": "Access License를 입력해주세요.", "secretKey": "Secret Key를 입력해주세요.", "customerId": "Customer ID 를 입력해주세요.", "required": "모든 양식을 입력해주세요.", "accountName": "Account Name을 입력해주세요."}, "metrics": {"ga4": {"setEventFail": "필수 전환값을 제외하고 최대 10개까지 설정할 수 있습니다.", "invalidEvent": "영문, 숫자, _로만 이루어진 전환값만 설정이 가능합니다. 한글로 설정된 전환값은 설정이 불가합니다.", "saveConfirm": "전환 Event 순서는 최초 연동 시에만 설정되고<br/>이후 연동 On/Off 시에는 수정할 수 없습니다.<br/>그래도 저장하시겠습니까?"}, "saveConfirm": "전환값은 최초 1회만 설정이 가능하며,<br/>추가/삭제/수정이 불가능 합니다.<br/>연동을 시작하시겠습니까?"}, "editApi": {"validation": {"apiToken": "API Token을 입력해주세요.", "appName": "App Name을 입력해주세요.", "accessLicense": "Access License를 입력해주세요.", "secretKey": "Secret Key를 입력해주세요."}, "response": {"success": "API Key가 변경되었습니다.", "fail": "API Key 변경에 실패하였습니다. 시스템 담당자에게 문의주시기 바랍니다.", "invalidApiKey": "유효하지 않는 계정입니다. 정확하게 입력해주세요."}}, "GOOGLE": {"FAIL": "연동 요청에 실패하였습니다. 시스템 담당자에게 문의주시기 바랍니다.", "SUCCESS": "연동 요청에 성공하였습니다."}, "FAIL": "계정 연동에 실패하였습니다. 시스템 담당자에게 문의주시기 바랍니다.", "SUCCESS": "계정 연동에 성공하였습니다.", "ACCOUNT_ID_ALREADY_IN_USE": "동일한 계정ID가 이미 연동되어있습니다. 운영 담당자에게 연락하세요.", "DUPLICATE_ACCOUNT": "이미 등록된 계정입니다. MOP 관리자에게 문의해주세요.", "INVALID_API_KEY": "유효하지 않는 계정입니다. 정확하게 입력해주세요.", "INVALID_CUSTOMER_ID": "계정ID 확인 후 다시 시도해주세요. 반복 발생시 운영 담당자에게 연락주세요.", "INVALID_ACCOUNT_ID": "계정ID 확인 후 다시 시도해주세요. 반복 발생시 운영 담당자에게 연락주세요.", "CAMPAIGN_COLLECT_ERROR": "매체 오류로 캠페인 정보 수동 수집에 실패하였습니다. 6시간 이내 자동으로 수집합니다.", "EMPTY_MEDIA_EMAIL": "연동 가능한 광고 계정이 없습니다. 비즈니스 계정으로 연동해주세요.", "searchEmailSuccess": "조회가 완료되었습니다.", "searchEmailFail": "Email조회에 실패하였습니다. 정확한 Email을 입력해주세요.", "noDataFound": "NO DATA FOUND", "searchFail": "조회에 실패하였습니다. 시스템 담당자에게 문의주시기 바랍니다.", "updateUseYnFail": "연동 설정 변경에 실패하였습니다. 시스템 담당자에게 문의주시기 바랍니다.", "exeedNumberEvent": "필수 전환값을 제외하고 최대 10개까지 설정할 수 있습니다.", "enrollMetricSuccess": "전환값을 설정하였습니다.", "WRONG_SECRET_KEY": "연동에 실패했습니다. 다시 한번 시도해주세요.", "NAVER_COMMERCE_INTEGRATION_FAIL": "연동에 실패했습니다. 다시 한번 시도해주세요.", "FORBIDDEN_IP": "연동에 실패했습니다. 내 스토어 애플리케이션의 API 호출 IP를 확인해주세요.", "DUPLICATE_CLIENT_ID": "이 계정은 이미 등록되었습니다."}, "title": {"NAVER": "NAVER", "AIRBRIDGE": "AIRBRIDGE", "CRITEO": "CRITEO", "META": "META", "KAKAO": "KAKAO", "GOOGLE": "GOOGLE ADS", "GA": "GA", "GA4": "GA4", "APPSFLYER": "APPSFLYER", "NAVER-COMMERCE": "NAVER 커머스 계정 연동"}, "path": {"NAVER": {"title": "정보 확인 경로", "content": "네이버 검색광고 플랫폼 <span class='highlight'>로그인 > 광고 시스템 > 도구 > API 사용 관리</span>"}, "AIRBRIDGE": {"title": "정보 확인 경로", "content": "Airbridge <span class='highlight'>로그인 > Settings > Tokens</span>"}, "META": {"title": "광고계정 ID(account ID) 확인 경로", "content": "메타 비즈니스 플랫폼 <span class='highlight'>로그인 > 광고 관리자 > 계정 드롭 다운</span>"}, "GOOGLE": {"title": "CID 확인 경로", "content": "구글 Ads <span class='highlight'>로그인 > 광고 계정 ID(CID) 확인</span>"}, "CRITEO": {"title": "Permission 확인 경로", "content": "<span class='highlight'>크리테오 Consent(consent.criteo.com) 로그인> Consent Dashboard 확인정보 확인 경로</span>"}, "APPSFLYER": {"title": "API 토큰 확인 경로", "content": "<span class='highlight'>앱스플라이어 로그인> 보안센터 > 앱스플라이어 토큰 관리</span>"}, "NAVER-COMMERCE": {"message": "추가적인 MOP 기능(MOP 캠페인 생성, 통합 리포트 등)을 이용하시려면, 커머스 계정을 연동해주세요.", "title": "정보 확인 경로", "content": "네이버 커머스 API 센터 <span class='highlight'> 로그인 > 내 스토어 애플리케이션</span>"}}, "notice": {"NAVER": "<li>계정명은 MOP에서 사용하는 이름으로, 일반적으로 네이버에 등록한 계정명을 입력합니다.</li><li>네이버의 API 사용관리에서 제공하는 “계정 ID”, “엑세스라이선스”, “비밀키”는 연동 정보가 변경되는 경우 MOP와의 연동이 중단될 수 있습니다.</li><li>연동 요청 전, Master Report 수가 90개 이하인지 확인합니다. 안정적인 데이터 연동을 위해, MOP 사용 기간에 Master Report 수를 90개 이하로 유지합니다.​</li>", "META": "<li>MOP 메타 광고 계정: <EMAIL> 으로 권한을 부여해주세요.</li><li>MOP에서 연동 요청하신 계정 ID로 보낸 초대를 수락해야 연동이 완료됩니다.</li>", "GOOGLE": "<li>MOP에 연동할 광고 계정 ID (MCC ID가 아닌 하위 계정 ID, CID)를 입력해야 합니다.</li><li>MOP의 구글 광고 계정(<EMAIL>)에서 입력한 CID로 하위계정 설정을 요청합니다.</li><li>입력한 CID로 보낸 요청을 수락하면 연동이 완료됩니다.</li>", "CRITEO": "<li>Consent Dashboard에서 연동하고자 하는 광고의 'Permissions'가 'Authorized' 상태인지 확인합니다.</li><li>크리테오 로그인 후 Portfolio Access에서 MOP에 연동할 광고 선택 후 Approve 합니다. 연동에 어려움이 있을 경우, <a href='https://support.mop.co.kr/data_connect'>크리테오 연동 방법(서포트 링크 연결)</a>을 참고하세요</li><li>MOP 연동시 통화는 원(KRW)으로 설정됩니다. 달러(USD) 설정이 필요한 경우 <a href='mop(@lgcns.com'>mop(@lgcns.com</a>으로 문의해주세요.</li>", "APPSFLYER": "<li>앱스플라이어 데이터 연동은 Pro 플랜에서만 지원됩니다. Basic/Lite 플랜은 데이터 연동만 가능하며, 애드써클 유닛 설정은 제공되지 않습니다.</li>", "AIRBRIDGE": "<li>에어브릿지 데이터 연동은 Pro 플랜에서만 지원됩니다. Basic/Lite 플랜은 데이터 연동만 가능하며, 애드써클 유닛 설정은 제공되지 않습니다.</li>", "NAVER-COMMERCE": "<li>네이버의 API 사용관리에서 제공하는 “계정 ID”, “엑세스라이선스”, “비밀키”는 연동 정보가 변경되는 경우 MOP와의 연동이 중단될 수 있습니다.</li><li>애플리케이션 ID와 애플리케이션 시크릿 정보를 확인할 수 없는 경우, 애플리케이션 등록을 먼저 진행해주세요</li>애플리케이션 등록 경로 | 네이버 커머스 API 센터 로그인 > 내 스토어 애플리케이션 > 새 애플리케이션 등록"}, "form": {"NAVER": {"label": {"customerId": "계정 ID*", "customerName": "계정명*", "accessLicense": "엑세스라이선스*", "secretKey": "비밀키*"}, "placeholder": {"customerId": "계정 ID", "customerName": "MOP에서 해당 계정을 관리할 이름을 입력하세요", "accessLicense": "엑세스라이선스", "secretKey": "비밀키"}}, "AIRBRIDGE": {"label": {"appName": "앱 이름 (Unique ID)*", "apiToken": "API 토큰*"}, "placeholder": {"appName": "앱 이름 (Unique ID)", "apiToken": "API 토큰"}}, "GOOGLE": {"label": {"mediaLoginEmail": "Google Email", "mediaAccountId": "CID"}, "placeholder": {"mediaLoginEmail": "Google Email", "mediaAccountId": "CID"}}, "CRITEO": {"label": {"criteoEmail": "Crite<PERSON>"}, "placeholder": {"criteoEmail": "Crite<PERSON>"}}, "APPSFLYER": {"label": {"accountName": "계정명*", "apiToken": "API 토큰*"}, "placeholder": {"accountName": "MOP에서 해당 계정을 관리할 이름을 입력하세요", "apiToken": "API 토큰"}}, "submit": {"NAVER": "CONNECT", "AIRBRIDGE": "CONNECT", "CRITEO": "CONNECT", "GOOGLE": "연동 요청", "APPSFLYER": "CONNECT", "NAVER-COMMERCE": "CONNECT"}, "NAVER-COMMERCE": {"label": {"clientName": "계정명*", "clientId": "애플리케이션 ID*", "secretKey": "애플리케이션 시크릿*", "dataSolutionClientId": "데이터 솔루션 애플리케이션 ID*", "dataSolutionSecretKey": "데이터 솔루션 애플리케이션 시크릿*"}, "placeholder": {"clientName": "MOP에서 해당 계정을 관리할 이름을 입력하세요", "clientId": "애플리케이션 ID", "secretKey": "애플리케이션 시크릿", "dataSolutionClientId": "데이터 솔루션 애플리케이션 ID", "dataSolutionSecretKey": "데이터 솔루션 애플리케이션 시크릿"}}}, "tooltip": {"selectableMetrics": {"title": "설정 가능 전환값 안내​​", "content": {"0": "각 매체에서 제공하는 전환값을 연동하여 보여줍니다. ​", "1": "※ 전환툴 및 계정ID를 기준으로 설정 가능한 전환값이 달라집니다. 자세한 내용은 운영담당자와 상의하세요.", "2": "운영담당자: <EMAIL>​"}}, "conect": {"title": "연동 안내​", "content": {"0": "전환값을 설정하지 않아 연동 ON/OFF가 불가능합니다.​", "1": "연동이 OFF 된 상태로 ON 할 수 있습니다.", "2": "연동이 ON 된 상태로 OFF 할 수 있습니다.​"}}, "currency": {"title": "통화 안내​", "content": {"0": "매체에 설정된 계정ID별 통화를 연동하여 보여줍니다. ​", "1": "※ MOP는 애드써클별 통화를 설정하고, 계정ID의 통화와 일치하는 경우에만 유닛을 생성할 수 있습니다.", "2": "※ 원화 또는 달러 외의 통화를 사용하는 광고의 경우 <span class='error'>운영 담당자에게 연락해주세요.</span>", "3": "매체에서 계정별 통화 설정이 가능한 경우​", "4": "구글, 메타", "5": "운영담당자: <EMAIL>​"}}, "metrics": {"title": "전환값 설정 안내​", "content": {"0": "계정ID별 전환값 설정 후 데이터 연동 및 수집을 진행합니다. ​", "1": "※ 전환값은 최초 1회만 설정 가능하며, 추가/삭제/수정이 불가능합니다.", "2": "※ 전환값은 최대 10개까지만 설정 가능합니다.", "3": "※ 설정한 전환값 중 1개를 선택하여 Unit의 전환값을 설정할 수 있습니다.", "4": "계정별 전환값 설정이 필요한 경우", "5": "매체: 구글, 메타", "6": "전환툴: GA4, 에어브릿지​"}}, "utmOrCommerce": {"title": "UTM 및 커머스 계정 연동 설정 안내​", "content": {"0": "UTM 설정​", "1": "전환툴을 성과 분석 툴로 활용하시는 경우, 계정ID별 UTM 구조 입력 후 데이터 연동 및 수집을 진행합니다.", "2": "※ MOP가 각 키워드/소재 단위로 성과를 인식할 수 있는 UTM 구조가 셋팅되어 있어야 합니다.", "3": "※ MOP가 인식할 수 없는 구조이거나, 잘못된 Rule로 셋팅한 경우 전환 최적화가 지원되지 않아 UTM 수정이 필요할 수 있습니다.", "4": "계정별 전환값 설정이 필요한 경우", "5": "전환툴: GA4, 에어브릿지, 앱스플라이어", "6": "커머스 계정 연동", "7": "네이버 계정의 경우, 네이버 커머스 계정을 추가적으로 연동하여 추가적인 기능을 이용할 수 있습니다."}}, "etc": {"title": "비고 메시지 안내​", "content": {"0": {"type1": "연동중:", "detail1": "매체로부터 데이터를 연동하고 있습니다.", "type2": "연동실패:", "detail2": "매체로부터 데이터를 연동하는데 실패했습니다. <span class='error'>(운영 담당자에게 연락해주세요)​</span>"}, "1": {"title": "네이버의 경우", "type1": "API에러:", "detail1": "네이버에서 API 키 에러가 발생하여 데이터 연동이 중단되었습니다. 엑세스라이선스, 비밀키를 수정해주세요.​", "type2": "마스터 레포트 삭제:", "detail2": "네이버의 마스터 레포트가 100개를 초과하여 데이터 연동이 중단되었습니다. 마스터 레포트 개수를 90개 이하로 유지해주세요."}, "2": {"title": "구글의 경우", "type1": "초대 예정:", "detail1": "MOP의 구글 광고 계정에서 입력한 CID로 초대를 보냅니다.", "type2": "초대 수락 필요:", "detail2": "입력한 CID로 보낸 초대를 수락해주세요. ​"}, "3": "운영담당자: <EMAIL>​"}}, "forPro": {"title": "전환툴", "content": ["에어브릿지 및 앱스플라이어 연동은<br/>Pro 플랜에서 제공됩니다.", "※ Basic 및 Lite 플랜은 데이터 연동은 가능하지만,<br/>애드써클 유닛은 등록할 수 없습니다."]}, "naverCommerce": {"title": "내 스토어 애플리케이션 등록", "content": "NAVER 커머스 계정 연동을 위해서는 내 스토어 애플리케이션에 MOP와 API데이터솔루션이 등록되어 있어야 합니다.<br/>만약 MOP와 API데이터솔루션이 등록되어 있지 않은 경우, 아래 안내에 따라 애플리케이션 등록을 먼저 진행해주세요.", "registerMOP": {"title": "내 스토어 애플리케이션에 MOP 등록하기", "step": {"1": "1단계: 네이버 커머스 센터 로그인", "2": "2단계: '내 스토어 애플리케이션'으로 이동", "3": "3단계: '새 애플리케이션 등록' 클릭", "4": "4단계: 'API 호출 IP' 필드에 아래 IP 주소 입력", "5": "5단계: 발급받은 애플리케이션 ID와 시크릿 확인"}}, "registerAPI": {"title": "내 스토어 애플리케이션에 API데이터솔루션 등록하기​", "step": {"1": "1단계: 네이버 '커머스 솔루션 마켓' 로그인​", "2": "2단계: 데이터 분석 카테고리의 'API데이터솔루션(통계)' 추가​", "3": "3단계: 네이버 커머스 센터 로그인​", "4": "4단계: '내 스토어 애플리케이션'으로 이동​", "5": "5단계: 'API데이터솔루션(통계)'에 대해 발급받은 애플리케이션 ID와 시크릿 확인​"}}}}, "createModal": {"title": "데이터 연동"}, "commerceAccountModal": {"title": "커머스 계정", "table": {"title": "연동된 커머스 계정", "accountName": "계정명", "integratedDate": "연동일", "delete": "삭제"}, "deleteConfirm": "정말로 이 계정을 삭제하시겠습니까?", "notice": {"title": "커머스 계정 연동 및 취소 시 확인하세요", "content1": "MOP는 업계 표준과 데이터 보안 관련 법률을 준수하며, 모든 데이터는 광고주 단위로 안전하게 보호되고 철저히 관리합니다.​", "content2": "데이터 연동은 사용자의 선택과 책임 하에 이루어지며, 연동된 데이터의 이관이나 삭제는 연동을 설정한 당사자가 직접 관리해야하 합니다."}, "button": {"addIntegration": "커머스 계정 연동"}}}