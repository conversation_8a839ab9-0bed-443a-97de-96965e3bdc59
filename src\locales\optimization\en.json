{"label": {"list": {"all": "All", "createNew": "Create", "bidYn": "ON/OFF", "status": "Status", "mediaType": "Media", "optimizationId": "Opt ID​", "optimizationName": "Opt Name​", "budgetVariance": "Budget Variance", "optDate": "Opt Date", "estimatedImpact": "Estimated Impact", "bidStartDate": "Start Date", "bidEndDate": "End Date", "dailyBudget": "Daily Budget​", "optimizationGoal": "Opt Goal​", "createdDateTime": "Created Date​", "errorStatus": "Notes", "adType": "Ad Type", "negativeKeyword": "Negative Keywords", "adgroupsCount": "# of Ad Groups"}, "button": {"previous": "PREVIOUS", "continue": "CONTINUE", "done": "DONE", "back": "Back to List"}, "placeholder": {"optimizationName": "Opt Name", "campaign": "Campaigns"}, "budgetOpt": {"list": {"status": {"SETTING": "Configuring", "RUNNING": "Running", "FINISHED": "Completed", "ERROR": "Error"}, "performImpact": {"HIGH": "Highly Recommended", "NORMAL": "Recommended", "LOW": "Potential Impact"}, "create": {"success": "The optimization setting has been created.", "failed": "Failed to create the optimization setting. Please try again."}}, "createOpt": {"title": "Optimization Name", "placeholder": "Enter Optimization Name"}, "deleteOpt": {"confirm": "Do you want to delete the optimization setting?", "success": "The optimization setting has been deleted.", "failed": "Failed to delete the optimization setting. Please try again."}, "editOpt": {"success": "The optimization setting name has been updated.", "failed": "Failed to update the optimization setting name. Please try again."}, "stepper": {"select": "Select Media/Campaigns", "setup": "Set-up Options", "result": "Check Recommendation"}, "info": {"available": "Media/Campaigns eligible for optimization", "unavailable": "Media/Campaigns eligible for optimization, with analyzing activated upon meeting conditions", "linked": "Media/Campaigns linked to MOP within the past 3 days", "factor": "※ <b>Optimization Eligibility Criteria</b>", "active": "Campaign must be active (Status: On)", "running": "Must have performance data for at least one of the past 3 days"}, "config": {"optimizationGoal": "Optimization Goal", "budgetVariance": "Budget Variance per Campaign", "fixingRatio": "Lock Budget Allocation by Media", "attributionType": "Apply Attribution Analysis", "attributionTarget": "Attribution Goal"}, "result": {"label": {"date": "Opt Date", "goal": "Optimization Goal", "variance": "Budget Variance per Campaign", "ratio": "Lock Budget Allocation by Media", "attribution": "Apply Attribution Analysis", "WeeklySpending": "Past Weekly Average Ad Spend", "spending": "Spending", "recommendation": "Recommended Budget", "budget": "Budget", "estimatedImpact": "Estimated Impact", "avgImpact": "Avg. Impact", "showVariance": "Show % Change"}, "table": {"last2weekes": "Last 2 Weeks", "suggest": "Recommended Budget", "variance": "% Change", "estimatedImpact": "Estimated Impact (Next 7 Days)", "weeklySpending": "Weekly Avg<br/>Ad <PERSON>d", "spending": "Ad Spend(%)", "weeklyBudget": "Weekly<br/>Budget", "budget": "Budget(%)"}, "message": {"optimizing": "MOP is optimizing the budget across selected campaigns.", "error": "An error occurred during optimization.<br/>Please contact the operations team."}}, "validation": {"campaign": "Please select at least one campaign to optimize.", "kpis": "Please select at least one optimization goal."}, "tooltip": {"status": {"title": "Status Message Guide", "contents": [{"term": "ⓘ Configuring:", "define": "The optimization item is being configured."}, {"term": "ⓘ Running:", "define": "Optimization is currently being executed on the registered optimization item."}, {"term": "ⓘ Completed", "define": "Optimization has been completed and you can check the optimization results."}, {"term": "ⓘ Error:", "define": "An error occurred during the optimization process for the registered optimization item. <span class='error'>(Please contact support.)</span>"}], "subscript": "Support: <EMAIL>"}, "optimizationId": {"title": "Optimization ID Copy Guide", "contents": ["You can copy the registered optimization item."]}, "engineRunDate": {"title": "Optimization Reference Date Guide", "contents": ["Results can be checked based on the most recent date the optimization was executed for the registered optimization item."]}, "performImproveRate": {"title": "Performance Impact Prediction Guide", "contents": ["Displays predicted performance impact when reallocating budgets across media or campaigns based on optimization results.", "A significant increase in performance is predicted compared to the budget change, so it is highly recommended to adjust the budget according to the optimization results.", "An increase in performance is predicted compared to the expected change, so adjusting the budget according to the optimization results is recommended.", "Performance is predicted to increase when changing the budget, and it is recommended to review the optimization results."]}, "targetCampaign": {"title": "Media/Campaign Selection", "contents": ["Campaign information set on the media is linked and displayed, and you can select the media/campaign to proceed with budget reallocation optimization.", "※ All campaign types are subject to optimization, and campaigns registered on the media are automatically reflected within 4 hours.", "※ New campaigns cannot be set up in MOP.", "Optimization settings can be applied to media/campaigns eligible for optimization.", "Optimization settings can be applied to media/campaigns operating less than 2 weeks or with insufficient performance, but optimization will proceed only after conditions are met.", "Media/campaigns linked within 3 days in MOP."]}, "onAndOff": {"title": "ON/OFF Guide", "contents": ["The ON/OFF status of ads by campaign set on the media is linked and displayed.", "※ ON/OFF of campaigns cannot be controlled in MOP.", "※ Ads will show OFF status if the daily budget set by the media is fully exhausted."]}, "optimizationGoal": {"title": "Optimization Goal Selection", "contents": ["Select one or more optimization goals considering the objective of the ads being executed."]}, "budgetVariance": {"title": "Campaign Budget Variance Selection", "contents": ["Set the allowable budget variance based on the current campaign budget."]}, "contributionType": {"title": "Apply Attribution Analysis (Optional)", "contents": ["If you have selected GA as the analytics tool and set units, you can choose whether to apply attribution analysis.", "※ If attribution analysis is applied, ‘Conversions’ must be selected as an optimization goal."]}, "fixingRatio": {"title": "Fix Media Budget Ratio Selection (Optional)", "contents": ["Option to fix the budget allocation ratio for each media. When selected, the budget ratio for each media is fixed based on the existing spending amount, and budget is reallocated per campaign."]}, "attributionTarget": {"title": "Attribution Goal Selection", "contents": ["You can set the attribution goal by purchase count, GA goal sum, or overall (purchase count + GA goal)."]}, "spending": {"title": "Past Weekly Average Ad Spend Guide", "contents": ["Displays the weekly average ad spend (excluding VAT) for currently running ads based on past data.", "※ The weekly average ad spend is calculated based on the ad spend (excluding VAT) over the past 2 weeks by media and campaign.", "※ Depending on the data aggregation time, it is calculated based on ad spend up to the previous day or two days ago."]}, "recommend": {"title": "Recommended Budget Guide", "contents": ["Displays MOP’s recommended budget (excluding VAT) based on media/campaign budget reallocation optimization."]}, "estimated": {"title": "Performance Impact Prediction Guide", "contents": ["Displays the predicted advertising performance impact when reallocating budget by media/campaign based on optimization results.", "※ Only predicts the performance impact of the optimization goals set in Step 2. \"Avg. Impact\" refers to the average performance impact.", "※ Performance impact is calculated by comparing predicted results with MOP’s recommended budget to those without a budget change.", "※ The Spend Pacing menu predicts the performance impact across all linked accounts and optimization goals."]}}}, "SearchOptimizationDetailModal": {"modalTitle": {"sa": "Search Ad"}, "label": {"media": "Media", "bidPeriod": "Period", "targetAdgroups": "Campaign / Ad group", "setting": "Setting", "optimizationName": "Optimization Name", "dailyBudget": "Daily Budget", "dailyAverageSpending": "Historical Daily Average Spend", "optimizationGoal": "Optimization Goal", "CLICK": "Click", "CONVERSION": "Conversion", "ROAS": "ROAS", "KPIS": "Multi-Objective", "negativeKeywrods": "Negative Keywords(Optional)", "topRankImpressionBoosting": "Top rank impression boosting", "advancedSettingOptions": "Advanced Setting (Optional)", "recommended": "Recommended"}, "description": {"CLICK": "Generates a keyword bidding plan to <em>maximize clicks within the budget</em>.", "CONVERSION": "Generates a keyword bidding plan to <em>maximize conversions within the budget</em>.", "KPIS": "Generates a keyword bidding plan to <em>simultaneously optimize multiple ad kpis</em>.", "ROAS": "Generates a keyword bidding plan to <em>maximize revenue within the budget</em>."}, "keywordOrKeywordId": "Keyword / Keyword ID", "title": "Status Value", "new": "New", "modify": "Modify", "view": "View", "bidPeriod": "Bid Period", "bidStartDate": "Start Date", "bidEndDate": "End Date", "optimizationName": "Optimization Name", "dailyBudget": "Daily Budget", "conversionGoal": "Conversion Goal", "emptyConversion": "No Conversion Goal", "optimizationGoal": "Optimization Goal", "excludeKeyword": "Exclude Keywords from Bidding", "selectOption": "Please select", "targetAdgroups": "Campaign / Ad Group", "button": {"kpis": "Set Multi-KPI Optimization"}, "kpiType": {"IMPRESSIONS": "Impression", "CLICKS": "Click", "CTR": "CTR", "CPC": "Average CPC", "CPC_MAX": "Max CPC", "CONVERSIONS": "Conversion", "CVR": "Conversion Rate", "REVENUE": "Revenue", "CPA": "Average CPA", "ROAS": "ROAS", "LIMIT": "Limit (Optional)"}, "possibleBidding": "Campaigns/Ad groups <b>eligible for optimization</b>", "conditionalBidding": "Campaigns/Ad groups <b>eligible for optimization, with bidding activated upon meeting conditions</b>", "impossibleBidding": "Campaigns/Ad groups <b>not eligible for optimization</b>", "newBidding": "Campaigns/Ad groups <b>linked to MOP within the past 3 days</b>", "unit": "ad groups", "save": "SAVE", "vatExcluded": "(VAT excluded)", "vatIncluded": "(VAT included)", "update": "update", "dailyAverageSpending": {"min": "<b>Conservative</b> bidding", "avr": "<b>7-day average</b>", "max": "<b>Aggressive</b> bidding", "last7Days": "Last <b>7</b> days", "last14Days": "Last <b>14</b> days", "last30Days": "Last <b>30</b> days", "recommended": "Recommended"}, "tableHeader": {"targetAdgroups": "Campaign / Ad group", "onAndOff": "ON / OFF", "dailyBudget": "Daily Budget"}, "tooltip": {"optimizationName": {"title": "Set Optimization Name", "contents": ["Optimization name must be unique."]}, "dailyBudget": "<h1>Set Daily Budget</h1><div><div>Enter the average daily budget (VAT included) based on a 7-day period.</div><ul><li>We recommend setting the daily budget with reference to past daily ad spend—particularly the last 7 days.</li><li>Please enter a daily budget that is equal to or lower than the daily budget set in the ad platform.<br>Even if a higher amount is entered in MOP, actual ad spend will follow the daily cap set in the ad platform.</li><li>Daily budget must be between {{min}} and {{max}} (in increments of {{unit}}).</li><li>Actual daily spend may vary depending on day-of-week patterns.</li></ul></div><div><div><b>When the daily budget is exceeded</b></div><ul><li>The daily budget set in MOP is a safeguard to help prevent overspending, not a strict spending cap.</li><li>Actual spend may vary by ±20% from the entered budget.</li></ul></div><div><div><b>※ [Caution] Daily budget may be significantly exceeded in the following cases:</b></div><ul><li>-A seasonal event or issue causes higher-than-usual clicks</li><li>-Higher ad ranking results in more clicks than usual</li><li>-A very low daily budget is set while bids remain high</li><li>-A surge in clicks occurs due to other unexpected reasons</li></ul></div>", "dailyAverageSpending": "<h1>Historical Average Daily Spend</h1><div><div>Displays the historical average daily ad spend (excluding VAT) for the selected campaigns or ad groups.</div><div class='indent2'>※ Click the refresh button after changing the selected campaigns/ad groups to recalculate the average daily spend.</div><div class='indent2' hidden>※ Keywords set in Negative Keywords or Rank Targeting are excluded.</div><div class='indent2'>※ Calculated based on ad spend data from 1-2 days ago, depending on the reporting cycle.</div></div>", "optimizationGoal": {"title": "Select Optimization Goal", "contents": ["Select one goal based on your advertising objectives."]}, "negativeKeywrods": "<h1>Setting Negative Keywords (Optional)</h1><div><div>Search for keywords/keyword IDs you want to exclude from bidding, then move them to the right.</div></div><div class='indent2'>※ Hold Shift to select multiple consecutive items.</div><div class='indent2'>※ Double-click to add/remove items.</div>", "targetAdgroups": "<h1>Select Campaigns/Ad Groups</h1><div><div>Campaigns/ad groups set in the ad platform are synced and shown. Select the ones you want to optimize with a single budget.</div><div class='indent2'>※ Campaign types eligible for optimization are ‘Shopping Mall Product’ and ‘Product Catalog’ ads. Changes are reflected automatically 1–2 hours after creation in the ad platform.</div><div class='indent2'>※ New campaigns/ad groups cannot be created in MOP.</div></div><div><div class='indent3'><span class='select-mark-blue'></span>Campaigns/Ad groups<b>eligible for optimization</b>.</div><div class='indent3'><span class='select-mark-red'></span>Campaigns/Ad groups <b>eligible for optimization, with bidding activated upon meeting conditions</b>.</div><div class='indent3'><span class='select-mark-gray'></span>Campaigns/Ad groups <b>not eligible for optimization</b>.</div><div class='indent3'><span class='select-mark-new'></span>Campaigns/Ad groups <b>linked to MOP within the past 3 days</b></div></div>", "onAndOff": "<h1>ON/OFF Status</h1><div><div>Displays the ON/OFF status of ads for each campaign/ad group set on the media platform.</div><div class='indent2'>※ Cannot change ON/OFF status from MOP.</div><div class='indent2'>※ Displayed as OFF if the daily budget set on the media has been fully spent.</div></div>", "dailyBudgetAdgroups": "<h1>Daily Budget Info</h1><div><div>Displays the daily budget (VAT included) for each campaign/ad group as set on the media platform.</div></div>", "noOptTarget": "Campaigns and Ad groups will appear once data integration and verification are completed.", "topRankImpressionBoosting": "<h1>Top Rank Impression Boosting</h1><div><div>When selected, the algorithm aims to increase impressions for top ranks (1st–3rd), which may lead to exceeding the daily budget.</div></div>", "limit": {"title": "Set Avg. CPC/CPA Limits", "contents": ["Allows you to limit the average CPC and CPA for overall performance.", "※ Based on predicted performance for the day — actual values may differ.", "<b>If average CPC/CPA limit is exceeded</b><br>Note: This is not a per-keyword or creative setting. The system aims to achieve average CPC/CPA within the limit across all performance data, but results may vary."]}}}, "ShoppingOptimizationDetailModal": {"vatExcluded": "(VAT Excluded)", "vatIncluded": "(VAT Included)", "label": {"media": "Media", "bidPeriod": "Period", "targetAdgroups": "Campaign / Ad group", "setting": "Setting", "optimizationName": "Optimization Name", "dailyBudget": "Daily Budget", "dailyAverageSpending": "Historical Daily Average Spend", "optimizationGoal": "Optimization Goal", "CLICK": "Click", "CONVERSION": "Conversion", "ROAS": "ROAS", "KPIS": "Multi-Objective", "KPI_CLICK": "Click", "KPI_CONVERSION": "Conversion", "IMPRESSIONS": "Impressions", "REVENUE": "Revenue", "negativeAd": "Negative Ad(Optional)", "topRankImpressionBoosting": "Top rank impression boosting", "advancedSettingOptions": "Advanced Setting (Optional)", "recommended": "Recommended"}, "dailyAverageSpending": {"min": "<b>Conservative</b> bidding", "avr": "<b>7-day average</b>", "max": "<b>Aggressive</b> bidding", "last7Days": "Last <b>7</b> days", "last14Days": "Last <b>14</b> days", "last30Days": "Last <b>30</b> days", "recommended": "Recommended"}, "tableHeader": {"targetAdgroups": "Campaign / Ad group", "onAndOff": "ON / OFF", "dailyBudget": "Daily Budget"}, "tooltip": {"optimizationName": "<h1>Set Optimization Name</h1><div><div>Optimization name must be unique.</div></div>", "dailyBudget": "<h1>Set Daily Budget</h1><div><div>Enter the average daily budget (VAT included) based on a 7-day period.</div><ul><li>We recommend setting the daily budget with reference to past daily ad spend—particularly the last 7 days.</li><li>Please enter a daily budget that is equal to or lower than the daily budget set in the ad platform.<br>Even if a higher amount is entered in MOP, actual ad spend will follow the daily cap set in the ad platform.</li><li>Daily budget must be between {{min}} and {{max}} (in increments of {{unit}}).</li><li>Actual daily spend may vary depending on day-of-week patterns.</li></ul></div><div><div><b>When the daily budget is exceeded</b></div><ul><li>The daily budget set in MOP is a safeguard to help prevent overspending, not a strict spending cap.</li><li>Actual spend may vary by ±20% from the entered budget.</li></ul></div><div><div><b>※ [Caution] Daily budget may be significantly exceeded in the following cases:</b></div><ul><li>-A seasonal event or issue causes higher-than-usual clicks</li><li>-Higher ad ranking results in more clicks than usual</li><li>-A very low daily budget is set while bids remain high</li><li>-A surge in clicks occurs due to other unexpected reasons</li></ul></div>", "dailyAverageSpending": "<h1>Historical Average Daily Spend</h1><div><div>Displays the historical average daily ad spend (excluding VAT) for the selected campaigns or ad groups.</div><div class='indent2'>※ Click the refresh button after changing the selected campaigns/ad groups to recalculate the average daily spend.</div><div class='indent2' hidden>※ Keywords set in Negative Keywords or Rank Targeting are excluded.</div><div class='indent2'>※ Calculated based on ad spend data from 1-2 days ago, depending on the reporting cycle.</div></div>", "optimizationGoal": "<h1>Select Optimization Goal</h1><div><div>Select one goal based on the objective of your ad campaign.</div><div class='indent2'>※ For product catalog ads, ‘Conversion Optimization’ goal cannot be selected.</div></div>", "negativeAd": "<h1>Setting Negative Ad (Optional)</h1><div><div>Search for ads(creatives) you want to exclude from bidding, then move them to the right.</div></div><div class='indent2'>※ Hold Shift to select multiple consecutive items.</div><div class='indent2'>※ Double-click to add/remove items.</div>", "targetAdgroups": "<h1>Select Campaigns/Ad Groups</h1><div><div>Campaigns/ad groups set in the ad platform are synced and shown. Select the ones you want to optimize with a single budget.</div><div class='indent2'>※ Campaign types eligible for optimization are ‘Shopping Mall Product’ and ‘Product Catalog’ ads. Changes are reflected automatically 1–2 hours after creation in the ad platform.</div><div class='indent2'>※ New campaigns/ad groups cannot be created in MOP.</div></div><div><div class='indent3'><span class='select-mark-blue'></span>Campaigns/Ad groups<b>eligible for optimization</b>.</div><div class='indent3'><span class='select-mark-red'></span>Campaigns/Ad groups <b>eligible for optimization, with bidding activated upon meeting conditions</b>.</div><div class='indent3'><span class='select-mark-gray'></span>Campaigns/Ad groups <b>not eligible for optimization</b>.</div><div class='indent3'><span class='select-mark-new'></span>Campaigns/Ad groups <b>linked to MOP within the past 3 days</b></div></div>", "onAndOff": "<h1>ON/OFF Status</h1><div><div>The ON/OFF status of each campaign/ad group set in the ad platform is synced and displayed.</div><div class='indent2'>※ Campaign/ad group ON/OFF cannot be controlled from MOP.</div><div class='indent2'>※ If the daily budget set in the platform is exhausted, the campaign will also appear as OFF.</div></div>", "dailyBudgetAdgroups": "<h1>Daily Budget Information</h1><div><div>The daily budget (VAT included) set for each campaign/ad group in the ad platform is synced and displayed.</div></div>"}, "modalTitle": {"SHOPPING": "Shopping Mall", "CATALOG": "Product Catalog", "subtitle": "Shopping Ad"}, "selectAdTypeModal": {"title": "Select Ad Type", "subtitle": "Please select an Ad type", "description": "For Shopping Ads, the Shopping Mall Type and the Product Catalog Type must be set up separately.", "SHOPPING": "Shopping Mall Type", "SHOPPING_description": "image-based ad type promoting <b>products directly sold by the seller through their own store on naver shopping</b>", "CATALOG": "Product Catalog Type", "CATALOG_description": "image-based ad type promoting <b>product catalogs listed on Naver Shopping</b> by manufacturers and brand owners"}, "switchAdTypeModal": {"title": "Switch Ad Type", "description": "The changes you made will not be saved.<br><b>Do you want to switch?</b>", "goBack": "Go Back", "switch": "Switch"}, "negativeKeywordModal": {"title": "Negative keyword Setting", "subtitle": "Shopping Ad", "adtype": "AD TYPE", "optimizationName": "OPTIMIZATION NAME", "campaign": "CAMPAIGN", "adgroup": "AD GROUP", "adgroupTable": "Keyword Efficiency Analysis (Last 2 weeks)", "negativeTable": "Negative keyword", "negativeKeywordLimit": "(Current {{count}} / Max 70)", "adgroupTableColumn": {"productTitle": "AD", "keywordCount": "# Of keyword", "cost": "Cost", "impression": "Impression", "click": "Click", "conversion": "Conversion", "roas": "ROAS(%)", "ctr": "CTR(%)", "cvr": "CVR(%)", "cpc": "CPC", "cpa": "CPA"}, "adTableColumn": {"searchKeyword": "Keyword", "efficiency": "Efficiency", "cost": "Cost", "impressions": "Impression", "clicks": "Click", "conversions": "Conversion", "roas": "ROAS(%)", "ctr": "CTR(%)", "cvr": "CVR(%)", "cpc": "CPC", "cpa": "CPA"}, "toastMessage": {"emptyRawData": "결과가 0건입니다."}, "tooltip": {"date": "<h1>Analysis Date</h1><div><div>This indicates the date on which the keyword efficiency analysis was performed, based on performance data from the past 2 weeks.</div></div>", "efficiencyResult": "<h1>Keyword Efficiency Analysis</h1><div><div>Provides keyword efficiency analysis results for the ads(creatives) set in the optimization item, based on the optimization goal (impressions, clicks, conversions, revenue).</div><div class='indent2'>※ It is recommended to refer to the keyword efficiency analysis results when setting negative keywords.</div><div class='indent2'>※ Keyword Efficiency analysis results are updated daily based on performance over the past 2 weeks.</div></div>", "keywordSetting": "<h1>Negative Keyword Settings</h1><div><div>When you select an ad(creative), the negative keywords configured in the media platform will be displayed. You can add or remove negative keywords, then click the SAVE button to update the negative keyword settings in the media platform.</div><ul><li>-Negative keywords can be added or removed at the ad(creative) level, and clicking the SAVE button will apply the changes to the media.<br><span style='color:red'>-If you switch to a different campaign / ad group / ad(creative), changes will not be applied to the media.</span></li><li>-You can set up to 70 negative keywords per ad(creative.)</li></ul></div>", "keywordEfficiency": "<h1>Keyword Efficiency Info</h1><div><div>Keywords are listed in order of efficiency from lowest to highest for each ad creative.</div><div class='indent2'>※ You can set low-efficiency keywords as negative keywords to limit their exposure.</div></div>", "keywordCount": "<h1>Analyzed Keyword Count Info</h1><div><div>Shows the number of keywords analyzed for keyword efficiency per ad(creative).</div><div class='indent2'>※ Keyword efficiency analysis will not run if there is insufficient data from the past 2 weeks.</div></div>", "topRankImpressionBoosting": "<h1>Top Rank Impression Boosting Info</h1><div><div>The algorithm activates with the purpose of increasing impressions for top ranks (1st to 3rd) based on the selected optimization goal. It may exceed the set daily budget.</div></div>"}, "searchPlaceholder": "Search..."}, "description": {"CLICK": "Generates a keyword bidding plan to <em>maximize clicks within the budget</em><br>", "CONVERSION": "Generates a keyword bidding plan to <em>maximize conversions within the budget</em><br>", "KPIS": "Generates a keyword bidding plan to <em>simultaneously optimize multiple ad kpis</em>", "ROAS": "Generates a keyword bidding plan to <em>maximize revenue within the budget</em><br>"}, "possibleBidding": "Campaigns/Ad groups <b>eligible for optimization</b>", "conditionalBidding": "Campaigns/Ad groups <b>eligible for optimization, with bidding activated upon meeting conditions</b>", "isPro": "Runs on Naver’s recommended bid, optimization algorithm is auto-applied when conditions are met.", "impossibleBidding": "Campaigns/Ad groups <b>not eligible for optimization</b>", "newBidding": "Campaigns/Ad groups <b>linked to MOP within the past 3 days</b>"}}, "message": {"common": {"duplicateName": "The name you entered is already in use by another optimization setting.", "deleteConfirm": "Are you sure you want to delete this optimization item?"}, "searchOptimization": {"list": {"numberOfBidOptAdGroups": "Number of Bid Optimization Ad Groups", "noData": "No Data Found", "bidYn": "<div>Auto Bidding Guide</div><div><div class='bid-desc'><div class='bid-img bid-disable'></div><div>Auto bidding cannot be turned ON or OFF during In Review, Review Failed, or Ended states.</div></div><div class='bid-desc'><div class='bid-img bid-off'></div><div>Auto bidding is currently OFF and can be turned ON.</div></div><div class='bid-desc'><div class='bid-img bid-on'></div><div>Auto bidding is currently ON and can be turned OFF.</div></div><br/><div>Support: <EMAIL></div></div>", "status": "<div>Status Message</div><div><div><div>ⓘ In Review:</div><div>The optimization item is under review.</div></div><div><div>ⓘ Review Complete:</div><div>The review has been completed. Auto bidding can now be turned ON/OFF.</div></div><div><div>ⓘ Review Failed:</div><div>An error occurred during the review process. <span>(Please contact support.)</span></div></div><div><div>ⓘ Preparing:</div><div>Auto bidding is ON, but bidding has not started yet. The system is preparing to begin based on the configured schedule.</div></div><div><div>ⓘ Bidding:</div><div>Auto bidding is running based on the configured settings.</div></div><div><div>ⓘ Paused:</div><div>Auto bidding is turned OFF and paused.</div></div><div><div>ⓘ Ended:</div><div>The configured optimization bidding period has ended.</div></div><br/><div>Support: <EMAIL></div></div>", "errorStatus": "<div>Error Messages</div><div><div><div>Budget Shortage:</div><div>The Daily Budget is insufficient; bidding for low-performing keywords may not be properly executed. Please increase the budget.</div></div><div><div>Budget Excess:</div><div>The Daily Budget is high, which may lead to aggressive bidding. Maintaining this status is recommended during-high competition periods.</div></div><div><div>Optimization Error:</div><div>An error occurred due to other reasons. <span>(Please contact support.)</span></div></div><div><div>Bidding Error:</div><div>An error occurred during bidding process; may happen temporarily if certain keywords or ads are turned OFF by the media.<br><span>(Please contact support if it continues for more than 2 hours.)</span></div></div><div><div>Auto Bidding OFF:</div><div>Auto bidding has been turned OFF after 3+ days of continuous optimization or bidding errors. <span>(Please contact support.)</span></div></div><br/><div>Support: <EMAIL></div></div>"}, "dialog": {"deleteAlertTitle": "Delete Optimization Settings", "deleteAlertMessage": "Are you sure you want to delete the optimization settings?", "maxItem": {"title": "Number of Optimization Items You Can Register", "contents": ["Basic plan users can register up to <semibold>1 optimization item</semibold>.<br/>※ Search Ads and Shopping Ads are counted separately.<br/>※ Ended optimization items will be excluded.", "Pro plan users can register optimization items without limitations.", "Lite plan users can register up to 5 optimization items."]}}, "validation": {"overPeriodMax": "Please select a bid end date within 2 years from the start date.", "periodInvalid": "Please select a bid end date after the start date."}, "toast": {"emptyNegative": "No negative keywords/creatives have been set.", "updateBidYnFail": "Unable to change bid reflection setting. Please contact the system administrator.", "deleteFail": "Unable to delete. Please contact the system administrator.", "deleteSuccess": "Deleted successfully.", "impossibleDelete": "Auto bidding is currently reflected. Please turn it OFF to delete.", "impossibleEdit": {"bidYn": "Auto bidding is currently reflected. Please turn it OFF to edit.", "inspecting": "Editing is not allowed during review."}}, "tooltip": {"title": "Number of Ad Groups You Can Register", "contents": ["You can register up to <semibold>50 ad groups</semibold>.<br/>* Search Ads and Shopping Ads are counted separately.<br/>* Campaigns/Ad groups that have been deleted from the ad platform will be excluded.", "Pro plan users can register ad groups without limitations.", "Lite plan users can register up to 100 ad groups."]}}, "SearchOptimizationDetailModal": {"emptyGa": "No GA linkage information available. If you want to set conversion goals, please link through the \"Settings\" menu.", "duplicateAdgroup": "The selected ad group is already included in the bidding period of another optimization item.", "changeAdgroups": "If you change the campaign/ad group, the previously entered/saved negative keywords will be deleted.", "changeConversionGoal": "If the conversion goal is changed, previously set multi-goal optimization values will be deleted. Do you still want to change the conversion goal value?", "searchingKeywordIsEmpty": "No search results found.", "tableHeader": {"targetAdgroups": "Campaign/Ad Group", "onAndOff": "ON/OFF", "dailyBudget": "Daily Budget"}, "validation": {"keywordLength": "Negative keywords must be at least 2 characters long.", "emptyAdgroupIds": "Please select a Campaign/Ad Group before entering negative keywords.", "budgetMin": "Please enter a daily budget of at least {{min}}.", "budgetMax": "The maximum daily budget is {{max}}.", "budgetUnit": "Please enter the daily budget in units of {{unit}}.", "conversionGoal": "To enable conversion optimization, please select a conversion goal.", "kpis": "To enable multi-goal optimization, please first enter multi-goal optimization settings.", "bidPeriod": "The bid end date must be after the start date.", "endDate": "Duplicate ad group registration within the period. Please change the bid end date to an earlier date than the following: ", "maxCpc": "Please enter a bid amount between {{min}} and {{max}} KRW for Max CPC.", "adgroupCount": "{{plan}} plan allows up to {{num}} ad groups. Please deselect some and try again."}, "notPredictable": "<div style='font-size:14px'><span style='color:red'>Optimization errors occur</span> only when you select campaigns/ad groups <span style='color:red'>with less than 2 weeks of operation or insufficient performance</span>.<br/>Optimization and auto-bidding will proceed once the conditions are met.<br/>Do you want to save?</div>", "notMatchedConversion": "<div style='font-size:14px'>The selected campaigns<br/><span style='color:red'>have different configured unit conversion values.</span><br/>Do you want to proceed with optimization?</div>"}, "shoppingOptimization": {"list": {"numberOfBidOptAdGroups": "Number of Bid Optimization Ad Groups", "noData": "No Data Found", "noKeywordData": "No excluded keywords set.", "noAdIdData": "No negative keywords set", "bidYn": "<div>Auto Bidding Guide</div><div><div class='bid-desc'><div class='bid-img bid-disable'></div><div>Auto bidding cannot be turned ON or OFF during In Review, Review Failed, or Ended states.</div></div><div class='bid-desc'><div class='bid-img bid-off'></div><div>Auto bidding is currently OFF and can be turned ON.</div></div><div class='bid-desc'><div class='bid-img bid-on'></div><div>Auto bidding is currently ON and can be turned OFF.</div></div><br/><div class='indent2'></div></div>", "optimizationResult": "<div>Optimization Result Guide</div>", "status": "<div>Status Messages</div><div><div><div>ⓘ In Review:</div><div>The optimization item is under review.</div></div><div><div>ⓘ Review Complete:</div><div>The review has been completed. Auto bidding can now be turned ON/OFF.</div></div><div><div>ⓘ Review Failed:</div><div>An error occurred during the review process.<span>(Please contact support.)</span></div></div><div><div>ⓘ Preparing:</div><div>Auto bidding is ON, but bidding has not started yet. The system is preparing to begin based on the configured schedule.</div></div><div><div>ⓘ Bidding:</div><div>Auto bidding is running based on the configured settings.</div></div><div><div>ⓘ Paused:</div><div>Auto bidding is turned OFF and paused.</div></div><div><div>ⓘ Ended:</div><div>The configured optimization bidding period has ended.</div></div><br/><div>Support: <EMAIL></div></div>", "errorStatus": "<div>Error Messages</div><div><div><div>Budget Shortage:</div><div>The Daily Budget is insufficient; bidding for low-performing keywords may not be properly executed. Please increase the budget.</div></div><div><div>Budget Excess:</div><div>The Daily Budget is high, which may lead to aggressive bidding. Maintaining this status is recommended during-high competition periods.</div></div><div><div>Optimization Error:</div><div>An error occurred due to other reasons. <span>(Please contact support.)</span></div></div><div><div>Bidding Error:</div><div>An error occurred during bidding process; may happen temporarily if certain keywords or ads are turned OFF by the media.<br><span>(Please contact support if it continues for more than 2 hours.)</span></div></div><div><div>Auto Bidding OFF:</div><div>Auto bidding has been turned OFF after 3+ days of continuous optimization or bidding errors. <span>(Please contact support.)</span></div></div><br/><div>Support: <EMAIL></div></div>", "negativeKeyword": "<div>Negative Keywords Guide</div><div><div>You can set negative keywords to limit ad exposure based on the keyword efficiency analysis of each shopping ad optimization item.</div><ul><li>The analysis is updated daily using the past 2 weeks of data.</li><li>Negative Keywords cannot be set during the In Review or Review Failed states.</li>You can add or remove negative keywords at the ad(creative) level.</li></ul></div>", "adType": "<div>Ad Types Guide</div><div><div>For Shopping Ads, the Shopping Mall Type and the Product Catalog Type must be set up separately.</div></div>"}, "dialog": {"deleteAlertTitle": "Delete Optimization Setting", "deleteAlertMessage": "Are you sure you want to delete the optimization setting?", "maxItem": {"title": "Number of Optimization Items You Can Register", "contents": ["Basic plan users can register up to <semibold>1 optimization item</semibold>.<br/>※ Search Ads and Shopping Ads are counted separately.<br/>※ Ended optimization items will be excluded.", "Pro plan users can register optimization items without limitations.", "Lite plan users can register up to 5 optimization items."]}}, "validation": {"overPeriodMax": "The bid end date must be within 2 years after the start date.", "periodInvalid": "The bid end date must be after the start date."}, "toast": {"emptyNegative": "No excluded keywords or excluded creatives set.", "updateBidYnFail": "Cannot change bid reflection. Please contact the system administrator.", "deleteFail": "Unable to delete. Please contact the system administrator.", "deleteSuccess": "Deleted successfully.", "impossibleDelete": "Auto bidding is active. Please turn off auto bidding to delete.", "impossibleEdit": {"bidYn": "Auto bidding is active. Please turn off auto bidding to edit.", "inspecting": "Cannot edit while under review."}, "updatedOriginalNegativeKeyword": "Excluded keywords have been changed by the media, and previously entered/saved excluded keywords will be removed. Please try again.", "failedToGetRestrictKeyword": "Failed to retrieve excluded keywords due to a media error.", "failedToUpdateRestrictKeyword": "Failed to save due to a media error. Please try again.", "resetNegativeKeywords": "Excluded keywords have been cleared due to AD ID change."}, "tooltip": {"title": "Number of Ad Groups You Can Register", "contents": ["You can register up to <semibold>50 ad groups</semibold>.<br/>* Search Ads and Shopping Ads are counted separately.<br/>* Campaigns/Ad groups that have been deleted from the ad platform will be excluded.", "Pro plan users can register ad groups without limitations.", "Lite plan users can register up to 100 ad groups."]}}, "ShoppingOptimizationDetailModal": {"emptyGa": "No GA integration information found. Please go to the 'Settings' menu to connect if you want to set a conversion goal.", "duplicateAdgroup": "The selected ad group overlaps with another optimization item's bidding period.", "changeAdgroups": "Campaign/Ad group has been changed. Previously entered/saved negative keywords will be deleted.", "changeConversionGoal": "Changing the conversion goal will delete previously set multi-goal optimization values. Do you still want to change the conversion goal?", "searchingKeywordIsEmpty": "No search results found.", "validation": {"keywordLength": "Please enter at least 2 characters for the negative keyword.", "emptyAdgroupIds": "To enter negative keywords, please select a Campaign/Ad group first.", "budgetMin": "Please enter a daily budget of at least {{min}}.", "budgetMax": "Daily budget can be up to {{max}}.", "budgetUnit": "Please enter the daily budget in units of {{unit}}.", "conversionGoal": "To enable conversion optimization, please select a conversion goal.", "kpis": "To use multi-goal optimization, please enter the multi-goal optimization settings first.", "bidPeriod": "Please set the bid end date to be after the start date.", "endDate": "Duplicate ad group registration within the period. Please set the bid end date earlier than the following date:", "maxCpc": "Please enter a bid between {{min}} and {{max}} for Max CPC.", "adgroupCount": "{{plan}} plan allows up to {{num}} ad groups. Please deselect some and try again."}}}, "AdvancedOptions": {"label": {"averageCPC": "CPC", "averageCPA": "CPA", "maxCPC": "MAX CPC", "topRankImpressionBoosted": "Top Rank Impression Boosting", "addTopCpcYn": "Rank Boosting", "boostingRateLevel": "Budget Boosting", "minImps": "Clustering Level", "cpcReboot": "CPC Reboot", "advancedBidSetting": "Advanced Bid Settings", "bidBudgetLimit": "Set a bid budget limit.", "advancedAlgorithmSetting": "Advanced Algorithm Settings", "configMopAdvancedAlgorithm": "Configure MOP advanced algorithm.", "basic": "Standard", "bid": "Bid", "aggressive": "Aggressive", "tight": "Tight", "loose": "Loose"}, "tooltip": {"averageCPC": {"title": "Set Average CPC/CPA Limit", "contents": ["This feature sets a limit on the average CPC/CPA based on overall performance.", "It operates based on the performance forecast for the same day, so actual results may be higher or lower than the set value.", "The average CPC/CPA limit set in MOP is not applied at the individual keyword or ad(creative) level. It is designed to achieve an overall average CPC/CPA below the specified value, so please note that actual results may be higher or lower."]}, "averageCPA": {"title": "Set Average CPA Limit", "contents": ["This feature limits the average CPA based on the overall performance.", "※ It operates based on the predicted performance for the day, so the actual result may be higher or lower than the set value.", "※ The average CPA limit set in MOP is not applied at the individual keyword or creative level. It is an option that aims to achieve an average CPA across all performance results below the set value, but actual results may vary."]}, "maxCPC": {"title": "Set Max CPC Limit", "contents": ["The optimization algorithm adjusts bids within the configured maximum CPC limit to help ensure more stable ad performance.", "This setting will be applied in the next optimization cycle. For immediate application, please contact your consultant or support team.", "Available for Pro/Lite Plan Users Only."]}, "topRankImpressionBoosted": {"title": "Top Rank Impression Boosting", "contents": "The algorithm works with an additional objective of increasing impressions in the top (1st -3rd) ranks, on top of the selected optimization goal. As a result, the daily budget may be exceeded."}, "addTopCpc": {"title": "Rank Boosting", "contents": ["Use the Rank Boosting feature to gradually increase ad spend through the algorithm.", "Available for Pro/Lite Plan Users Only."]}, "boostingRateLevel": {"title": "Budget Boosting", "contents": ["Use the Budget Boosting feature to accelerate ad spend through the algorithm. It is recommended for new product launches or promotions.", "Available for Pro plan users only."]}, "minImps": {"title": "Clustering Level", "contents": ["In shopping ads, when multiple ads(creatives) share the same keyword, the algorithm adjusts the clustering intensity among them to control influence on dominant ads. Lower clustering intensity allows the algorithm to more granularly manage a wider range of ads.", "Available for Pro plan users only."]}, "exclusive": {"title": "Pro Plan Feature", "contents": ["The MOP’s advanced optimization features and latest algorithms are available as beta access for members with the highest optimization capability.", "For inquiries about using Pro plan features,<br/>please contact your consultant."]}, "cpcReboot": {"title": "CPC Reboot", "contents": ["Retrains bid strategies based on the latest trends to explore new opportunities for improving ad performance.", "※ The CPC Reboot feature cannot be used together with the Average CPC or CPA features. It can be used with Max CPC."]}}, "validation": {"cpcReboot": "The CPC Reboot feature cannot be used together with the Average CPC/CPA feature."}}}