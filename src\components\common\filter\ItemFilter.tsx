import SelectBottom from '@components/common/SelectBottom';
import { SelectDropdownIcon } from '@components/common/icon'
import { MenuItem } from '@material-ui/core';
import { useTranslation } from 'react-i18next';
import './ItemFilter.scss'

interface Props {
  name: string;
  value: any;
  width?: string;
  all?: boolean
  font?: number;
  dropdownSize?: number;
  onChange: (_event: React.ChangeEvent<{
    name?: string | undefined;
    value: unknown;
  }>) => void;
}

const ItemFilter = ({ children, value, name, onChange, width, font, dropdownSize, all = true }: React.PropsWithChildren<Props>) => {
  const { t } = useTranslation();
  const customStyle = { '--width': width, '--font-size': `${ font }px`, '--dropdown-width': `${ dropdownSize }px` }
  return (
    <div className="item-filter" style={customStyle as React.CSSProperties}>
      <SelectBottom
        displayEmpty
        name={name}
        value={value}
        onChange={onChange}
        MenuProps={{
          className: 'filter-options-popover',
          anchorOrigin: { vertical: 24, horizontal: 'left' }
        }}
        IconComponent={(props) => <SelectDropdownIcon {...props} size={dropdownSize} />}
      >
        { all && <MenuItem value={'ALL'}>{ t('common.label.filter.all') }</MenuItem> }
        { children }
      </SelectBottom>
    </div>
  )
}

export default ItemFilter