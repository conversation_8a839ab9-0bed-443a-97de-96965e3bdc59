import React, { ReactElement, useEffect, useState, useRef } from 'react'
import { Box, Dialog, IconButton, Table, TableRow, TableCell, MenuItem } from '@material-ui/core'
import { t } from 'i18next'
import './ShoppingOptimizationNegativeKeywordModal.scss'
import { useRecoilValue } from 'recoil'
import { advertiserState } from '@store/Advertiser'
import {
  getShoppingCampaignSearch,
  getShoppingAdgroupSearch,
  getShoppingKeywordEfficiency,
  getShoppingKeywordEfficiencyDetail,
  getShoppingKeywordEfficiencyDownload,
  getShoppingRestrictKeyword,
  updateShoppingRestrictKeyword
} from '@api/shoppingOptimization/ShoppingOptimization'
import { format } from 'date-fns'
import { convertStrToDate } from '@utils/DateUtil'
import { DateFnsFormat } from '@models/common/CommonConstants'
import {
  GetCampainSearchValueInfo,
  GetAdgroupSearchValueInfo,
  AnalysisResult,
  KeywordEfficiencyDetail
} from '@models/optimization/GetAdgroupsResponse'
import SelectBottom from '@components/common/SelectBottom'
import { ExpandLess } from '@material-ui/icons'
import { FixedLayoutTable, FixedLayoutColumn } from '@components/common/table'
import AddIcon from '@material-ui/icons/Add'
import RemoveIcon from '@material-ui/icons/Remove'
import { ReactComponent as LeftArrowIcon } from '@components/assets/images/icon_arrow_left.svg'
import { ReactComponent as RightArrowIcon } from '@components/assets/images/icon_arrow_right.svg'
import { objectCompare } from '@utils/CompareUtil'
import { useToast, useDialog } from '@hooks/common'
import { numberWithCommas } from '@utils/FormatUtil'

import InnerHtml from '@components/common/InnerHtml'
import AdviceTooltip from '@components/common/AdviceTooltip'
import { ReactComponent as AdviceMarkIcon } from '@components/assets/images/advicemark.svg'
import { MopIcon, TransButton } from '@components/common'
import { MOPIcon } from '@models/common'
import { ButtonUI } from '@models/common/UI'
import { StatusCode } from '@models/common/CommonResponse'

import { MopSearch } from '@components/common'
import useSearch from '@components/common/search/useSearch'
import tw from 'twin.macro'
import styled from '@emotion/styled'
import { FlexRowCenter } from '@components/layout'

const TableContainer = tw.div`mt-6 flex gap-8 overflow-auto w-full`
const TableHeader = tw.div`relative flex flex-col items-center justify-center bg-primary-black text-white p-2.5 gap-2.5`
const TooltipTextBox = tw.div`flex items-center gap-1`
const NegativeWrapper = tw.div`flex flex-col w-80 min-w-80`

export interface Props {
  optimizationId: number
  optimizationName: string
  saShoppingType: string
  onClose: (ok: boolean) => void
  open: boolean
}

interface Keyword {
  keyword: string
}

const Tooltip = ({ id, icon }: { id: string; icon?: any }) => {
  const FIXED_KEY = 'optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.tooltip'
  return (
    <AdviceTooltip
      id={`shopping-optimization-negative-keyword-tooltip-${id}`}
      title={<InnerHtml innerHTML={t(`${FIXED_KEY}.${id}`)} />}
      placement="bottom-start"
      arrow
    >
      <span className={`icon-tooltip icon-tooltip-${id}`}>{icon ?? <AdviceMarkIcon />}</span>
    </AdviceTooltip>
  )
}

type withTooltipProps = { id: string; icon?: any; children?: React.ReactNode }
const WithTooltip = ({ id, icon, children }: withTooltipProps) => {
  return (
    <span className="columns-withTooltip">
      <Tooltip id={id} icon={icon} />
      {children}
    </span>
  )
}

const ShoppingOptimizationNegativeKeywordModal: React.FC<Props> = ({
  optimizationId,
  optimizationName,
  saShoppingType,
  onClose,
  open
}: Props): ReactElement => {
  const advertiser = useRecoilValue(advertiserState)
  const [selectedCampaignId, setSelectedCampaignId] = useState<string>('')
  const [selectedAdgroupId, setSelectedAdgroupId] = useState<string>('')
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult[]>([])
  const [analysisDate, setAnalysisDate] = useState<string>('')
  const [detailResult, setDetailResult] = useState<KeywordEfficiencyDetail[]>([])
  const [negativeKeywords, setNegativeKeywords] = useState<string[]>([])
  const [savedNegativeKeywords, setSavedNegativeKeywords] = useState<string[]>([])
  const [openAdId, setOpenAdId] = useState<string>('')
  const [headerDropdown, setHeaderDropdown] = useState<string>('ctr')
  const [order, setOrder] = useState<any>({})
  const { openDialog } = useDialog()
  const { openToast } = useToast()

  // FIXME
  const [
    campaigns,
    filteredCampaigns,
    searchCampaignValue,
    _changeCampaign,
    handleCampaignSearch,
    setCampaigns,
    setCampaignSearch
  ] = useSearch<GetCampainSearchValueInfo>({
    optimizationId,
    advertiserId: advertiser.advertiserId,
    searchAPI: getShoppingCampaignSearch
  })

  const [
    adgroups,
    filteredAdgroups,
    searchAdgroupValue,
    _changeAdgroup,
    handleAdgroupSearch,
    setAdgroups,
    setAdSearch
  ] = useSearch<GetAdgroupSearchValueInfo>({
    optimizationId,
    advertiserId: advertiser.advertiserId,
    searchAPI: getShoppingAdgroupSearch
  })

  useEffect(() => {
    // NOTE: get all campaigns
    const init = async () => {
      const result = await getShoppingCampaignSearch(optimizationId, {
        advertiserId: advertiser.advertiserId,
        searchCampaign: ''
      })
      setCampaigns(result ?? [])
      setSelectedCampaignId(result[0]?.campaignId || '')
    }
    init()
  }, []) //eslint-disable-line

  useEffect(() => {
    if (campaigns.length < 1) return
    // NOTE: get all ad groups by campaignId
    const init = async () => {
      const result = await getShoppingAdgroupSearch(optimizationId, {
        advertiserId: advertiser.advertiserId,
        campaignId: selectedCampaignId,
        searchAdgroup: ''
      })
      setAdgroups(result ?? [])
      setSelectedAdgroupId(result[0]?.adgroupId || '')
    }
    init()
  }, [selectedCampaignId])

  useEffect(() => {
    const fetchData = async () => {
      const result = await getShoppingKeywordEfficiency(optimizationId, {
        advertiserId: advertiser.advertiserId,
        adgroupId: selectedAdgroupId
      })
      result.analysisResult && setAnalysisResult(result.analysisResult)
      result.analysisDate && setAnalysisDate(result.analysisDate)
      if (negativeKeywords.length > 0) {
        openToast(t('optimization.message.shoppingOptimization.toast.resetNegativeKeywords'))
        setOpenAdId('')
        setNegativeKeywords([])
      }
    }
    if (campaigns.length > 0 && adgroups.length > 0 && selectedAdgroupId) {
      fetchData()
    }
  }, [selectedAdgroupId])

  useEffect(() => {
    setTimeout(() => {
      const targets = ['ctr', 'cvr', 'cpc', 'cpa']
      const cols = document.querySelectorAll(
        '.adgroupTableWrapper .fixedLayout-table-root .fixedLayout-table-root colgroup col'
      )
      const filtered = [...cols].filter((col) => targets.includes(col.className.slice(20)))
      filtered.forEach((col) => {
        ;(col as HTMLTableColElement).style.width = headerDropdown === col.className.slice(20) ? '100px' : '0px'
      })
    }, 1)
  }, [headerDropdown])

  useEffect(() => {
    if (openAdId) {
      fetchNegativeKeywords()
    }
  }, [openAdId])

  const fetchNegativeKeywords = async () => {
    const response = await getShoppingRestrictKeyword(openAdId, {
      optimizationId: optimizationId,
      advertiserId: advertiser.advertiserId
    })
    const result: string[] = response?.data || []
    if (response.successOrNot === 'N' && response.statusCode === 'EXTERNAL_SERVICE_ERROR') {
      openToast(t('optimization.message.shoppingOptimization.toast.failedToGetRestrictKeyword'))
      return
    }
    const ads = document.querySelectorAll(
      `.adgroupTableWrapper .fixedLayout-table-root .fixedLayout-table-root table tbody tr td:nth-child(2)`
    )
    ads.forEach((td) => {
      if (result.indexOf(td.getAttribute('value') || '') >= 0) {
        td.previousElementSibling!.querySelector('input')!.click()
        td.parentElement!.style.display = 'none'
      }
    })
    setNegativeKeywords(result)
    setSavedNegativeKeywords(result)
  }

  const renderDropdownIcon = (props: any) => {
    return (
      <div
        className={`search-input-dropdown ${
          props.className.includes('MuiSelect-iconOpen') ? 'search-input-dropdown-open' : ''
        }`}
      >
        <ExpandLess />
      </div>
    )
  }

  const handleChangeCampaign = (event: any) => {
    const campaign = campaigns.find((campaign) => campaign.campaignId === event.target.value)
    setSelectedCampaignId(campaign?.campaignId || '')
    setCampaignSearch('')
  }

  const handleChangeAdgroup = (event: any) => {
    const adgroup = adgroups.find((adgroup) => adgroup.adgroupId === event.target.value)
    setSelectedAdgroupId(adgroup?.adgroupId || '')
    setAdSearch('')
  }

  const adgroupTableColumns: FixedLayoutColumn<AnalysisResult>[] = [
    {
      title: '',
      field: '',
      render: (rowData) => {
        if (rowData.adId === openAdId) {
          return <RemoveIcon className="removeIcon" data-adid={rowData.adId} />
        } else {
          return <AddIcon className="addIcon" data-adid={rowData.adId} />
        }
      },
      cellStyle: {
        width: 50
      }
    },
    {
      title: t(
        'optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.adgroupTableColumn.productTitle'
      ),
      field: 'productTitle',
      render: (rowData) => (
        <div>
          <span>{rowData.productTitle}</span>
          <br />
          <span>{rowData.adId}</span>
        </div>
      ),
      cellStyle: {
        width: 350
      }
    },
    {
      title: (
        <WithTooltip id="keywordCount">
          {t('optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.adgroupTableColumn.keywordCount')}
        </WithTooltip>
      ),
      field: 'keywordCount',
      headerStyle: {
        paddingLeft: 0,
        textAlign: 'right'
      },
      cellStyle: {
        width: 150,
        textAlign: 'right',
        fontWeight: 700
      },
      render: (rowData) => numberWithCommas(rowData.keywordCount)
    },
    {
      title: t('optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.adgroupTableColumn.cost'),
      field: 'cost',
      headerStyle: {
        paddingLeft: 0,
        textAlign: 'right'
      },
      cellStyle: {
        width: 75,
        textAlign: 'right'
      },
      render: (rowData) => numberWithCommas(rowData.cost),
      defaultSort: order.column === 'cost' ? order.direction.toLowerCase() : undefined
    },
    {
      title: t('optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.adgroupTableColumn.impression'),
      field: 'impressions',
      headerStyle: {
        paddingLeft: 0,
        textAlign: 'right'
      },
      cellStyle: {
        width: 120,
        textAlign: 'right'
      },
      render: (rowData) => numberWithCommas(rowData.impressions),
      defaultSort: order.column === 'impressions' ? order.direction.toLowerCase() : undefined
    },
    {
      title: t('optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.adgroupTableColumn.click'),
      field: 'clicks',
      headerStyle: {
        paddingLeft: 0,
        textAlign: 'right'
      },
      cellStyle: {
        width: 75,
        textAlign: 'right'
      },
      render: (rowData) => numberWithCommas(rowData.clicks),
      defaultSort: order.column === 'clicks' ? order.direction.toLowerCase() : undefined
    },
    {
      title: t('optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.adgroupTableColumn.conversion'),
      field: 'conversions',
      headerStyle: {
        paddingLeft: 0,
        textAlign: 'right'
      },
      cellStyle: {
        width: 120,
        textAlign: 'right'
      },
      render: (rowData) => numberWithCommas(rowData.conversions),
      defaultSort: order.column === 'conversions' ? order.direction.toLowerCase() : undefined
    },
    {
      title: t('optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.adgroupTableColumn.roas'),
      field: 'roas',
      render: (row) => <span>{(row['roas'] * 100).toFixed(2)}</span>,
      headerStyle: {
        paddingLeft: 0,
        textAlign: 'right'
      },
      cellStyle: {
        width: 90,
        textAlign: 'right'
      },
      defaultSort: order.column === 'roas' ? order.direction.toLowerCase() : undefined
    },
    {
      title: (
        <SelectBottom
          className="search-input"
          value={headerDropdown}
          onChange={(event) => setHeaderDropdown(event.target.value as string)}
          onClick={(event) => event.stopPropagation()}
          displayEmpty
          MenuProps={{
            className: 'shopping-optimization-filter-popover',
            anchorOrigin: {
              vertical: 30,
              horizontal: 'left'
            }
          }}
          IconComponent={(props) => renderDropdownIcon(props)}
        >
          <MenuItem key={'ctr'} value={'ctr'}>
            {t('optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.adgroupTableColumn.ctr')}
          </MenuItem>
          <MenuItem key={'cvr'} value={'cvr'}>
            {t('optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.adgroupTableColumn.cvr')}
          </MenuItem>
          <MenuItem key={'cpc'} value={'cpc'}>
            {t('optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.adgroupTableColumn.cpc')}
          </MenuItem>
          <MenuItem key={'cpa'} value={'cpa'}>
            {t('optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.adgroupTableColumn.cpa')}
          </MenuItem>
        </SelectBottom>
      ),
      field: 'ctr',
      render: (row) => (
        <span>
          {(headerDropdown === 'ctr' || headerDropdown === 'cvr') && (row[headerDropdown] * 100).toFixed(2)}
          {(headerDropdown === 'cpc' || headerDropdown === 'cpa') && row[headerDropdown]}
        </span>
      ),
      headerStyle: {
        paddingLeft: 0,
        textAlign: 'right'
      },
      cellStyle: {
        width: 100,
        textAlign: 'right'
      }
    }
  ]

  const adTableColumns: FixedLayoutColumn<KeywordEfficiencyDetail>[] = [
    {
      title: t('optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.adTableColumn.searchKeyword'),
      field: 'searchKeyword',
      cellStyle: {
        width: 330
      }
    },
    {
      title: (
        <WithTooltip id="keywordEfficiency">
          {t('optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.adTableColumn.efficiency')}
        </WithTooltip>
      ),
      field: 'efficiency',
      render: (row) => <span>{row['efficiency'].toFixed(4)}</span>,
      headerStyle: {
        paddingLeft: 0,
        textAlign: 'right'
      },
      cellStyle: {
        width: 130,
        textAlign: 'right',
        fontWeight: 700
      },
      defaultSort: 'asc'
    },
    {
      title: t('optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.adTableColumn.cost'),
      field: 'cost',
      headerStyle: {
        paddingLeft: 0,
        textAlign: 'right'
      },
      cellStyle: {
        width: 75,
        textAlign: 'right'
      },
      render: (rowData) => numberWithCommas(rowData.cost)
    },
    {
      title: t('optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.adTableColumn.impressions'),
      field: 'impressions',
      headerStyle: {
        paddingLeft: 0,
        textAlign: 'right'
      },
      cellStyle: {
        width: 120,
        textAlign: 'right'
      },
      render: (rowData) => numberWithCommas(rowData.impressions)
    },
    {
      title: t('optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.adTableColumn.clicks'),
      field: 'clicks',
      headerStyle: {
        paddingLeft: 0,
        textAlign: 'right'
      },
      cellStyle: {
        width: 75,
        textAlign: 'right'
      },
      render: (rowData) => numberWithCommas(rowData.clicks)
    },
    {
      title: t('optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.adTableColumn.conversions'),
      field: 'conversions',
      headerStyle: {
        paddingLeft: 0,
        textAlign: 'right'
      },
      cellStyle: {
        width: 120,
        textAlign: 'right'
      },
      render: (rowData) => numberWithCommas(rowData.conversions)
    },
    {
      title: t('optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.adTableColumn.roas'),
      field: 'roas',
      render: (row) => <span>{(row['roas'] * 100).toFixed(2)}</span>,
      headerStyle: {
        paddingLeft: 0,
        textAlign: 'right'
      },
      cellStyle: {
        width: 90,
        textAlign: 'right'
      }
    },
    {
      title: t('optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.adTableColumn.ctr'),
      field: 'ctr',
      render: (row) => <span>{(row['ctr'] * 100).toFixed(2)}</span>,
      headerStyle: {
        paddingLeft: 0,
        textAlign: 'right'
      },
      cellStyle: {
        width: headerDropdown === 'ctr' ? 100 : 0,
        textAlign: 'right'
      }
    },
    {
      title: t('optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.adTableColumn.cvr'),
      field: 'cvr',
      render: (row) => <span>{(row['cvr'] * 100).toFixed(2)}</span>,
      headerStyle: {
        paddingLeft: 0,
        textAlign: 'right'
      },
      cellStyle: {
        width: headerDropdown === 'cvr' ? 100 : 0,
        textAlign: 'right'
      }
    },
    {
      title: t('optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.adTableColumn.cpc'),
      field: 'cpc',
      render: (row) => <span>{row['cpc']}</span>,
      headerStyle: {
        paddingLeft: 0,
        textAlign: 'right'
      },
      cellStyle: {
        width: headerDropdown === 'cpc' ? 100 : 0,
        textAlign: 'right'
      }
    },
    {
      title: t('optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.adTableColumn.cpa'),
      field: 'cpa',
      render: (row) => <span>{row['cpa']}</span>,
      headerStyle: {
        paddingLeft: 0,
        textAlign: 'right'
      },
      cellStyle: {
        width: headerDropdown === 'cpa' ? 100 : 0,
        textAlign: 'right'
      }
    }
  ]

  const negativeKeywordColumns: FixedLayoutColumn<Keyword>[] = [
    {
      title: (
        <div
          style={{ textAlign: 'center', marginLeft: '-25px' }}
        >{t('optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.negativeKeywordLimit', {
            count: negativeKeywords.length
        })}</div>
      ),
      field: 'keyword'
    }
  ]

  const handleMoveRight = () => {
    const checked: string[] = []
    const checkboxes = document.querySelectorAll(
      '.adgroupTableWrapper .fixedLayout-table-root .fixedLayout-table-root table tbody tr td:first-child input'
    )
    checkboxes.forEach((el: any) => {
      const tr = el.parentElement.parentElement.parentElement.parentElement
      if (el.checked && tr.style.display === '') {
        const keyword = el.parentElement.parentElement.parentElement.nextElementSibling.getAttribute('value')
        checked.push(keyword)
        tr.style.display = 'none'
      }
    })
    setNegativeKeywords([...negativeKeywords, ...checked])
  }

  const handleMoveLeft = () => {
    const checked: string[] = []
    const checkboxes = document.querySelectorAll(
      '#negativeTableWrapper .fixedLayout-table-root table tbody tr td:first-child input'
    )
    checkboxes.forEach((el: any) => {
      if (el.checked) {
        const keyword = el.parentElement.parentElement.parentElement.nextElementSibling.getAttribute('value')
        checked.push(keyword)
        const matchedEl = document.querySelector(
          `.adgroupTableWrapper .fixedLayout-table-root .fixedLayout-table-root table tbody tr td:nth-child(2)[value="${keyword}"]`
        )
        if (matchedEl && matchedEl.parentElement) {
          matchedEl.parentElement.style.display = ''
        }
      }
    })
    const newNegativeKeywords = negativeKeywords.filter((keyword) => checked.indexOf(keyword) < 0)
    setNegativeKeywords(newNegativeKeywords)
  }

  const tableRef = useRef()

  const toggle = (id: any) => {
    return () => {
      const svgs = document.querySelectorAll(
        '.adgroupTableWrapper .fixedLayout-table-root table tbody tr td:nth-child(2) svg'
      )
      svgs.forEach((el: any) => {
        if (el.dataset.adid === id) {
          el.parentElement.parentElement.childNodes[0].childNodes[0].childNodes[0].click()
        }
      })
    }
  }

  const handleSave = async () => {
    if (openAdId) {
      if (objectCompare(negativeKeywords, savedNegativeKeywords, true)) {
        openToast(t('common.message.notChanged'))
        return
      }

      openDialog({
        title: t('common.message.title.notice'),
        message: t('common.message.saveConfirm'),
        cancelLabel: t('common.label.button.cancel'),
        actionLabel: t('common.label.button.confirm'),
        onAction: async () => {
          const response = await updateShoppingRestrictKeyword(openAdId, {
            optimizationId: optimizationId,
            advertiserId: advertiser.advertiserId,
            originalKeywords: savedNegativeKeywords,
            updatedKeywords: negativeKeywords
          })
          if (response.successOrNot === 'Y') {
            openToast(t('common.message.saveSuccess'))
            fetchNegativeKeywords()
          } else {
            switch (response.statusCode) {
              case 'NO_UPDATED_RESTRICT_KEYWORD':
                openToast(t('common.message.notChanged'))
                break
              case 'UPDATED_ORIGINAL_RESTRICT_KEYWORD':
                openToast(t('optimization.message.shoppingOptimization.toast.updatedOriginalNegativeKeyword'))
                break
              case 'EXTERNAL_SERVICE_ERROR':
                openToast(t('optimization.message.shoppingOptimization.toast.failedToUpdateRestrictKeyword'))
                break
              default:
                openToast(t('common.message.systemError'))
                break
            }
          }
        }
      })
    }
  }

  const EmptyKeyword = () => {
    const notiKey = openAdId ? 'noKeywordData' : 'noAdIdData'
    return (
      <div className="empty-keyword-panel">
        <MopIcon name={MOPIcon.NOTICE_SPEECH_BOX} size={80} />
        <span>{t(`optimization.message.shoppingOptimization.list.${notiKey}`)}</span>
      </div>
    )
  }

  const downloadNegativeKeyword = async () => {
    const result = await getShoppingKeywordEfficiencyDownload(optimizationId, {
      analysisDate: analysisDate,
      adgroupId: selectedAdgroupId,
      optimizationName: optimizationName
    })
    if (result && result?.statusCode === StatusCode.EMPTY_RAWDATA) {
      openToast(t('optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.toastMessage.emptyRawData'))
    }
  }

  const handleClose = () => {
    onClose(false)
  }
  return (
    <Dialog id="shopping-optimization-negative-keyword" onClose={handleClose} open={open}>
      <FlexRowCenter>
        <FlexRowCenter className="gap-1.5 text-active-blue font-bold text-lg">
          <MopIcon name={MOPIcon.SHOPPING_BAG} size={28} bgColor="#e1e6ff" />
          {t('optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.subtitle')}
        </FlexRowCenter>
        <MopIcon id="close-icon" customClass="ml-auto bg-white" name={MOPIcon.CLOSE} onClick={handleClose} size={24} bgColor="white" />
      </FlexRowCenter>
      <FlexRowCenter className="text-primary-text gap-2.5 my-2.5">
        <span className="text-2xl font-black">
          {t('optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.title')}
        </span>
        <MopIcon customClass="ml-auto" name={MOPIcon.DOWNLOAD} size={24} onClick={downloadNegativeKeyword} />
      </FlexRowCenter>
      {/* FILTER */}
      <Table className="adgroup-select">
        <colgroup>
          <col width="15%" />
          <col width="25%" />
          <col width="30%" />
          <col width="30%" />
        </colgroup>
        <TableRow>
          <TableCell>{t('optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.adtype')}</TableCell>
          <TableCell>
            {t('optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.optimizationName')}
          </TableCell>
          <TableCell>{t('optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.campaign')}</TableCell>
          <TableCell>{t('optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.adgroup')}</TableCell>
        </TableRow>
        <TableRow>
          <TableCell>{t(`common.code.saShoppingType.${saShoppingType}`)}</TableCell>
          <TableCell>{optimizationName}</TableCell>
          <TableCell>
            {campaigns.length > 0 && (
              <div className="search-input">
                <SelectBottom
                  value={selectedCampaignId}
                  onChange={handleChangeCampaign}
                  displayEmpty
                  MenuProps={{
                    className: 'shopping-optimization-filter-popover',
                    anchorOrigin: {
                      vertical: 30,
                      horizontal: 'left'
                    }
                  }}
                  IconComponent={(props) => renderDropdownIcon(props)}
                  renderValue={(value: any) =>
                    campaigns.find((campaign) => campaign.campaignId === value)?.campaignName || ''
                  }
                >
                  <MopSearch
                    id="negative-keyword-campaign"
                    placeholder={t(
                      'optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.searchPlaceholder'
                    )}
                    value={searchCampaignValue}
                    onChange={(e) => setCampaignSearch(e.target.value)}
                    onSearch={() =>
                      handleCampaignSearch({
                        searchCampaign: searchCampaignValue
                      })
                    }
                    onFocus={() => setCampaignSearch('')}
                  />
                  {filteredCampaigns.map((campaign) => {
                    return (
                      <MenuItem key={campaign.campaignId} value={campaign.campaignId}>
                        {campaign.campaignName}
                      </MenuItem>
                    )
                  })}
                </SelectBottom>
              </div>
            )}
          </TableCell>
          <TableCell>
            {adgroups.length > 0 && (
              <div className="search-input">
                <SelectBottom
                  value={selectedAdgroupId}
                  onChange={handleChangeAdgroup}
                  displayEmpty
                  MenuProps={{
                    className: 'shopping-optimization-filter-popover',
                    anchorOrigin: {
                      vertical: 30,
                      horizontal: 'left'
                    }
                  }}
                  IconComponent={(props) => renderDropdownIcon(props)}
                  renderValue={(value: any) =>
                    adgroups.find((adgroup) => adgroup.adgroupId === value)?.adgroupName || ''
                  }
                >
                  <MopSearch
                    id="negative-keyword-adgroup"
                    placeholder={t(
                      'optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.searchPlaceholder'
                    )}
                    value={searchAdgroupValue}
                    onChange={(e) => setAdSearch(e.target.value)}
                    onSearch={() =>
                      handleAdgroupSearch({
                        campaignId: selectedCampaignId,
                        searchAdgroup: searchAdgroupValue
                      })
                    }
                    onFocus={() => setAdSearch('')}
                  />
                  {filteredAdgroups.map((adgroup) => {
                    return (
                      <MenuItem key={adgroup.adgroupId} value={adgroup.adgroupId}>
                        {adgroup.adgroupName}
                      </MenuItem>
                    )
                  })}
                </SelectBottom>
              </div>
            )}
          </TableCell>
        </TableRow>
      </Table>
      <TableContainer className="contents">
        <Box className="adgroupTableWrapper">
          <TableHeader>
            <TooltipTextBox className="font-bold text-xl">
              <Tooltip id="efficiencyResult" />
              {t('optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.adgroupTable')}
            </TooltipTextBox>
            {analysisDate && (
              <TooltipTextBox className="absolute bottom-2.5 left-2.5 text-sm">
                <Tooltip id="date" />
                <span>
                  Date : <b>{format(convertStrToDate(analysisDate)!, DateFnsFormat.DISP_DATE)}</b>
                </span>
              </TooltipTextBox>
            )}
          </TableHeader>

          <FixedLayoutTable
            tableRef={tableRef}
            options={{
              showTitle: false,
              search: false,
              selection: false,
              sorting: true,
              thirdSortClick: false,
              paging: false,
              draggable: false,
              toolbar: false,
              tableLayout: 'fixed',
              detailPanelType: 'single',
              maxBodyHeight: '420px',
              minBodyHeight: '420px',
              rowStyle: (rowData) => ({
                backgroundColor: openAdId === rowData.adId ? '#F1F6FF' : '#fff'
              })
            }}
            columns={adgroupTableColumns}
            data={analysisResult}
            localization={{ body: { emptyDataSourceMessage: t('common.message.list.noData') } }}
            detailPanel={[
              {
                icon: () => <></>,
                openIcon: () => <></>,
                render: (rowData) => {
                  return (
                    <FixedLayoutTable
                      options={{
                        showTitle: false,
                        search: false,
                        selection: true,
                        sorting: true,
                        thirdSortClick: false,
                        paging: false,
                        draggable: false,
                        toolbar: false,
                        tableLayout: 'fixed'
                      }}
                      columns={adTableColumns}
                      data={detailResult}
                      localization={{ body: { emptyDataSourceMessage: t('common.message.list.noData') } }}
                    />
                  )
                }
              }
            ]}
            onRowClick={async (event, rowData, togglePanel) => {
              if (rowData!.adId === openAdId) {
                setOpenAdId('')
                setNegativeKeywords([])
                togglePanel && togglePanel()
              } else {
                const result = await getShoppingKeywordEfficiencyDetail(optimizationId, {
                  analysisDate: analysisDate,
                  adgroupId: selectedAdgroupId,
                  adId: rowData!.adId
                })
                setOpenAdId(rowData!.adId)
                setDetailResult(result!)
                if (negativeKeywords.length > 0) {
                  openToast(t('optimization.message.shoppingOptimization.toast.resetNegativeKeywords'))
                  setNegativeKeywords([])
                }

                setTimeout(toggle(rowData!.adId), 1)
              }
            }}
            onOrderChange={(orderedColumnId, orderDirection) => {
              setOrder({
                column: adgroupTableColumns[orderedColumnId].field,
                direction: orderDirection
              })
            }}
          />
        </Box>
        <Box className="shuttleButtons">
          <IconButton id="move-right" size="small" onClick={handleMoveRight} disabled={!openAdId}>
            <RightArrowIcon />
          </IconButton>
          <IconButton id="move-left" size="small" onClick={handleMoveLeft} disabled={!openAdId}>
            <LeftArrowIcon />
          </IconButton>
        </Box>
        <NegativeWrapper id='negativeTableWrapper'>
          <TableHeader>
            <TooltipTextBox className="font-bold text-xl">
              <Tooltip id="keywordSetting" />
              {t('optimization.label.ShoppingOptimizationDetailModal.negativeKeywordModal.negativeTable')}
            </TooltipTextBox>
          </TableHeader>
          <FixedLayoutTable
            options={{
              showTitle: false,
              search: false,
              selection: true,
              sorting: false,
              thirdSortClick: false,
              paging: false,
              draggable: false,
              toolbar: false,
              maxBodyHeight: '420px',
              minBodyHeight: '420px'
            }}
            columns={negativeKeywordColumns}
            data={negativeKeywords.map((keyword) => ({ keyword: keyword }))}
            localization={{ body: { emptyDataSourceMessage: <EmptyKeyword /> } }}
          />
          <TransButton className="px-10" ui={ButtonUI.Contained} onClick={handleSave} i18nKey={`common.button.save`} />
        </NegativeWrapper>
      </TableContainer>
    </Dialog>
  )
}
export default ShoppingOptimizationNegativeKeywordModal
