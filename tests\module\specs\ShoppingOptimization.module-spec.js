import ShoppingOptimizationPage from '@pages/shoppingOptimization/ShoppingOptimizationPage';
import ShoppingOptimizationMock from '@mock/ShoppingOptimizationMock';
import SearchOptimizationListMock from '@mock/SearchOptimizationListMock';
import AdvertiserMock from '@mock/AdvertiserMock';

const page = new ShoppingOptimizationPage();
const service = new ShoppingOptimizationMock();
const searchOptService = new SearchOptimizationListMock();
const advertiserService = new AdvertiserMock();

const sessionInfo = {
  sessionId: 'test-session-id',
  memberId: 1,
  memberName: '홍길동',
  roleType: 'USER',
};

describe('ShoppingOptimizationPage', () => {
    it.guide('상품 최적화 페이지로 이동, 화면이 로드되면 Then 조회필터와 리스트가 표시됨.', {
        mockFunc: () => {
            advertiserService.successWhenGetAdvertisers();
            service.successWhenGetShoppingOptimizations();
            service.successWhenGetShoppingOptimizationNewAdgroup();
            searchOptService.successWhenGetMedia(1);
        },
        actionFunc: () => {
            page.fakeSession(sessionInfo);
            page.visit('/shopping-optimization');
        },
        waitFunc: () => {
            cy.wait(['@successWhenGetAdvertisers', '@successWhenGetShoppingOptimizations', '@successWhenGetShoppingOptimizationNewAdgroup', '@successWhenGetMedia']);
        },
        assertFunc: () => {
            page.assertFilterIsVisible();
            page.assertDataExist();
        }
    });

    it.guide('조회조건(자동입찰) 드롭다운을 통해 조건을 설정하면 Then 변경된 조회 결과가 리스트에 표시됨.',{
        mockFunc: () => {
            service.successWhenGetShoppingOptimizations();
        },
        actionFunc: () => {
            page.clickFilter(1);
            page.clickSelect(2);
        },
        waitFunc: () => {
            cy.wait('@successWhenGetShoppingOptimizations');
        },
        assertFunc: () => {
            page.assertInputValue('BID','Y');
        },
    })

    it.guide('조회조건(상태) 드롭다운을 통해 조건을 설정하면 Then 변경된 조회 결과가 리스트에 표시됨.',{
        mockFunc: () => {
            service.successWhenGetShoppingOptimizations();
        },
        actionFunc: () => {
            page.clickFilter(2);
            page.clickSelect(2);
        },
        waitFunc: () => {
            cy.wait('@successWhenGetShoppingOptimizations');
        },
        assertFunc: () => {
            page.assertInputValue('STATUS','INSPECTING');
        },
    })

    it.guide('조회조건(매체) 드롭다운을 통해 조건을 설정하면 Then 변경된 조회 결과가 리스트에 표시됨.',{
        mockFunc: () => {
            service.successWhenGetShoppingOptimizations();
        },
        actionFunc: () => {
            page.clickFilter(3);
            page.clickSelect(2);
        },
        waitFunc: () => {
            cy.wait('@successWhenGetShoppingOptimizations');
        },
        assertFunc: () => {
            page.assertInputValue('MEDIA','NAVER');
        },
    })

    it.guide('조회조건(광고상품 유형) 드롭다운을 통해 조건을 설정하면 Then 변경된 조회 결과가 리스트에 표시됨.',{
        mockFunc: () => {
            service.successWhenGetShoppingOptimizations();
        },
        actionFunc: () => {
            page.clickFilter(4);
            page.clickSelect(2);
        },
        waitFunc: () => {
            cy.wait('@successWhenGetShoppingOptimizations');
        },
        assertFunc: () => {
            page.assertInputValue('AD','SHOPPING');
        },
    })

    it.guide('조회조건(최적화 목표) 드롭다운을 통해 조건을 설정하면 Then 변경된 조회 결과가 리스트에 표시됨.',{
        mockFunc: () => {
            service.successWhenGetShoppingOptimizations();
        },
        actionFunc: () => {
            page.clickFilter(5);
            page.clickSelect(2);
        },
        waitFunc: () => {
            cy.wait('@successWhenGetShoppingOptimizations');
        },
        assertFunc: () => {
            page.assertInputValue('GOAL','CLICK');
        },
    })

    it.guide('최적화명으로 검색하면 Then 변경된 조회 결과가 리스트에 표시됨.',{
        mockFunc: () => {},
        actionFunc: () => {
            page.search('최적화_test명_1');
        },
        waitFunc: () => {},
        assertFunc: () => {
            page.assertDataExist();
        },
    })

    it.guide(' row의 제외키워드을 클릭하면 Then 제외키워드를 설정할 수 있는 모달창이 띄워짐',{
        mockFunc: () => {
            service.successWhenGetShoppingCampaignSearch();
            service.successWhenGetShoppingAdgroupSearch();
            service.successWhenGetShoppingKeywordEfficiency();

        },
        actionFunc: () => {
            page.clickExKeyword();
        },
        waitFunc: () => {
            cy.wait(['@successWhenGetShoppingCampaignSearch', '@successWhenGetShoppingAdgroupSearch', '@successWhenGetShoppingKeywordEfficiency']);
        },
        assertFunc: () => {
            page.assertModalIsVisible();
        },
        afterFunc: () => {
            page.clickCloseModal();
        }
    })

 
    it.guide(' row의 최적화명을 클릭하면 Then 모달창이 띄워짐',{
        mockFunc: () => {
            service.successWhenGetShoppingOptimizationsName();
            service.successWhenGetShoppingAdgroups();
            service.successWhenGetEmptyShoppingAdgroups();
            service.successWhenGetShoppingOptimizationCosts();
        },
        actionFunc: () => {
            page.clickOptName();
        },
        waitFunc: () => {
            cy.wait([
                '@successWhenGetShoppingOptimizationsName', 
                '@successWhenGetShoppingAdgroups',
                '@successWhenGetShoppingOptimizationCosts'
            ]);
        },
        assertFunc: () => {
            page.assertModalIsVisible();
        }
    })

    
});