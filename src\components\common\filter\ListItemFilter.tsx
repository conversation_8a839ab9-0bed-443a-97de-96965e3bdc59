import React from 'react'
import { useTranslation } from 'react-i18next';
import SelectBottom from '@components/common/SelectBottom';
import { SelectDropdownIcon } from '@components/common/icon'
import { MenuItem } from '@material-ui/core';
import type { SelectProps } from '@material-ui/core';

import './ListItemFilter.scss'

interface Props {
  label: string;
  name: string;
  value: any;
  width?: string;
  isCustomSelect?: boolean
  multiple?: boolean
  onChange: (_event: React.ChangeEvent<{
    name?: string | undefined;
    value: unknown;
  }>) => void;
  onClick?: (_event: any) => void
  renderValue?: (value: SelectProps['value']) => React.ReactNode
}

const ListItemFilter = ({ children, label, value, name, onChange, onClick, renderValue, width, multiple, isCustomSelect }: React.PropsWithChildren<Props>) => {
  const { t } = useTranslation();
  return (
    <div className={`list-item-filter list-item-filter--${name}`} onClick={onClick}>
      <span className="list-item-filter__label">{ label }</span>
      <div className="list-item-filter__select" style={{ '--width': width } as React.CSSProperties}>
        {isCustomSelect
          ? <>{ children }</>
          : <SelectBottom
              displayEmpty
              multiple={multiple}
              name={name ?? label}
              value={value}
              onChange={onChange}
              MenuProps={{
                className: 'filter-options-popover',
                anchorOrigin: { vertical: 24, horizontal: 'left' }
              }}
              IconComponent={(props) => <SelectDropdownIcon {...props} />}
              renderValue={renderValue}
            >
              <MenuItem value={'ALL'}>
                <div className="list-item-filter__all">
                  { t('common.label.filter.all') }
                </div>
              </MenuItem>
              { children }
            </SelectBottom>
        }
      </div>
    </div>
  )
}

export default ListItemFilter