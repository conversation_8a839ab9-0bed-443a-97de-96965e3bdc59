import { AnalyticsType, MediaAnalyticsType } from './Analytics'
import { AuthorityType } from './Advertiser'
import { CommerceMediaType, MediaType, MediaTypeWithETC, COMMERCE_PATH_CONNECTION } from './Media'
import { MOPIcon } from './Icon'
import { ButtonUI } from './UI'
import { DeviceType } from './Device'
import { StatusCode } from './CommonResponse'
import { Service } from './Service'
import { YNFlag } from './YNFlag'
import { ActionType } from './CommonConstants'
import { ViewType } from './Layout'
import { CONSTRAINT } from './ContraintsByCurrency'
import { DayOfWeek } from './Time'

export type MopMedia = MediaType | AnalyticsType
export type MediaAndCommerce = MopMedia | CommerceMediaType

export type ObjectKeys<T> = T extends object
  ? (keyof T)[]
  : T extends number
  ? []
  : T extends Array<any> | string
  ? string[]
  : never

export {
  ActionType,
  AnalyticsType,
  AuthorityType,
  ButtonUI,
  CONSTRAINT,
  DayOfWeek,
  DeviceType,
  MediaAnalyticsType,
  MediaType,
  MediaTypeWithETC,
  MOPIcon,
  Service,
  StatusCode,
  YNFlag,
  ViewType,
  CommerceMediaType,
  COMMERCE_PATH_CONNECTION,
}
