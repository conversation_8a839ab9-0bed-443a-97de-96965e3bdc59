import KeywordTabs from '@components/report/negativeKeyword/keywordTabs/KeywordTabs'
import NegativeKeywordHeader from '@components/report/negativeKeyword/NegativeKeywordHeader'
import { ReactElement, useState, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { MaterialListFilterControl, RecommendationType } from '@components/common'
import PinnedTable from '@components/common/table/PinnedTable'
import { ColumnDef, VisibilityState } from '@tanstack/react-table'
// 네거티브 키워드 리포트 데이터 타입 정의
type NegativeKeywordDetailRow = {
  keywordId: string;
  keyword: string;
  matchType: string;
  adGroup: string;
  campaign: string;
  impressions: number;
  clicks: number;
  cost: number;
  conversions: number;
};

type NegativeKeywordReportRow = {
  id: string;
  keyword: string;
  matchType: string;
  // 광고 지표
  impressions: number;
  clicks: number;
  cost: number;
  conversions: number;
  ctr: number; // CTR(%)
  cpc: number; // CPC
  roas: number; // ROAS(%)
  // 상세 정보
  details: NegativeKeywordDetailRow[];
};

// ComplexCase용 Mock 데이터
const mockNegativeKeywordReportData: NegativeKeywordReportRow[] = [
  {
    id: '1',
    keyword: '뉴발란스 운동화뉴발란스 운동화뉴발란스 운동화',
    matchType: '완전일치',
    impressions: 2970,
    clicks: 2970,
    cost: 300000,
    conversions: 2970,
    ctr: 2970,
    cpc: 2970,
    roas: 2970,
    details: [
      {
        keywordId: 'kw1-1',
        keyword: 'C type 젠더',
        matchType: '완전일치',
        adGroup: '스포츠화 그룹',
        campaign: 'CtoC 캠페인',
        impressions: 1000,
        clicks: 50,
        cost: 100000,
        conversions: 5
      },
      {
        keywordId: 'kw1-2',
        keyword: 'C type 캐주얼',
        matchType: '구문일치',
        adGroup: '캐주얼화 그룹',
        campaign: 'CtoC 캠페인',
        impressions: 970,
        clicks: 45,
        cost: 100000,
        conversions: 4
      }
    ]
  },
  {
    id: '2',
    keyword: '뉴발란스 운동화뉴발란스 운동화뉴발란스 운동화',
    matchType: '구문일치',
    impressions: 2970,
    clicks: 2970,
    cost: 300000,
    conversions: 2970,
    ctr: 2970,
    cpc: 2970,
    roas: 2970,
    details: [
      {
        keywordId: 'kw2-1',
        keyword: 'C type 젠더',
        matchType: '구문일치',
        adGroup: '스포츠화 그룹',
        campaign: 'CtoC 캠페인',
        impressions: 1500,
        clicks: 75,
        cost: 150000,
        conversions: 8
      }
    ]
  }
];

const mockAddedExcludedKeywords = [
  {
    id: '1234543234',
    keyword: '뉴발란스 운동화뉴발란화',
    count: 4,
    subItems: []
  },
  {
    id: '1234543235',
    keyword: '뉴발란스 운동화뉴발란스 운동화뉴발란스 운동화',
    count: 4,
    subItems: []
  }
];

const mockRecoveredKeywords = [
  {
    id: 'rec1',
    keyword: '복구된 키워드 1',
    count: 2
  },
  {
    id: 'rec2',
    keyword: '복구된 키워드 2',
    count: 1
  }
]
const campaignOptions = [
  { value: 'campaign1', label: '캠페인 1' },
  { value: 'campaign2', label: '캠페인 2' },
  { value: 'campaign3', label: '캠페인 3' }
]
const adGroupOptions = [
  { value: 'adGroup1', label: '그룹 1' },
  { value: 'adGroup2', label: '그룹 2' },
  { value: 'adGroup3', label: '그룹 3' }
]
const CreateCampaignNaverReportPage: React.FC = (): ReactElement => {
  const { t } = useTranslation()

  const [addedExcludedKeywords, setAddedExcludedKeywords] = useState(mockAddedExcludedKeywords)
  const [recoveredKeywords, setRecoveredKeywords] = useState(mockRecoveredKeywords)

  const handleRemoveKeyword = (keywordId: string, tabType: 'excluded' | 'recovered') => {
    if (tabType === 'excluded') {
      setAddedExcludedKeywords((prev) => prev.filter((keyword) => keyword.id !== keywordId))
    } else {
      setRecoveredKeywords((prev) => prev.filter((keyword) => keyword.id !== keywordId))
    }
  }
  const handleRecommendationChange = (type: RecommendationType) => {
    console.log('Recommendation type:', type)
  }

  const handleFilterChange = (filters: { material: string; campaign: string; adGroup: string }) => {
    console.log('Filters:', filters)
  }
  return (
    <>
      <div className="CreationCampaignPage" id="createFormPage">
        <div className="left-site-create-new-campaign">
          <div className="flex items-center justify-between mb-5">
            <div className="text-[28px] font-bold">
              {t('negativeKeyword.title')}
            </div>
          </div>

          <div className="flex-grow flex flex-col">
            <NegativeKeywordHeader />
            <div>
              <MaterialListFilterControl
                analysisDate="2025.08.01"
                onRecommendationTypeChange={handleRecommendationChange}
                onFilterChange={handleFilterChange}
                onDownload={() => console.log('Download clicked')}
                campaignOptions={campaignOptions}
                adGroupOptions={adGroupOptions}
              />
             <PinnedTable/>
            </div>
          </div>
          
        </div>

        <div className="right-site-create-new-campaign">
          <p className='text-[20px] font-bold'>제외키워드 설정</p>
          <KeywordTabs
            addedExcludedKeywords={addedExcludedKeywords}
            recoveredKeywords={recoveredKeywords}
            onRemoveKeyword={handleRemoveKeyword}
          />
        </div>
      </div>
    </>
  )
}

export default CreateCampaignNaverReportPage
