import KeywordTabs from '@components/report/negativeKeyword/keywordTabs/KeywordTabs'
import Negative<PERSON>eywordHeader from '@components/report/negativeKeyword/NegativeKeywordHeader'
import { ReactElement, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { MaterialListFilterControl, RecommendationType } from '@components/common'
import PinnedTable from '@components/common/table/PinnedTable'
const mockAddedExcludedKeywords = [
  {
    id: '1234543234',
    keyword: '뉴발란스 운동화뉴발란화',
    count: 4,
    subItems: []
  },
  {
    id: '1234543235',
    keyword: '뉴발란스 운동화뉴발란스 운동화뉴발란스 운동화',
    count: 4,
    subItems: []
  },
  {
    id: '1234543236',
    keyword: '뉴발란스 운동화뉴발란스 운동화뉴발란스 운동화뉴발란...',
    count: 4,
    subItems: [
      { id: 'sub1', keyword: 'C type 젠더' },
      { id: 'sub2', keyword: 'C type 젠더 C type 젠더' },
      { id: 'sub3', keyword: 'C type 젠더 C type 젠더' },
      { id: 'sub4', keyword: 'C type 젠더' }
    ]
  },
  {
    id: '1234543237',
    keyword: '나이키 에어맥스 운동화',
    count: 3,
    subItems: [
      { id: 'sub5', keyword: '남성용' },
      { id: 'sub6', keyword: '여성용' },
      { id: 'sub7', keyword: '키즈용' }
    ]
  },
  {
    id: '1234543238',
    keyword: '아디다스 스탠스미스',
    count: 2,
    subItems: [
      { id: 'sub8', keyword: '화이트' },
      { id: 'sub9', keyword: '블랙' }
    ]
  },
  {
    id: '1234543239',
    keyword: '뉴발란스 운동화뉴발란스 운동화뉴발란스 운동화뉴발란...',
    count: 4,
    subItems: []
  },
  {
    id: '1234543240',
    keyword: '컨버스 척테일러',
    count: 5,
    subItems: [
      { id: 'sub10', keyword: '하이탑' },
      { id: 'sub11', keyword: '로우탑' },
      { id: 'sub12', keyword: '원스타' }
    ]
  }
]

const mockRecoveredKeywords = [
  {
    id: 'rec1',
    keyword: '복구된 키워드 1',
    count: 2
  },
  {
    id: 'rec2',
    keyword: '복구된 키워드 2',
    count: 1
  }
]
const campaignOptions = [
  { value: 'campaign1', label: '캠페인 1' },
  { value: 'campaign2', label: '캠페인 2' },
  { value: 'campaign3', label: '캠페인 3' }
]
const adGroupOptions = [
  { value: 'adGroup1', label: '그룹 1' },
  { value: 'adGroup2', label: '그룹 2' },
  { value: 'adGroup3', label: '그룹 3' }
]
const CreateCampaignNaverReportPage: React.FC = (): ReactElement => {
  const { t } = useTranslation()

  const [addedExcludedKeywords, setAddedExcludedKeywords] = useState(mockAddedExcludedKeywords)
  const [recoveredKeywords, setRecoveredKeywords] = useState(mockRecoveredKeywords)

  const handleRemoveKeyword = (keywordId: string, tabType: 'excluded' | 'recovered') => {
    if (tabType === 'excluded') {
      setAddedExcludedKeywords((prev) => prev.filter((keyword) => keyword.id !== keywordId))
    } else {
      setRecoveredKeywords((prev) => prev.filter((keyword) => keyword.id !== keywordId))
    }
  }
  const handleRecommendationChange = (type: RecommendationType) => {
    console.log('Recommendation type:', type)
  }

  const handleFilterChange = (filters: { material: string; campaign: string; adGroup: string }) => {
    console.log('Filters:', filters)
  }
  return (
    <>
      <div className="CreationCampaignPage" id="createFormPage">
        <div className="left-site-create-new-campaign">
          <div className="flex items-center justify-between mb-5">
            <div className="text-[28px] font-bold">
              {t('negativeKeyword.title')}
            </div>
          </div>

          <div className="flex-grow flex flex-col">
            <NegativeKeywordHeader />
            <div>
              <MaterialListFilterControl
                analysisDate="2025.08.01"
                onRecommendationTypeChange={handleRecommendationChange}
                onFilterChange={handleFilterChange}
                onDownload={() => console.log('Download clicked')}
                campaignOptions={campaignOptions}
                adGroupOptions={adGroupOptions}
              />
             <PinnedTable/>
            </div>
          </div>
          
        </div>

        <div className="right-site-create-new-campaign">
          <p className='text-[20px] font-bold'>제외키워드 설정</p>
          <KeywordTabs
            addedExcludedKeywords={addedExcludedKeywords}
            recoveredKeywords={recoveredKeywords}
            onRemoveKeyword={handleRemoveKeyword}
          />
        </div>
      </div>
    </>
  )
}

export default CreateCampaignNaverReportPage
