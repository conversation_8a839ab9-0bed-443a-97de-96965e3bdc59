import React, { ReactElement, useEffect } from 'react'
import './LoginPage.scss'
import { useTranslation } from 'react-i18next'
// import LoginForm from '@components/login/LoginForm'
import SignInForm from '@components/login/SignInForm'
import Logo from '@components/assets/images/logo_login.png'
import LoginBot from '@components/assets/images/bg_login_bot.png'
import { initChannelService } from '@utils/ChannelServiceUtil'
import { TransText } from '@components/common'
import { ReactComponent as NaverLogo } from '@assets/images/logo-naver-green.svg'
import { ReactComponent as CNSLogo } from '@assets/images/logo-lgcns.svg'
import { ReactComponent as Collabo } from '@assets/icon/collaboration-mark.svg'
import { fill } from 'lodash'

const LoginPage: React.FC = (): ReactElement => {
  const { t } = useTranslation()
  const urlSearchParams = new URLSearchParams(window.location.search)
  const params = Object.fromEntries(urlSearchParams.entries())

  useEffect(() => {
    return initChannelService({
      memberId: '',
      memberHash: ''
    })
  }, [])

  return (
    <div id="loginPage">
      <div className="login-title">{t('login.label.LoginPage.title')}</div>
      <div className="login-form">
        <div className="flex items-center justify-center gap-4">
          <span className='flex items-center gap-2.5'>
            <NaverLogo className='w-auto h-[19px]' />
            <Collabo className='w-auto h-[23px] collabo-line-color' />
            <CNSLogo className='w-auto h-[27px]' />
          </span>
          <TransText as="span" className="font-smibold text-[26px] text-landing-black" i18nKey="landing.banner.text" />
        </div>
        <div className="my-10">
          <div className="login-subtitle">
            {params.next ? t('login.label.LoginPage.insight') : t('login.label.LoginPage.optimization')}
          </div>
          <div className="logo-wrapper">
            <img src={Logo} alt="MOP" data-testid="logoIcon" id="logoIcon" />
          </div>
        </div>
        {/* <LoginForm /> */}
        <SignInForm />
      </div>
      <div className="login-footer">
        <ul className="login-footer-container">
          <li className="login-footer-item">
            <img src={LoginBot} alt="MEMBER" className="bg-login-bot" />
            {t('login.footer.collection')}
          </li>
          <li className="login-footer-item">
            <img src={LoginBot} alt="MEMBER" className="bg-login-bot" />
            {t('login.footer.projection')}
          </li>
          <li className="login-footer-item">
            <img src={LoginBot} alt="MEMBER" className="bg-login-bot" />
            {t('login.footer.flight')}
          </li>
        </ul>
      </div>
    </div>
  )
}
export default LoginPage
