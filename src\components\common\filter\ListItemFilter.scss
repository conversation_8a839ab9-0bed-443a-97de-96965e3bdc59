.list-item-filter {
  --dropdown-width: 40px;
  --width: 100%;
  --mop20-filter-label-bg: #363b50;
  width: var(--width);

  &__label {
    height: 22px;
    width: 100%;
    display: inline-block;
    background-color: var(--mop20-filter-label-bg);
    color: #fff;
    font-size: 13px;
    font-weight: 500;
    text-align: center;
    padding-right: var(--dropdown-width);
  }

  &:first-child .list-item-filter__select {
    border-left: 1px solid #b5b7c9;
  }

  &__select {
    width: 100%;
    border-top: 1px solid #b5b7c9;
    border-right: 1px solid #b5b7c9;
    border-bottom: 1px solid #b5b7c9;
    background-color: #fff;
    height: 40px;

    .MuiInputBase-root.MuiInput-root {
      font-size: 14px;
      width: 100%;
      font-weight: 300;
      color: var(--text-base);
      box-sizing: border-box;
      text-align: center;

      .MuiSelect-select.MuiSelect-select {
        padding-right: var(--dropdown-width);
      }

      #select-dropdown-icon {
        height: 100%;
        pointer-events: none;
        // cursor: pointer;
      }

      &.MuiInput-underline {
        height: 40px;
        &:before,
        &:after,
        &:hover:not(.Mui-disabled):before {
          border-bottom: none;
        }
      }

      &:focus {
        background: none;
      }
    }
  }

  #multi-select-platform-type {
    position: relative;
    .MuiAutocomplete-root {
      height: 40px;
      line-height: 40px;
      padding: 0px;

      .MuiFormControl-fullWidth > .MuiAutocomplete-inputRoot {
        flex-wrap: unset;
        height: 40px;
        padding-left: 8px;
      }
    }

    .MuiAutocomplete-endAdornment {
      top: 0;
    }

    .MuiAutocomplete-popupIndicator {
      padding: 0;
      margin-right: 0;
    }

    .dropdown-wrapper {
      width: 40px;
      height: 40px;

      #select-dropdown-icon {
        border-left: none;
      }
    }
  }
}
