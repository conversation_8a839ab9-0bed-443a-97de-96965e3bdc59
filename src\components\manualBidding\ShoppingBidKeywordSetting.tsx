import { Fragment, useEffect, useMemo, useState } from 'react'
import type { ColumnDef } from '@tanstack/react-table'
import { useTable, useActionType, useI18n } from '@hooks/common'
import {
  isTargetSettingReadyState,
  originBidTargetSettingState,
  shoppingBidKeywordStatsState,
  shoppingBidSettingSelector,
  shoppingBidSettingState,
  bidKeywordStatsDateSelector
} from '@store/SsRankMaintenance'
import { BID_CRITERIA, ManualBidKeywordData } from '@models/manualBidding'
import { numberWithCommas } from '@utils/FormatUtil'
import BaseTable, { HeaderCell, DataCell } from '@components/common/table/BaseTable'
import EmptyBox from '@components/common/status/EmptyBox'
import { cn } from '@utils/index'
import { Tab, Listbox, Transition } from '@headlessui/react'
import tw from 'twin.macro'
import { EllipsisText, MopIcon, MopSwitch } from '@components/common'
import { ActionType, MOPIcon, YNFlag } from '@models/common'
import { useRecoilState, useRecoilValue } from 'recoil'
import { ModalTooltip } from '@components/common/rankMaintenance/RankTooltips'
import CustomTooltip from '@components/common/CustomTooltip'
import MopSearch from '@components/common/mopUI/MopSearch'
import { useTranslation } from 'react-i18next'

const InputBox = tw.div`flex rounded items-center border border-[#ededed] rounded p-1 gap-1`

const categories = [
  { tab: 'Avg Rank', value: BID_CRITERIA.AVG_RANK, panel: 'Average Rank' },
  { tab: 'ROAS', panel: 'ROAS 설정값', value: BID_CRITERIA.ROAS, isPercent: true },
  { tab: 'CPA', panel: 'CPA 설정값', value: BID_CRITERIA.CPA }
]
interface SelectedKeyword {
  searchKeyword: string
  totalKeywordYn: YNFlag
}

interface Props {
  modalType: ActionType
}

const ShoppingBidKeywordSetting = ({ modalType }: Props) => {
  const { t } = useTranslation();
  const { isReadType, isEditType, isCreateType } = useActionType(modalType)
  const [selected, setSelected] = useState<SelectedKeyword | undefined>()
  const searchKeywordStats = useRecoilValue(shoppingBidKeywordStatsState)
  const originTargetSetting = useRecoilValue(originBidTargetSettingState)
  const searchKeywords = useRecoilValue(shoppingBidSettingSelector)
  const latestStatDate = useRecoilValue(bidKeywordStatsDateSelector)
  const isSettingReady = useRecoilValue(isTargetSettingReadyState)
  const [bidSettingInfo, setBidSettingInfo] = useRecoilState(shoppingBidSettingState)
  const [keywordSearch, setKeywordSearch] = useState('')
  const { isEN } = useI18n()

  const isFormDisabled = !isSettingReady && isCreateType

  const filteredKeywords = useMemo(
    () =>
      searchKeywords.filter((k) => k.searchKeyword.toLowerCase().includes(keywordSearch.toLowerCase())),
    [searchKeywords, keywordSearch]
  )

  const columns = useMemo<ColumnDef<ManualBidKeywordData>[]>(
    () => [
      {
        accessorKey: 'searchKeyword',
        header: 'Keyword',
        cell: ({ row }) => (
          <EllipsisText>
            <CustomTooltip title={row.original.searchKeyword} placement="bottom">
              <span>{row.original.searchKeyword}</span>
            </CustomTooltip>
          </EllipsisText>
        ),
        size: 220
      },
      {
        accessorKey: 'impressions',
        header: 'Impression',
        cell: ({ row }) => <p className="w-full text-right">{numberWithCommas(row.original.impressions)}</p>
      },
      {
        accessorKey: 'clicks',
        header: 'Click',
        cell: ({ row }) => <p className="w-full text-right">{numberWithCommas(row.original.clicks)}</p>
      },
      {
        accessorKey: 'cost',
        header: 'Cost',
        cell: ({ row }) => <p className="w-full text-right">{numberWithCommas(row.original.cost)}</p>
      },
      {
        accessorKey: 'conversions',
        header: 'Conversion',
        cell: ({ row }) => <p className="w-full text-right">{numberWithCommas(row.original.conversions)}</p>
      },
      {
        accessorKey: 'sales',
        header: 'Revenue',
        cell: ({ row }) => <p className="w-full text-right">{numberWithCommas(row.original.sales)}</p>
      },
      {
        accessorKey: 'avgRank',
        header: 'Avg. Rank',
        cell: ({ row }) => <p className="w-full text-right">{row.original.avgRank.toFixed(2)}</p>
      },
      {
        accessorKey: 'cpc',
        header: 'CPC',
        cell: ({ row }) => <p className="w-full text-right">{numberWithCommas(row.original.cpc)}</p>
      },
      {
        accessorKey: 'cpa',
        header: 'CPA',
        cell: ({ row }) => <p className="w-full text-right">{numberWithCommas(row.original.cpa)}</p>
      },
      {
        accessorKey: 'roas',
        header: 'ROAS(%)',
        cell: ({ row }) => <p className="w-full text-right">{numberWithCommas(row.original.roas)}</p>
      }
    ],
    []
  )

  const [tabIndex, setTabIndex] = useState(0)

  const { table } = useTable<ManualBidKeywordData>({
    data: searchKeywordStats ?? [],
    columns,
    getRowId: (row) => `${row.adId}_${row.searchKeyword}`
  })
  const headers = table.getHeaderGroups()[0].headers
  const dataRows = table.getRowModel().rows
  const pinnedRows = dataRows.filter((row) => row.original.totalKeywordYn === 'Y')
  const normalRows = dataRows.filter((row) => row.original.totalKeywordYn !== 'Y')
  const emptyCellClass = '!text-primary-300'

  const changeBidSetting = ({ target: { name, value } }: React.ChangeEvent<HTMLInputElement>) => {
    const newVal = name === 'AVG_RANK' ? value : value.replace(/,/g, '')
    setBidSettingInfo((prev) => ({
      ...prev,
      [name]: Number(newVal)
    }))
  }

  const changeBidCriteria = (value: BID_CRITERIA) => {
    if (isEditType && value === originTargetSetting.bidCriteria) {
      setBidSettingInfo((prev) => ({
        ...prev,
        bidTarget: originTargetSetting.bidTarget,
        bidCriteria: value
      }))
    } else {
      setBidSettingInfo((prev) => ({
        ...prev,
        bidTarget: 0,
        bidCriteria: value
      }))
    }
  }

  const selectSearchKeyword = (selected: SelectedKeyword) => {
    setSelected(selected)
    setBidSettingInfo((prev) => ({
      ...prev,
      ...selected
    }))
  }

  useEffect(() => {
    if (!isCreateType)
      setSelected({
        searchKeyword: bidSettingInfo.searchKeyword,
        totalKeywordYn: bidSettingInfo.totalKeywordYn
      })
  }, [searchKeywords])

  useEffect(() => {
    if (!isCreateType) {
      const tabIdx = categories.findIndex((category) => category.value === bidSettingInfo.bidCriteria)
      setTabIndex(tabIdx)
    }
  }, [bidSettingInfo])

  return (
    <div className={cn('flex p-6 gap-2')}>
      <div className="w-3/4 flex flex-col gap-2 bg-gray-light p-2 rounded">
        <div className="flex items-center justify-between py-2">
          <ModalTooltip id="bidKeywordPerf" field="target">
            <span className="font-bold text-base">{t('rankMaintenance.targetBidding.modal.keywordPerformance')}</span>
          </ModalTooltip>
          {latestStatDate && (
            <ModalTooltip id="analysisDate" field="target">
              <span className="text-sm">Date: {latestStatDate}</span>
            </ModalTooltip>
          )}
        </div>
        <div className="max-h-[420px] overflow-y-auto">
          <BaseTable>
            <BaseTable.ColGroup>
              {headers.map((header) => (
                <col key={header.id} width={`${header.column.columnDef.size}%`} />
              ))}
            </BaseTable.ColGroup>
            <BaseTable.Head className="sticky top-0 h-12 border-y-0 bg-primary-black">
              <BaseTable.Row>
                {headers.map((header) => (
                  <HeaderCell
                    key={header.id}
                    className={cn('text-sm text-white bg-transparent', header.column.columnDef.meta?.headCellClass)}
                    header={header}
                  />
                ))}
              </BaseTable.Row>
            </BaseTable.Head>
            <BaseTable.Body className={cn('relative', dataRows.length > 0 ? 'border-b' : '!border-b-0')}>
              {dataRows.length > 0 ? (
                <>
                  {pinnedRows.map((row) => (
                    <BaseTable.Row key={row.id} className="bg-[#f9f9fb]">
                      {row.getVisibleCells().map((cell) => {
                        return <DataCell key={cell.id} cell={cell} className="h-[40px] font-bold p-2" />
                      })}
                    </BaseTable.Row>
                  ))}
                  {normalRows.map((row, index) => {
                    return (
                      <BaseTable.Row key={`${row.id}+${index}`} className={cn('h-auto')}>
                        {row.getVisibleCells().map((cell) => {
                          return (
                            <DataCell
                              key={cell.id}
                              cell={cell}
                              className={cn('p-2 h-[30px]', cell.column.columnDef.meta?.dataCellClass)}
                            />
                          )
                        })}
                      </BaseTable.Row>
                    )
                  })}
                </>
              ) : (
                <BaseTable.Row>
                  <DataCell className={emptyCellClass} colSpan={headers.length}>
                    <EmptyBox />
                  </DataCell>
                </BaseTable.Row>
              )}
            </BaseTable.Body>
          </BaseTable>
        </div>
      </div>
      <div className="flex-1 flex flex-col gap-4">
        <div className="bg-gray-light rounded border border-[#efefef]">
          <ModalTooltip id="bidKeyword" field="target" className="py-3 px-5 inline-flex">
            <span className="text-base font-bold">{t('rankMaintenance.targetBidding.modal.selectBiddingKeyword')}</span>
          </ModalTooltip>
          <div className="bg-white flex items-center justify-between py-3 px-5">
            <div className="w-full">
              <Listbox
                value={selected}
                onChange={selectSearchKeyword}
                disabled={isReadType || searchKeywords.length === 0 || isFormDisabled}
              >
                <div className="relative mt-1">
                  <Listbox.Button className="relative w-full border border-[#efefef] cursor-default rounded bg-white py-2 pl-3 pr-10 text-left focus:outline-none">
                    <span className="block truncate text-center">{selected?.searchKeyword ?? t('rankMaintenance.targetBidding.modal.pleaseSelect')}</span>
                    <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                      <MopIcon name={MOPIcon.CHEVRON_DOWN} />
                    </span>
                  </Listbox.Button>
                  <Transition
                    as={Fragment}
                    leave="transition ease-in duration-100"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                  >
                    <Listbox.Options className="absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none">
                      <div className="p-2 sticky top-0 bg-white z-10">
                        <MopSearch
                          size={25}
                          value={keywordSearch}
                          onChange={(e) => setKeywordSearch(e.target.value)}
                          placeholder="키워드 검색"
                          visibleIcon
                        />
                      </div>
                      {filteredKeywords.map((keyword, keywordIdx) => (
                        <Listbox.Option
                          key={keywordIdx}
                          className={({ active }) =>
                            `relative cursor-default select-none py-2 pl-10 pr-4 text-primary-black ${
                              active && 'bg-active-blue'
                            }`
                          }
                          value={keyword}
                        >
                          {({ selected }) => (
                            <CustomTooltip title={keyword.searchKeyword} placement="bottom">
                              <EllipsisText className={`block truncate ${selected ? 'font-medium' : 'font-normal'}`}>
                                {keyword.searchKeyword}
                              </EllipsisText>
                            </CustomTooltip>
                          )}
                        </Listbox.Option>
                      ))}
                    </Listbox.Options>
                  </Transition>
                </div>
              </Listbox>
            </div>
          </div>
        </div>
        <div className="bg-gray-light rounded border border-[#efefef]">
          <ModalTooltip id="bidTarget" field="target" className="py-3 px-5 inline-flex">
            <span className="text-base font-bold ">{t('rankMaintenance.targetBidding.modal.setBiddingTarget')}</span>
          </ModalTooltip>

          <Tab.Group selectedIndex={tabIndex} onChange={setTabIndex}>
            <Tab.List className="bg-[#f9f9fb] flex p-2 justify-between gap-4">
              {categories.map((category, i) => (
                <Tab
                  key={i}
                  onClick={(e: any) => {
                    if (isReadType || isFormDisabled) {
                      e.preventDefault()
                    } else {
                      changeBidCriteria(category.value)
                    }
                  }}
                  className={({ selected }) =>
                    cn(
                      'rounded border py-1 px-3 w-1/3',
                      selected ? 'bg-black font-bold border-black text-white' : 'bg-white font-normal border-[#efefef]',
                      (isReadType || isFormDisabled) && 'cursor-default'
                    )
                  }
                >
                  {category.tab}
                </Tab>
              ))}
            </Tab.List>
            <Tab.Panels className="bg-white py-3 px-5">
              {categories.map((category, i) => (
                <Tab.Panel key={i} className="flex items-center justify-between">
                  {/* <span className="text-sm"> {category.panel}</span> */}
                  <InputBox className="w-full">
                    <input
                      type="text"
                      name="bidTarget"
                      value={
                        category.value === BID_CRITERIA.AVG_RANK
                          ? bidSettingInfo.bidTarget
                          : bidSettingInfo.bidTarget.toLocaleString('ko-KR')
                      }
                      onChange={changeBidSetting}
                      className="w-full appearance-none focus:outline-none text-center"
                      readOnly={isReadType || isFormDisabled}
                    />
                    {category.isPercent && '%'}
                  </InputBox>
                </Tab.Panel>
              ))}
            </Tab.Panels>
          </Tab.Group>
        </div>
        <div className="bg-gray-light rounded border border-[#efefef]">
          <span className="text-base font-bold py-3 px-5 inline-block">{t('rankMaintenance.targetBidding.modal.detailedBiddingSetting')}</span>
          <div className="bg-[#f9f9fb] flex items-center justify-between py-3 px-5">
            <ModalTooltip id="currentBid" field="target">
              <span className="text-base inline-block">{t('rankMaintenance.targetBidding.modal.currentBidPrice')}</span>
            </ModalTooltip>

            <span className="pr-1">{numberWithCommas(bidSettingInfo.bidAmount ?? 0)} 원</span>
          </div>
          <div className="bg-white py-3 px-5 flex flex-col gap-3">
            <div className="flex items-center justify-between">
              <ModalTooltip id="maxCpc" field="target">
                <span className="text-base">{t('rankMaintenance.targetBidding.modal.maximumBidPrice')}</span>
              </ModalTooltip>
              <InputBox className="w-1/2">
                <input
                  type="text"
                  name="maxCpc"
                  value={bidSettingInfo.maxCpc.toLocaleString('ko-KR')}
                  onChange={changeBidSetting}
                  className="w-full text-right appearance-none focus:outline-none"
                  readOnly={isReadType || isFormDisabled}
                />
                원
              </InputBox>
            </div>
            <div className={isEN ? 'block' : 'flex items-center justify-between'}>
              <ModalTooltip id="bidIncremental" field="target">
                <span className="text-base">{t('rankMaintenance.targetBidding.modal.bidPriceFluctuation')}</span>
                <MopSwitch
                  checked={bidSettingInfo.incrementalUseYn === YNFlag.Y}
                  onChange={(value) => {
                    setBidSettingInfo((prev) => ({
                      ...prev,
                      incrementalUseYn: value ? YNFlag.Y : YNFlag.N
                    }))
                  }}
                  disabled={isReadType || isFormDisabled}
                />
              </ModalTooltip>

              <InputBox className={isEN ? 'w-full' : "w-1/2"}>
                <input
                  type="text"
                  name="incrementalValue"
                  value={bidSettingInfo.incrementalValue.toLocaleString('ko-KR')}
                  onChange={changeBidSetting}
                  className="w-full text-right appearance-none focus:outline-none"
                  readOnly={isReadType || bidSettingInfo.incrementalUseYn === YNFlag.N || isFormDisabled}
                />
                원
              </InputBox>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ShoppingBidKeywordSetting
