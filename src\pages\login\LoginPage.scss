#loginPage {
  width: 100%;
  height: 100%;
  background-color: #f2f2f2;
  padding-top: 0px;
  min-width: 100%;
  overflow: hidden;
  box-sizing: border-box;

  .login-title {
    height: 100px;
    line-height: 100px;
    font-size: 21px;
    font-weight: 200;
    color: var(--color-white);
    text-align: center;
    letter-spacing: 4px;
    background-color: var(--color-black);
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  }

  .login-subtitle {
    height: 47px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 47px;
    border: 1px solid var(--landing-red);
    background: #fff;
    box-shadow: 0px 1px 12px 0px rgba(225, 1, 6, 0.29);
    margin-bottom: 28px;
    padding: 5px 35px;
    color: var(--landing-red);
    font-size: 29px;
    font-weight: 700;
  }

  .login-form {
    width: 100%;
    min-height: calc(100vh - 290px);
    padding: 50px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20px;

    .logo-wrapper {
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .login-box {
      width: 400px;
      margin: 0px;

      .form-container {
        width: 100%;
        height: 75px;

        .MuiFormControl-root {
          margin: 0px 0px 0px 0px;

          .MuiFormHelperText-root {
            margin-top: 0px;
            font-size: 11px;
          }
        }

        .MuiOutlinedInput-root {
          width: 100%;
          height: 60px;
          padding: 0 11px;
          font-size: 18px;
          color: #121c3d;
          border-radius: 30px;
          background-color: var(--color-white);

          input {
            font-size: 18px !important;
          }

          input::placeholder {
            color: var(--point_color);
            opacity: 1;
          }

          input::-webkit-input-placeholder {
            color: var(--point_color);
          }

          input:-webkit-autofill,
          input:-webkit-autofill:hover,
          input:-webkit-autofill:focus,
          input:-webkit-autofill:active {
            -webkit-transition: background-color 9999s ease-out;
            -webkit-box-shadow: 0 0 0px 1000px #d0d7da inset !important;
          }

          .MuiOutlinedInput-notchedOutline {
            border-color: transparent;
          }
        }

        .MuiOutlinedInput-root {
          &.Mui-focused .MuiOutlinedInput-notchedOutline {
            border-color: #3f51b5;
          }

          &.Mui-error .MuiOutlinedInput-notchedOutline {
            border-color: #f44336;
          }
        }
      }

      #login-button {
        width: 100%;
        height: 60px;
        font-size: 18px;
        font-weight: 700;
        color: var(--color-white);
        border-radius: 30px;
        background-color: var(--color-black);
      }
    }
  }

  .login-footer {
    width: 100%;
    height: 190px;
    padding-bottom: 100px;
    position: relative;
    border-bottom: 30px solid var(--color-black);

    &::before {
      width: 100%;
      content: '';
      width: 100%;
      height: 1px;
      position: absolute;
      top: 12px;
      left: 0;
      background-color: #000;
    }

    ul.login-footer-container {
      padding-inline-start: 0px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      max-width: 900px;
      margin: 0 auto;

      li.login-footer-item {
        width: 130px;
        padding-top: 40px;
        position: relative;
        font-size: 18px;
        font-weight: 500;
        text-align: center;
        color: var(--color-black);
        list-style: none;

        .bg-login-bot {
          width: 81px;
          height: 25px;
          position: absolute;
          top: 0;
          left: 50%;
          transform: translateX(-50%);
        }
      }
    }
  }

  .collabo-line-color{
    line {
      stroke: #171717;
    }
  }

  @media (max-width: 640px) {
    .login-title {
      font-size: 14px;
    }

    .login-form {
      .logo-wrapper img {
        width: 50%;
        height: 50%;
      }
      .login-box {
        width: 300px;
        .form-container {
          height: 50px;

          .MuiFormControl-root {
            margin: 0px 0px 0px 0px;

            .MuiFormHelperText-root {
              margin-top: 0px;
              font-size: 8px;
            }
          }

          .MuiOutlinedInput-root {
            height: 40px;
            font-size: 12px;
            padding: 0 5px;
            border-radius: 20px;

            input {
              font-size: 12px !important;
              padding: 8px 10px;
            }
          }
        }
        #login-button {
          height: 40px;
          font-size: 12px;
          font-weight: 700;
          border-radius: 20px;
        }
      }
    }
    .login-footer {
      ul.login-footer-container {
        width: 100%;
        li.login-footer-item {
          width: 70px;
          font-size: 12px;
          font-weight: 500;
        }
      }
    }
  }
}
