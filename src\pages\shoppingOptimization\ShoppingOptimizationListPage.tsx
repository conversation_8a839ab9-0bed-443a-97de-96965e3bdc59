import React, { ReactElement, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ShoppingOptimizationListFilter from '@components/shoppingOptimization/ShoppingOptimizationListFilter';
import {
  deleteShoppingOptimization,
  getShoppingOptimizations,
  updateShoppingOptimizationBidYn,
  getShoppingOptimizationNewAdgroup,
} from '@api/shoppingOptimization/ShoppingOptimization';
import { Box, Button } from '@material-ui/core';
import { ContextMenuFunctions } from '@models/shoppingOptimization/ShoppingOptimization';
import ShoppingOptimizationList from '@components/shoppingOptimization/ShoppingOptimizationList';

import AddIcon from '@material-ui/icons/Add';
import { useRecoilState, useRecoilValue, useResetRecoilState } from 'recoil';
import { ssListActiveFilterState, ssListState } from '@store/ShoppingOptimization';

import './ShoppingOptimizationListPage.scss';
import ShoppingOptimizationDetailModal from '@components/shoppingOptimization/ShoppingOptimizationDetailModal';
import { StatusCode } from '@models/common/CommonResponse';
import { useAuthority, useToast, useDialog } from "@hooks/common";
import { StatusType } from '@models/optimization/Status';
import { MediaType } from '@models/common/Media';
import { getMediaByAdvertiserId } from '@api/common/Media';
import { PlatformType } from '@models/common/Platform';
import ShoppingOptimizationSelectAdTypeModal from '@components/shoppingOptimization/ShoppingOptimizationSelectAdTypeModal';
import ShoppingOptimizationNegativeKeywordModal from '@components/shoppingOptimization/ShoppingOptimizationNegativeKeywordModal';
import { ActionType } from '@models/common/CommonConstants';
import TagManager from 'react-gtm-module'
import TableHeaderRowCount from '@components/common/table/TableHeaderRowCount';
import { TransText } from '@components/common';
import { LiteBadge, ProBadge } from '@components/common/BaseChip';
import { getFunctionValue } from '@store/Advertiser';

const ShoppingOptimizationListPage: React.FC = (): ReactElement => {
  const { t } = useTranslation();
  const { advertiser, hasAuthority } = useAuthority();
  const { openToast } = useToast();
  const { openDialog } = useDialog();
  const [ssListActiveFilter, setSsListActiveFilter] = useRecoilState(ssListActiveFilterState);
  const resetSsListActiveFilter = useResetRecoilState(ssListActiveFilterState);
  const [ssList, setSsList] = useRecoilState(ssListState);
  const resetSsList = useResetRecoilState(ssListState);

  const [initFlag, setInitFlag] = useState<boolean>(true);
  const [linkedMedia, setLinkedMedia] = useState<MediaType[]>([]);

  const [optimizationId, setOptimizationId] = useState<number>(0);
  const [optimizationName, setOptimizationName] = useState<string>('');
  const [modalType, setModalType] = useState<ActionType>(ActionType.CREATE);
  const [openShoppingOptimizationDetail, setOpenShoppingOptimizationDetail] = useState<boolean>(false);
  const [openSelectAdType, setOpenSelectAdType] = useState<boolean>(false);
  const [openNegativeKeyword, setOpenNegativeKeyword] = useState<boolean>(false);
  const [saShoppingType, setSaShoppingType] = useState<string>('');
  const [refreshList, setRefreshList] = useState<boolean>(false);
  const [deleteOptimization, setDeleteOptimization] = useState<boolean>(false);
  const [hasNewAdgroup, setHasNewAdgroup] = useState<string>('N');
  const totalAdgroupsCount = useRecoilValue(getFunctionValue({ functionType: 'UI', functionId: 'SPA_OPT_TARGET_COUNT' })) ?? 50

  const contextMenuFunctions: ContextMenuFunctions = {
    requestEdit: (id: number) => {
      const findOptimization = ssList.optimizations.find((optimization) => optimization.optimizationId === Number(id));
      if (findOptimization?.bidYn === 'Y') {
        openToast(t('optimization.message.shoppingOptimization.toast.impossibleEdit.bidYn'))
      } else if (findOptimization?.status === StatusType.INSPECTING) {
        openToast(t('optimization.message.shoppingOptimization.toast.impossibleEdit.inspecting'))
      } else {
        setOptimizationId(id);
        setModalType(ActionType.MODIFY);
        setOpenShoppingOptimizationDetail(true);
        setSaShoppingType(findOptimization!.saShoppingType);
      }
    },
    requestDelete: (id: number) => {
      const findOptimization = ssList.optimizations.find((optimization) => optimization.optimizationId === id);
      if (findOptimization?.bidYn === 'Y') {
        openToast(t('optimization.message.shoppingOptimization.toast.impossibleDelete'))
        return;
      }
      setOptimizationId(id);
      openDialog({
        title: t('common.message.title.notice'),
        message: t('optimization.message.shoppingOptimization.dialog.deleteAlertMessage'),
        cancelLabel: t('common.label.button.cancel'),
        actionLabel: t('common.label.button.delete'),
        onAction: () => {
          deleteModalOnAction();
        }
      });
    },
    requestBidOnOff: (id: number, onOff: boolean) => {
      updateShoppingOptimizationBidYn(id, onOff ? 'Y' : 'N').then((response) => {
        if (response.successOrNot === 'Y' && response.statusCode === StatusCode.SUCCESS) {
          setRefreshList(true);
        } else {
          openToast(t('optimization.message.shoppingOptimization.toast.updateBidYnFail'))
        }
      });
    },
    requestRead: (id: number) => {
      const findOptimization = ssList.optimizations.find((optimization) => optimization.optimizationId === Number(id));
      setOptimizationId(id);
      setModalType(ActionType.READ);
      setOpenShoppingOptimizationDetail(true);
      setSaShoppingType(findOptimization!.saShoppingType);
    },
    requestNegativeKeyword: (id: number) => {
      const findOptimization = ssList.optimizations.find((optimization) => optimization.optimizationId === Number(id));
      setOptimizationId(Number(id));
      setSaShoppingType(findOptimization!.saShoppingType);
      setOptimizationName(findOptimization!.optimizationName);
      setOpenNegativeKeyword(true);
    },
    requestDuplicate: (id: number) => {
      const findOptimization = ssList.optimizations.find((optimization) => optimization.optimizationId === Number(id));
      setOptimizationId(id);
      setModalType(ActionType.DUPLICATE);
      setOpenShoppingOptimizationDetail(true);
      setSaShoppingType(findOptimization!.saShoppingType);
    }
  };

  const deleteModalOnAction = () => {
    setDeleteOptimization(true);
  };

  const handleCreateShoppingOptimization = (saShoppingType: string) => {
    setOpenSelectAdType(false);
    if (saShoppingType) {
      setModalType(ActionType.CREATE);
      setSaShoppingType(saShoppingType);
      setOpenShoppingOptimizationDetail(true);
    }
  };

  const handleDetailModalClose = (saveYn: string) => {
    setOpenShoppingOptimizationDetail(false);
    setSaShoppingType('');

    if (saveYn === 'Y') {
      setRefreshList(true);
    }
  };

  useEffect(() => {
    if (deleteOptimization && optimizationId) {
      deleteShoppingOptimization(optimizationId).then((response) => {
        if (response.successOrNot === 'Y' && response.statusCode === StatusCode.SUCCESS) {
          openToast(t('optimization.message.shoppingOptimization.toast.deleteSuccess'))
          if (
            ssListActiveFilter.pageIndex > 1 &&
            ssList.totalCount === ssListActiveFilter.pageSize * (ssListActiveFilter.pageIndex - 1) + 1
          ) {
            setSsListActiveFilter({ ...ssListActiveFilter, pageIndex: ssListActiveFilter.pageIndex - 1 });
          }
          ssListActiveFilter &&
            getShoppingOptimizations(ssListActiveFilter).then((optimizations) => {
              setSsList(optimizations);
            });
        } else {
          openToast(t('optimization.message.shoppingOptimization.toast.deleteFail'))
        }
      });
      setDeleteOptimization(false);
    }

    if (refreshList === true) {
      ssListActiveFilter &&
        getShoppingOptimizations(ssListActiveFilter).then((response) => {
          setSsList(response);
        });
      setRefreshList(false);
    }
  }, [refreshList, deleteOptimization]); // eslint-disable-line

  useEffect(() => {
    let isSubscribed = true;
    if (!initFlag && Object.keys(ssListActiveFilter).length !== 0) {
      getShoppingOptimizations(ssListActiveFilter).then((response) => {
        if (isSubscribed) {
          setSsList(response);
        }
      });
    }
    return () => {
      isSubscribed = false;
    };
  }, [ssListActiveFilter]); // eslint-disable-line

  const getLinkedMedia = async () => {
    const response = await getMediaByAdvertiserId(advertiser.advertiserId);
    setLinkedMedia(response?.[PlatformType.SA] || []);
  };

  const getNewAdgroup = async () => {
    const result = await getShoppingOptimizationNewAdgroup({advertiserId: advertiser.advertiserId});
    setHasNewAdgroup(result);
  }

  useEffect(() => {
    setInitFlag(false);
    getLinkedMedia();
    getNewAdgroup();
    return () => {
      resetSsListActiveFilter();
      resetSsList();
    };
  }, []); //eslint-disable-line

  return (
    <div id="ShoppingOptimizationListPage">
      <ShoppingOptimizationListFilter />
      <Box className="listHeaderWrapper">
        <div className="listLeftWrapper">
          {hasAuthority && (
            <Button
              id="createButton"
              className={hasNewAdgroup === 'Y' ? 'new' : ''}
              data-testid="createButton"
              data-gtm-id="spa-opt-create-click"
              variant="contained"
              onClick={(e) => {
                const gtmEventId = e.currentTarget.dataset.gtmId
                TagManager.dataLayer({
                  dataLayer: {
                    event: 'click',
                    gtm_id: gtmEventId
                  }
                })
                
                if (ssList.currentItemsCount === undefined || (ssList.maxItemsCount !== -1 && ssList.currentItemsCount >= ssList.maxItemsCount)) {
                  openDialog({
                    title: t('optimization.message.shoppingOptimization.dialog.maxItem.title'),
                    actionLabel: t('common.button.confirm'),
                    render: (
                      <>
                        <TransText
                          className="text-left"
                          as="p"
                          i18nKey={'optimization.message.shoppingOptimization.dialog.maxItem.contents.0'}
                        />
                        <div className="!mt-2.5 !p-2.5 leading-relaxed bg-[#F2F3F6] rounded-sm">
                          <div className="flex flex-col gap-y-2 justify-center items-center w-[360px]">
                            <div className="flex gap-3 justify-start items-center">
                              <ProBadge disabled={false} className="mb-[-3px]" />
                              <TransText
                                className="text-left"
                                as="p"
                                i18nKey={'optimization.message.shoppingOptimization.dialog.maxItem.contents.1'}
                              />
                            </div>
                            <div className="flex gap-3 justify-start items-center w-[304px]">
                              <LiteBadge disabled={false} className="mb-[-3px]" />
                              <TransText
                                className="text-left"
                                as="p"
                                i18nKey={'optimization.message.shoppingOptimization.dialog.maxItem.contents.2'}
                              />
                            </div>
                          </div>
                        </div>
                      </>
                    ),
                    onAction: () => {}
                  });
                } else {
                  setOpenSelectAdType(true)
                }
              }}
              color="primary"
              endIcon={<AddIcon />}
            >
              {t('common.label.button.create')}
            </Button>
          )}
          <TableHeaderRowCount
            current={ssList.currentAdgroupsCount}
            total={totalAdgroupsCount}
            text={t('optimization.message.shoppingOptimization.list.numberOfBidOptAdGroups')}
            plan={advertiser.subscriptionProductType}
          >
            <>
              <TransText
                as="h1"
                i18nKey={'optimization.message.shoppingOptimization.tooltip.title'}
              />
              <div className="common-style">
                <TransText
                  as="p"
                  i18nKey={'optimization.message.shoppingOptimization.tooltip.contents.0'}
                />
                <div className="!mt-2.5 !py-2.5 align-middle leading-relaxed bg-[#F2F3F6] rounded-sm">
                  <div className="flex flex-col justify-center items-center">
                    <div className="flex justify-start items-center w-[260px]">
                      <ProBadge disabled={false} className="mb-[-3px]" />
                      <TransText
                        as="p"
                        i18nKey={'optimization.message.shoppingOptimization.tooltip.contents.1'}
                      />
                    </div>
                  </div>
                  <div className="flex justify-center items-center">
                    <div className="flex justify-start items-center w-[260px]">
                      <LiteBadge disabled={false} className="mb-[-3px]" />
                      <TransText
                      as="p"
                      i18nKey={'optimization.message.shoppingOptimization.tooltip.contents.2'}
                    />
                    </div>
                  </div>
                </div>
              </div>
            </>
        </TableHeaderRowCount>
        </div>
      </Box>
      <ShoppingOptimizationList contextMenuFunctions={contextMenuFunctions} />
      {openShoppingOptimizationDetail && (
        <ShoppingOptimizationDetailModal
          open={openShoppingOptimizationDetail}
          type={modalType}
          optimizationId={optimizationId}
          linkedMedia={linkedMedia}
          saShoppingType={saShoppingType}
          onClose={(saveYn) => handleDetailModalClose(saveYn)}
          remainAdgroupsCount={Number(totalAdgroupsCount) - Number(ssList.currentAdgroupsCount)}
        />
      )}
      {openSelectAdType && (
        <ShoppingOptimizationSelectAdTypeModal
          open={openSelectAdType}
          onClose={(saShoppingType) => {
            handleCreateShoppingOptimization(saShoppingType)
          }}
        />
      )}
      {openNegativeKeyword && (
        <ShoppingOptimizationNegativeKeywordModal
          optimizationId={optimizationId}
          optimizationName={optimizationName}
          saShoppingType={saShoppingType}
          open={openNegativeKeyword}
          onClose={() => {
            setOpenNegativeKeyword(false)
          }}
        />
      )}
    </div>
  )
};

export default ShoppingOptimizationListPage;
