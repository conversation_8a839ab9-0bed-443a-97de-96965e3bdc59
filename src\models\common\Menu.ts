import { ReactElement } from 'react'

export enum MenuGroupType {
  SPEND_PACING = 'spendPacing',
  BID_OPT = 'bidOpt',
  TOOLS = 'tools',
  BUDGET_OPT = 'budgetOpt',
  REPORT = 'report',
  INSIGHT = 'insight',
  ADMIN_OPTIMIZATION = 'adminOptimization',
  ADMIN_BATCH = 'adminBatch',
  ADMIN_USER = 'adminUser',
  INSIGHT_CONTRIBUTION = 'contribution',
  INSIGHT_BUDGET = 'budget',
  INSIGHT_COMPETITION = 'competition',
  SETTING = 'setting',
  SA = 'sa',
  SA_SHOPPING = 'saShopping',
  AD_BUDGET = 'adBudget',
  API_CENTER = 'apiCenter'
}

export enum MenuType {
  RAW_DATA_REPORT = 'report.raw_data',
  CAMPAIGN_REPORT = 'report.campaign',
  CREATE_CAMPAIGN_NAVER_REPORT = 'report.createCampaignNaverReport',
  OPTIMIZATION_REPORT = 'report.optimization',
  SA_REPORT = 'report.sa',
  SHOP_REPORT = 'report.shop',
  DVA_REPORT = 'report.dva',
  ANOMALY_DETECTION_URL = 'report.anomalyDetectionUrl',
  ANOMALY_DETECTION_UTM = 'report.anomalyDetectionUtm',
  ANOMALY_DETECTION_SPA = 'shop.anomalyDetectionSpa',
  BID_OPT_SA = 'sa.bid_opt',
  BID_OPT_SHOP = 'shop.bid_opt',
  BUDGET_OPT_DVA = 'budget_opt.dva',
  RANKMAINTENANCE_SA = 'sa.rankMaintenance',
  TOOLS_SHOP = 'tools.shop',
  TOOLS_CREATIVE = 'tools.creative',
  COMPETITION_SA = 'sa.competition',
  COMPETITION_SHOP = 'shop.competition',
  TARGET_BID = 'shop.targetBid',
  CREATE_CAMPAIGN = 'shop.createCampaign',
  NEGATIVE_KEYWORD = 'shop.negativeKeyword',
  BUDGET_SA = 'sa.budget',
  BUDGET_SHOP = 'shop.budget',
  ADMIN_OPTIMIZATION = 'admin.optimization',
  ADMIN_RANKMAINTENANCE = 'admin.rankMaintenance',
  ADMIN_BATCHHISTORY = 'admin.batchHistory',
  ADMIN_BATCH_TIMETABLE = 'admin.batchTimeTable',
  MEMBER_ENROLL = 'admin.memberEnroll',
  OAUTH = 'admin.oauth',
  UTM_RULES = 'admin.utmRules',
  ADMIN_AUTHORITY = 'admin.authority',
  SETTING_CIRCLE = 'setting.circle',
  DATA_CONNECT = 'setting.dataConnect',
  ACCESS_MANAGE = 'setting.accessManage',
  SUBSCRIPTION = 'setting.subscription',
  SPEND_PACING = 'adBudget.spendPacing',
  BUDGET_OPT = 'adBudget.budgetOpt',
  CONTRIBUTION = 'adBudget.contribution'
}

export interface MenuGroupItem {
  type: MenuGroupType
  icon: () => ReactElement
  subMenuItems?: MenuItem[]
  pathname?: string
  hidden?: Boolean
}

export interface MenuItem {
  type: MenuType
  pathname: string
  hidden?: Boolean
}
