import React, { ReactElement } from 'react';
import './ShoppingOptimizationList.scss';
import { pageSizeOptions } from '@models/common/CommonConstants';
import { ContextMenuFunctions, ShoppingOptimizationInfo } from '@models/shoppingOptimization/ShoppingOptimization';
import { useTranslation } from 'react-i18next';
import { FixedLayoutTable, TablePagination, FixedLayoutColumn } from '@components/common/table'
import ShoppingOptimizationListFormatter from '@components/shoppingOptimization/ShoppingOptimizationListFormatter';
import { useRecoilState, useRecoilValue } from 'recoil';
import { ssListActiveFilterState, ssListState } from '@store/ShoppingOptimization'

interface Props {
  contextMenuFunctions?: ContextMenuFunctions;
}

const shoppingOptimizationListFormatter = new ShoppingOptimizationListFormatter();

const ShoppingOptimizationList: React.FC<Props> = ({ contextMenuFunctions }: Props): ReactElement => {
  const { t } = useTranslation();

  const ssList = useRecoilValue(ssListState);
  const [ssListActiveFilter, setSsListActiveFilter] = useRecoilState(ssListActiveFilterState);
  const allColumns: Array<FixedLayoutColumn<ShoppingOptimizationInfo>> = shoppingOptimizationListFormatter.getColumnFormat(
    contextMenuFunctions,
    ssListActiveFilter.orderBy,
    ssListActiveFilter.sorting,
    ssList,
  );

  const handleChangePage = (newPage: number) => {
    if (newPage !== ssListActiveFilter.pageIndex) {
      setSsListActiveFilter({ ...ssListActiveFilter, pageIndex: newPage });
    }
  };

  const handleChangeRowsPerPage = (newRowsPerPage: number) => {
    if (newRowsPerPage !== ssListActiveFilter.pageSize) {
      setSsListActiveFilter({ ...ssListActiveFilter, pageSize: newRowsPerPage, pageIndex: 1 });
    }
  };

  const handleOrderChange = (orderBy: number, orderDirection: 'asc' | 'desc') => {
    setSsListActiveFilter({
      ...ssListActiveFilter,
      orderBy: allColumns[orderBy]?.field,
      sorting: orderDirection.toUpperCase(),
      pageIndex: 1,
    });
  };

  return (
    <div id="shoppingOptimizationList">
      {ssList.optimizations && allColumns && (
        <>
          <FixedLayoutTable
            data-testid="shoppingOptimizationTable"
            onOrderChange={handleOrderChange}
            columns={allColumns}
            data={ssList.optimizations.map((obj) => Object.create(obj)) || []}
            localization={
              ssList.optimizations.length === 0
                ? { body: { emptyDataSourceMessage: t('common.message.list.noData') } }
                : { body: { emptyDataSourceMessage: '' } }
            }
          />
          <TablePagination
            id="shopping-optimization-list-pagination"
            totalCount={ssList.totalCount || 0}
            page={ssListActiveFilter.pageIndex || 1}
            rowsPerPage={ssListActiveFilter.pageSize || pageSizeOptions[0]}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        </>
      )}
    </div>
  );
};

export default ShoppingOptimizationList;
