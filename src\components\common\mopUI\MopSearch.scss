.mop-search-wrapper {
  --height: 36px;
  width: 100%;
  height: var(--height);
  border: 1px solid #bbbdcd;
  border-radius: calc(var(--height) / 2);
  text-align: center;
  padding: 4px 8px;
  display: flex;
  align-items: center;
  background-color: white;
  .mop-search__input {
    flex: 1;
    height: 100%;
    background-color: transparent;
    border: none;
    color: #333;

    &::placeholder {
      font-family: 'NotoSans';
      font-weight: 400;
      font-size: 13px;
      color: #707070;
    }

    &:focus {
      outline: none;
    }
  }
  .mop-icon-box { cursor: pointer; padding: 0px; }
}

.raw-data {
  --height: 27px;
}

.commerce {
  --height: 42px;
  border-radius: 4px;
  border: 1px solid #efefef;
  padding: 10px 20px;
  gap: 10px;
  .mop-search__input {
    font-size: 14px;
    text-align: right;
  }
}

.disabled {
  background-color: #f6f8f9;

  .mop-search__input {
    background-color: #f6f8f9;
  }
}