/* 기본 컨테이너: 가로/세로 스크롤 허용 */
.pinnedTableContainer {
  position: relative;
  overflow: auto;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #ffffff;
}

/* 테이블 기본 스타일 */
.pinnedTable {
  border-collapse: separate;
  border-spacing: 0;
  width: max-content;
  min-width: 100%;
  table-layout: auto;
}

.th,
.td {
  padding: 10px 12px;
  border-bottom: 1px solid #e5e7eb;
  background: #ffffff;
  white-space: nowrap;
}

/* 헤더 스타일 및 sticky */
.stickyHeader {
  position: sticky;
  top: 0;
  z-index: 3;
  background: #f9fafb;
  color: #111827;
  font-weight: 600;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

/* 첫 번째 열 sticky (헤더/셀 공통) */
.stickyFirstCol {
  position: sticky;
  left: 0;
  background: #ffffff;
  box-shadow: 2px 0 0 0 #e5e7eb;
}

/* 다중 고정 컬럼 공통 클래스 */
.stickyLeft {
  position: sticky;
  background: #ffffff;
}

/* 마지막 고정 컬럼 우측 경계선 표현 */
.stickyLeftEdge {
  box-shadow: 2px 0 0 0 #e5e7eb;
}

/* pinned body row sticky (상단 고정) */
.stickyRow {
  position: sticky;
  top: 0; /* 각 셀에 인라인 top으로 개별 보정됨 */
  background: #ffffff;
}

/* 줄무늬 행 */
.pinnedTable tbody tr:nth-child(odd) .td:not(.stickyFirstCol) {
  background: #fcfdff;
}

/* 헤더와 첫 컬럼이 만나는 좌상단 셀 시각적 보정 */
.pinnedTable thead tr:first-child th.stickyFirstCol {
  box-shadow: 2px 0 0 0 #e5e7eb, 0 1px 0 0 #e5e7eb;
}

/* 스크롤 시 성능 향상 */
.pinnedTableContainer,
.stickyHeader,
.stickyFirstCol {
  will-change: transform;
}

/* 무한 스크롤 센티널 & 상태 표시 */
.pinnedTableSentinel {
  height: 1px;
}

.loadMoreStatus {
  padding: 8px 12px;
  font-size: 12px;
  color: #6b7280;
}

/* Row Panel */
.rowPanelCell {
  background: #f9fafb;
  white-space: normal;
  padding: 0;
}

.rowPanelInner {
  margin-right: -3px;
}

/* Panel inner tables: match pinned table border */
.panelTable {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #ffffff;
}

.panelTable th,
.panelTable td {
  padding: 8px;
  border-bottom: 1px solid #e5e7eb;
  text-align: left;
}

.panelTable thead th {
  background: #f3f4f6;
  font-weight: 600;
}
