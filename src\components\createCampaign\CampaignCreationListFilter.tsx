import React, { ReactElement, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Grid, MenuItem } from '@material-ui/core'
import { FilterSelectType } from '@models/common/CommonConstants'

import PageListFilter from '@components/common/page/PageListFilter'
import PageListFilterGroup from '@components/common/page/PageListFilterGroup'

import './CampaignCreationListFilter.scss'
import { AD_REVIEW_STATUS, CAMPAIGN_CREATION_STATUS } from '@models/createCampaign/CreateCampaign'

interface FilterType {
  status: string
  adReview: string
}

interface CampaignCreationListFilterProps {
  onFilterChange: (filter: FilterType) => void
}

const CampaignCreationListFilter: React.FC<CampaignCreationListFilterProps> = ({ onFilterChange }): ReactElement => {
  const { t } = useTranslation()

  const filterDefault = {
    status: FilterSelectType.ALL,
    adReview: FilterSelectType.ALL
  }

  const [filter, setFilter] = useState<FilterType>(filterDefault)

  
  const handleChangeStatus = (
    event: React.ChangeEvent<{
      name?: string | undefined
      value: unknown
    }>
  ) => {
    const newFilter = Object.assign({}, filter)
    if ((event.target.value as string) === FilterSelectType.ALL) {
      newFilter.status = FilterSelectType.ALL
    } else {
      newFilter.status = event.target.value as string
    }
    setFilter(newFilter)
  }

  const handleChangeAdReview = (
    event: React.ChangeEvent<{
      name?: string | undefined
      value: unknown
    }>
  ) => {
    const newFilter = Object.assign({}, filter)
    if ((event.target.value as string) === FilterSelectType.ALL) {
      newFilter.adReview = FilterSelectType.ALL
    } else {
      newFilter.adReview = event.target.value as string
    }
    setFilter(newFilter)
  }

  useEffect(() => {
    onFilterChange(filter)
  }, [filter.status, filter.adReview]) //eslint-disable-line

  return (
    <div id="campaignCreationListFilter">
      <Grid container className="search-label-container" justifyContent="center">
        <PageListFilterGroup>
          <PageListFilter
            name="status"
            label={t('createCampaign.label.filter.status')}
            value={filter.status}
            onChange={handleChangeStatus}
            width={224}
            dropdownSize={40}
          >
            <MenuItem value={CAMPAIGN_CREATION_STATUS.SUCCESS}>{t('createCampaign.label.status.success')}</MenuItem>
            <MenuItem value={CAMPAIGN_CREATION_STATUS.PENDING}>{t('createCampaign.label.status.pending')}</MenuItem>
            <MenuItem value={CAMPAIGN_CREATION_STATUS.PARTIAL_FAIL}>{t('createCampaign.label.status.partialFailure')}</MenuItem>
            <MenuItem value={CAMPAIGN_CREATION_STATUS.FAIL}>{t('createCampaign.label.status.totalFailure')}</MenuItem>
          </PageListFilter>

          <PageListFilter
            name="adReview"
            label={t('createCampaign.label.filter.adReview')}
            value={filter.adReview}
            onChange={handleChangeAdReview}
            width={224}
            dropdownSize={40}
          >
            <MenuItem value={AD_REVIEW_STATUS.APPROVED}>{t('createCampaign.label.adReview.fullyApproved')}</MenuItem>
            <MenuItem value={AD_REVIEW_STATUS.UNDER_REVIEW}>{t('createCampaign.label.adReview.inProgress')}</MenuItem>
            <MenuItem value={AD_REVIEW_STATUS.PENDING}>{t('createCampaign.label.adReview.pending')}</MenuItem>
            <MenuItem value={AD_REVIEW_STATUS.ELIGIBLE}>{t('createCampaign.label.adReview.eligible')}</MenuItem>
            <MenuItem value={AD_REVIEW_STATUS.DENIED}>{t('createCampaign.label.adReview.fullyRejected')}</MenuItem>
          </PageListFilter>
        </PageListFilterGroup>
      </Grid>
    </div>
  )
}

export default CampaignCreationListFilter
