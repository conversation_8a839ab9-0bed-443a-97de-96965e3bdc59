#BudgetAnalysisPage {
  .tab-icon-box {
    --icon-bg: #e1e6ff;
    --size: 40px;
  }
  .tab-label-name {
    color: var(--color-active-blue);
    font-weight: 700;
    font-size: 20px;
  }
  .report-body-wrapper {
    width: auto;
    margin: 0 auto;
  }
  .report-body-filter-wrapper {
    padding: 33px 0;
    background-color: var(--color-gray-light);
  }
  .empty-detection-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #f2f3f6;
    width: 100%;
    height: 100%;
    min-height: inherit;

    p {
      white-space: pre-line;
      text-align: center;
      font-size: 24px;
      font-weight: 700;
      margin-top: 32px;
      color: var(--point_color);
    }
  }

  .report-content-header {
    max-width: 2388px;
    position: relative;
    margin: 27px auto;
  }

  .report-viewtype-tab {
    display: flex;
    margin: 0 auto;
  }

  .sorting-button-box {
    display: flex;
    width: fit-content;
    border-radius: 9999px;
    overflow: hidden;
    border: 1px solid var(--point_color);
    position: absolute;
    right: 0;
    top: 0;

    button {
      border: none;
      background-color: white;
      font-size: 12px;
      font-weight: 700;
      height: 32px;
      padding: 0 16px;
      cursor: pointer;

      > svg {
        display: inline;
      }
      &:first-child {
        border-right: 1px solid var(--point_color);
      }
      svg {
        // visibility: hidden;
      }
      &.asc {
        svg {
          visibility: visible;
          transform: rotate(180deg);
          margin-bottom: 2px;
        }
      }
      &.desc {
        svg {
          visibility: visible;
        }
      }
    }
  }

  .grid-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    margin: 16px auto;
    max-width: 2388px;
    gap: 32px 28px;

    .grid-item {
      background-color: #fff;
      padding: 16px 42px;
      border-bottom: 1px solid var(--point_color);
      position: relative;

      &.activated {
        z-index: 100;
      }
      &:nth-child(even) {
        .panel {
          left: -100%;
          right: 0;
        }
      }
      &.placeholder {
        grid-column: auto / span 2;
        height: 300px;
        background: transparent;
      }

      .live-chip {
        flex: 1;
        display: flex;
        align-items: center;
        .live {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          height: 16px;
          width: 48px;
          font-size: 9px;
          color: var(--point_color);
          background-color: #6ec7c2;
          border-radius: 8px;
        }
        .off {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          height: 16px;
          width: 48px;
          font-size: 9px;
          color: var(--point_color);
          background-color: #cccccc;
          border-radius: 8px;
        }
      }

      .optimization-name {
        font-size: 25px;
        font-weight: 800px;
        color: var(--point_color);

        &.new::after {
          content: 'New';
          position: relative;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          height: 16px;
          width: 36px;
          font-size: 9px;
          font-weight: 700;
          color: white;
          background-color: var(--color-active-blue);
          border-radius: 8px;
          margin-left: 6px;
          top: -5px;
        }
      }

      .detail {
        display: flex;
        margin-top: 8px;
        .detail-item {
          flex: 1;
          height: 374px;
          box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.25);
          &.left {
            background-color: #f2f3f6;
          }
          &.right {
            background-color: #fff;
            padding: 30px 20px 10px 10px;
            position: relative;
            .date {
              position: absolute;
              top: 8px;
              right: 10px;
              padding: 0 10px;
              height: 18px;
              line-height: 18px;
              border-radius: 9px;
              background-color: var(--point_color);
              color: #fff;
              font-size: 12px;
              font-weight: 400;
            }
            canvas {
              margin: 0 auto;
            }
          }

          .detail-grid {
            display: grid;
            grid-template-columns: 124px 170px;
            margin: 18px 18px 10px 18px;
            .name {
              color: var(--point_color);
              font-size: 14px;
              font-weight: 700;
              height: 42px;
              line-height: 42px;
            }
            .value {
              font-size: 14px;
              font-weight: 500;
              height: 42px;
              line-height: 42px;
              border-bottom: 1px solid var(--border-spacer);
              &:last-child {
                border-bottom-width: 0;
              }
              .icon-image {
                width: 22px;
                top: 5px;
                display: inline-block;
                position: relative;
                margin-right: 6px;
              }
              .go-opt-button {
                border: 1px solid #e4e7ee;
                background-color: white;
                border-radius: 9999px;
                color: var(--color-blue-darker);
                font-size: 12px;
                align-items: center;
                display: inline-flex;
                padding: 4px 8px;
                font-weight: 500;
                margin: 0 4px;
                cursor: pointer;
                svg {
                  margin-left: 8px;
                }
              }
            }
          }

          .toggle-container {
            display: grid;
            grid-template-columns: 124px 1fr;
            background-color: #fff;
            padding: 18px;
            .name {
              color: var(--point_color);
              font-size: 14px;
              font-weight: 700;
              display: flex;
              align-items: center;
              gap: 4px;
              line-height: 0;
            }
            .toggle-wrapper {
              .toggle {
                display: inline-block;
                appearance: none;
                height: 18px;
                line-height: 18px;
                border-radius: 9px;
                font-family: inherit;
                font-size: 12px;
                font-weight: 500;
                border: none;
                padding: 0 10px;
                margin: 0 6px 4px 0;
                cursor: pointer;
                &.impressions {
                  color: #fff;
                  background-color: #00359c;
                }
                &.clicks {
                  color: #fff;
                  background-color: #ff5c00;
                }
                &.revenue {
                  color: #fff;
                  background-color: #774cf2;
                }
                &.conversions {
                  color: var(--point_color);
                  background-color: #ffd600;
                }
                &.top-impression-share {
                  color: var(--point_color);
                  background-color: #adda2f;
                }
                &.off {
                  color: #fff !important;
                  background-color: #ccc !important;
                }
              }
            }
          }
          .bottom {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 28px;
            .learn-more {
              display: flex;
              justify-content: center;
              align-items: center;
              gap: 4px;
              appearance: none;
              font-family: inherit;
              font-size: 14px;
              font-weight: 700;
              border: none;
              background: transparent;
              cursor: pointer;
            }
          }
        }
      }

      .panel {
        position: absolute;
        display: flex;
        top: 100%;
        left: 0;
        right: calc(-100% - 28px);
        border-top: 1px solid var(--point_color);
        height: 345px;
        min-width: 311px;
        max-width: 2177px;
        overflow-x: auto;
        background-color: #fff;
        z-index: 100;

        .panel-item {
          border-right: 1px solid var(--border-spacer);
          position: relative;
          .dday {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 27px;
            background-color: #f2f3f6;
            color: var(--color-active-blue);
            font-size: 18px;
            font-weight: 700;
          }
          .chart {
            padding: 30px 20px 10px 10px;
            margin-right: 10px;
          }
          .date {
            position: absolute;
            top: 40px;
            right: 10px;
            padding: 0 10px;
            height: 16px;
            line-height: 16px;
            border-radius: 8px;
            background-color: #d9d9d9;
            color: var(--point_color);
            font-size: 10px;
            font-weight: 700;
          }
        }
      }
    }

    .dim {
      position: absolute;
      top: calc(0px - var(--tab-height) - var(--tab-top));
      left: -24px;
      right: -24px;
      bottom: -24px;
      z-index: 50;
      background-color: rgba(0, 0, 0, 0.25);
    }
  }

  .list-container {
    max-width: 2388px;
    margin: 16px auto 0;
    position: relative;
    background-color: white;
    overflow-y: scroll;
    scrollbar-gutter: stable both-edges;
    padding-bottom: 12px;

    &:empty {
      background-color: transparent;
      padding: 0;
      width: 0;
      height: 0;
    }

    .listview-header-wrapper {
      padding-top: 12px;
      background-color: white;
      position: sticky;
      top: 0;
      left: 0;
      z-index: 1000;
    }
  }
  .budget-analysis-filter-group {
    max-width: 1678px;
    margin: 0 auto;
    .list-item-filter {
      flex: 3;
      &--bidYn,
      &--mediaType {
        flex: 2;
      }
    }
  }
}

#budget-analysis-advice-tooltip {
  border: 1px solid var(--point_color);
  background-color: #fff;
  .MuiTooltip-tooltip {
    padding: 0 0 12px 0;
    margin: 0;
    background-color: transparent;
    .border-bottom {
      border-bottom: 1px solid #9196a4;
    }
    .indent1 {
      padding: 0 12px 0 14px;
      text-indent: -14px;
    }
    .indent2 {
      padding: 0 12px 0 30px;
      text-indent: -14px;
    }
    .indent3 {
      padding: 0 12px 0 38px;
      text-indent: -38px;
    }
    > div {
      color: var(--point_color);
      font-size: 12px;
      font-weight: normal;
      > h1,
      > h2,
      > h3,
      > h4,
      > h5,
      > h6 {
        font-size: 12px;
        font-weight: bold;
        margin: 0;
        padding: 12px 0px;
        border-bottom: 1px solid #9196a4;
        font-weight: 700;
        text-align: center;
      }
      > div {
        padding: 8px 12px 6px;
        line-height: 1.5;
        ul {
          margin: 0;
          padding-inline-start: 2em;
        }
      }
    }
    > div > ul {
      color: var(--point_color);
      font-size: 12px;
      font-weight: 500;
    }
    .MuiTooltip-arrow::before {
      border-color: var(--point_color);
    }
  }
}

.budget-analysis-popover .filter-item-group {
  align-items: center;
}
