import { Box, Button, ButtonGroup } from '@material-ui/core'
import React, { useState, ReactElement } from 'react'
import { FixedLayoutTable, TablePagination, FixedLayoutColumn } from '@components/common/table'
import { RankMaintenanceContextMenuFunctions, RankMaintenanceInfo } from '@models/rankMaintenance/RankMaintenance'
import RankMaintenanceListFormatter from '@components/rankMaintenance/RankMaintenanceListFormatter'
import { useTranslation } from 'react-i18next'
import { useRecoilValue, useRecoilState } from 'recoil'
import { rankMaintenanceListFilterState, rankMaintenanceListState } from '@store/RankMaintenance'
import MonitoringKeywordModal from './MonitoringKeywordModal'
import { KeywordRankMonitoringModalParams } from '@models/rankMaintenance/KeywordRankMonitoring'
import { ReactComponent as RankIcon } from '@components/assets/images/icon_rank.svg'
import AddIcon from '@material-ui/icons/Add'
import { useAuthority } from '@hooks/common'
import './RankMaintenanceList.scss'
import TableHeaderRowCount from '@components/common/table/TableHeaderRowCount'
import tw from 'twin.macro'
import { LiteBadge, ProBadge } from '@components/common/BaseChip'
import { TransText } from '@components/common'

interface Props {
  contextMenuFunctions: RankMaintenanceContextMenuFunctions
  onClickNew: () => void
  setOpenKeywordMonitoringModal: (openKeywordMonitoringModal: boolean) => void
  setKeywordMonitoringParams: (keywordMonitoringParams: KeywordRankMonitoringModalParams) => void
}

const RankMaintenanceList: React.FC<Props> = ({ contextMenuFunctions, onClickNew }: Props): ReactElement => {
  const { t } = useTranslation()
  const rmList = useRecoilValue(rankMaintenanceListState)
  const { hasAuthority } = useAuthority()

  const [rmListActiveFilter, setRmListActiveFilter] = useRecoilState(rankMaintenanceListFilterState)

  const [openMonitoringKeyword, setOpenMonitoringKeyword] = useState<boolean>(false)
  const rankMaintenanceListFormatter = new RankMaintenanceListFormatter()

  const handleChangePage = (newPage: number) => {
    if (newPage !== rmListActiveFilter.pageIndex) {
      setRmListActiveFilter({ ...rmListActiveFilter, pageIndex: newPage })
    }
  }

  const handleChangeRowsPerPage = (newRowsPerPage: number) => {
    if (newRowsPerPage !== rmListActiveFilter.pageSize) {
      setRmListActiveFilter({ ...rmListActiveFilter, pageSize: newRowsPerPage, pageIndex: 1 })
    }
  }

  const handleOrderChange = (orderBy: number, orderDirection: 'asc' | 'desc') => {
    setRmListActiveFilter({
      ...rmListActiveFilter,
      orderBy: tableColumns[orderBy]?.field,
      sorting: orderDirection.toUpperCase(),
      pageIndex: 1
    })
  }

  const tableColumns: Array<FixedLayoutColumn<RankMaintenanceInfo>> = rankMaintenanceListFormatter.getColumnFormat(
    contextMenuFunctions,
    rmListActiveFilter.orderBy,
    rmListActiveFilter.sorting
  )

  const toggleMonitoringKeyword = (open: boolean) => {
    setOpenMonitoringKeyword(open)
  }

  return (
    <div id="RankMaintenanceList">
      <Box className="listHeaderWrapper">
        <ButtonGroup>
          {hasAuthority && (
            <Button
              id="createButton"
              data-testid="createButton"
              variant="contained"
              endIcon={<AddIcon />}
              onClick={() => onClickNew()}
            >
              {t('rankMaintenance.label.RankMaintenanceListPage.list.button.new')}
            </Button>
          )}
          <Button
            id="keywordAllButton"
            endIcon={<RankIcon />}
            variant="contained"
            onClick={() => toggleMonitoringKeyword(true)}
          >
            {t('rankMaintenance.label.RankMaintenanceListPage.list.button.rankStatus')}
          </Button> 
          <TableHeaderRowCount
            current={rmList.currentCount}
            total={rmList.maxCount}
            text={t('rankMaintenance.label.RankMaintenanceListPage.totalCount')}
          >
            <>
              <TransText
                as="h1"
                i18nKey={'rankMaintenance.message.RankMaintenanceListPage.tooltip.title'}
              />
              <div className="common-style">
                <TransText
                  as="p"
                  i18nKey={'rankMaintenance.message.RankMaintenanceListPage.tooltip.contents.0'}
                />
                <div className="!mt-2.5 !py-2.5 align-middle leading-relaxed bg-[#F2F3F6] rounded-sm">
                  <div className="flex flex-col justify-center items-center">
                    <div className="flex justify-start items-center">
                      <ProBadge disabled={false} className="mb-[-3px]" />
                      <TransText
                        as="p"
                        i18nKey={'rankMaintenance.message.RankMaintenanceListPage.tooltip.contents.1'}
                      />
                    </div>
                  </div>
                  <div className="flex justify-center items-center">
                    <div className="flex justify-start items-center">
                      <LiteBadge disabled={false} className="mb-[-3px]" />
                        <TransText
                        as="p"
                        i18nKey={'rankMaintenance.message.RankMaintenanceListPage.tooltip.contents.2'}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </>
          </TableHeaderRowCount>
        </ButtonGroup>
      </Box>
      <>
        <FixedLayoutTable
          data-testid="rankMaintenanceTable"
          columns={tableColumns}
          onOrderChange={handleOrderChange}
          data={rmList?.keywords?.map((obj) => Object.create(obj) || [])}
          localization={{ body: { emptyDataSourceMessage: t('common.message.list.noData') } }}
        />
        <TablePagination
          id="rank-maintenance-list-pagination"
          totalCount={rmList.totalCount || 0}
          page={rmList.pageIndex || 1}
          rowsPerPage={rmList.pageSize || 10}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          pageOptions={[10, 20, 50]}
        />
        {openMonitoringKeyword && (
          <MonitoringKeywordModal
            onClose={() => toggleMonitoringKeyword(false)}
            onClickKeyword={(onClickParam: KeywordRankMonitoringModalParams) =>
              contextMenuFunctions.requestMonitoringResult?.(onClickParam)
            }
          />
        )}
      </>
    </div>
  )
}
export default RankMaintenanceList
