/* istanbul ignore file */
export const validatePassword = (password: string): boolean => {
  const regExp = /^(?=.*[0-9])(?=.*[^a-zA-Z0-9])(?=.*[a-z])(?!.*\s).{8,20}$/

  return regExp.test(password)
}

export const validateEmail = (email: string): boolean => {
  const regExp = /^[0-9a-zA-Z+_.-]+@[0-9a-zA-Z-]+\.[a-zA-Z0-9-.]+$/i

  return regExp.test(email)
}

export const validateUrl = (url: string): boolean => {
  const regExp = /^[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/

  return regExp.test(url)
}

export const validateGa4Event = (eventName: string): boolean => {
  const regExp = /^[a-zA-Z0-9_]+$/

  return regExp.test(eventName)
}

export const validateCampaignName = (eventName: string): boolean => {
  const allowedCharPattern = /^[a-zA-Z0-9\uAC00-\uD7AF\u1100-\u11FF\u3130-\u318F\s]*$/

  return allowedCharPattern.test(eventName)
}
