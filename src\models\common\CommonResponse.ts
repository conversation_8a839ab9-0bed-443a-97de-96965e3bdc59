import { YNFlag } from './YNFlag'

export default interface CommonResponse<T = any> {
  successOrNot: YNFlag | string
  statusCode: StatusCode | string
  data?: T
}

export type PaginationResponse<T, K extends string = 'data'> = {
  totalCount: number
  pageSize?: number
  pageIndex?: number
  currentCount?: number
} & Record<K, T>

export enum StatusCode {
  SESSION_EXPIRE = 'SESSION_EXPIRE',
  BAD_REQUEST_ERROR = 'BAD_REQUEST_ERROR',
  PARAMETER_VALUE_ERROR = 'PARAMETER_VALUE_ERROR',
  MANDATORY_PARAM_ERROR = 'MANDATORY_PARAM_ERROR',

  SUCCESS = 'SUCCESS',
  FAIL = 'FAIL',
  EXISTS_USER = 'EXISTS_USER',

  UNKNOWN_ERROR = 'UNKNOWN_ERROR',

  DUPLICATE_NAME = 'DUPLICATE_NAME',
  DUPLICATE_ADGROUP = 'DUPLICATE_ADGROUP',
  MESSAGE_EXCEED_MAX_ADGROUPS_CONFIG = 'MESSAGE_EXCEED_MAX_ADGROUPS_CONFIG',
  DUPLICATE_KEYWORD_CONFIG = 'DUPLICATE_KEYWORD_CONFIG',
  DUPLICATE_AD_CONFIG = 'DUPLICATE_AD_CONFIG',
  EXCEED_MAX_KEYWORD_CONFIG = 'EXCEED_MAX_KEYWORD_CONFIG',

  EXCEED_MAX_RAWDATA = 'EXCEED_MAX_RAWDATA',
  EMPTY_RAWDATA = 'EMPTY_RAWDATA',
  DUPLICATE_ACCOUNT = 'DUPLICATE_ACCOUNT',
  INVALID_API_KEY = 'INVALID_API_KEY',
  NAVER_COMMERCE_INTEGRATION_FAIL = 'NAVER_COMMERCE_INTEGRATION_FAIL',
  DUPLICATE_EMAIL = 'DUPLICATE_EMAIL',
  DUPLICATE_ADMIN = 'DUPLICATE_ADMIN',

  WRONG_EMAIL_OR_PASSWORD = 'WRONG_EMAIL_OR_PASSWORD',
  NOT_CONFIRMED_EMAIL = 'NOT_CONFIRMED_EMAIL',
  INVALID_PASSWORD = 'INVALID_PASSWORD',
  ACCOUNT_LOCK = 'LOCK',
  ACCOUNT_DORMANT = 'DORMANT',
  PW_EXPIRED = 'PW_EXPIRED',
  ASIS_USER = 'ASIS_USER', // before migration
  ACCOUNT_EXPIRE_PASSWORD = 'EXPIRE_PASSWORD',
  ALREADY_SENT_EMAIL = 'ALREADY_SENT_EMAIL',
  WAIT = 'WAIT',
  NORMAL = 'NORMAL',
  INITIAL = 'INITIAL',
  NOT_EXISTS_USER = 'NOT_EXISTS_USER',

  INVALID_TOKEN = 'INVALID_TOKEN',
  ALREADY_USED_TOKEN = 'ALREADY_USED_TOKEN',
  EXPIRE_TOKEN = 'EXPIRE_TOKEN',

  OVER_MONITORING_MAX_COUNT = 'OVER_MONITORING_MAX_COUNT',
  OVER_BID_MAX_COUNT = 'OVER_BID_MAX_COUNT'
}

export function isSuccessResponse<T>(
  response: CommonResponse<T>
): response is { successOrNot: YNFlag.Y; statusCode: StatusCode; data: T } {
  const isErrorStatus = response.statusCode.toString().includes('ERROR')
  return response.successOrNot === YNFlag.Y && !isErrorStatus
}
