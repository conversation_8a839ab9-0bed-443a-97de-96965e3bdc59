import { MopMedia } from '@models/common';
import { PlatformType } from '@models/common/Platform';

export enum MediaType {
  NAVER = 'NAVER',
  KAKAO = 'KAKAO',
  GOOGLE = 'GOOGLE',
  META = 'META',
  CRITEO = 'CRITEO'
}

export enum MediaTypeWithETC {
  NAVER = 'NAVER',
  KAKAO = 'KAKAO',
  GOOGLE = 'GOOGLE',
  META = 'META',
  CRITEO = 'CRITEO',
  ETC = 'ETC'
}

export interface Media {
  mediaType: MediaType;
  mediaName: string;
}

export enum CommerceMediaType {
  NAVER_COMMERCE = 'NAVER-COMMERCE'
}

export type MediaResponse = Record<PlatformType, MediaType[]>

export const COMMERCE_PATH_CONNECTION:{ [Key in  MopMedia]?: CommerceMediaType } = {
  [MediaType.NAVER]: CommerceMediaType.NAVER_COMMERCE
}