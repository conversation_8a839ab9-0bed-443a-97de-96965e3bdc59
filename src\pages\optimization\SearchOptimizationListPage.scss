#SearchOptimizationListPage {
  .MuiGrid-container {
    height: 100%;
    margin: 0;
  }

  .listHeaderWrapper {
    position: relative;

    .listLeftWrapper {
      position: absolute;
      bottom: 15px;
      left: 32px;
      display:flex;
      gap: 8px;
    }
  }

  #createButton {
    border-radius: 0;
    height: 30px;
    font-size: 14px;
    font-weight: 400;
    color: var(--color-black);
    text-align: left;
    border: 1px solid #bfbfbf;

    &::after {
      content: '';
      width: 15px;
      height: 16px;
      position: absolute;
      top: 50%;
      right: 12px;
      transform: translateY(-50%);
    }
    &.new::before {
      content: 'NEW';
      display: flex;
      align-items: center;
      justify-content: center;
      height: 16px;
      width: 38px;
      font-size: 8px;
      font-weight: 700;
      color: white;
      background-color: var(--mop20-active-blue);
      border-radius: 8px;
      padding: 0 10px 1px 10px;
      margin-left: -4px;
      margin-right: 4px;
    }
    &:disabled {
      color: #a0a0a0;
      border: 1px solid #cccccc;
    }
  }

  .MuiButton-containedPrimary {
    background-color: #fff;
  }

  .MuiButton-contained {
    box-shadow: none;
  }
}
