import { MediaType } from '@models/common/Media';
import { OptimizationKpi } from '@models/optimization/Kpi';
import { ShoppingAd } from '@models/common/Keyword';

export interface ShoppingOptimizationDetail {
  optimizationId?: number
  optimizationName: string
  mediaType: MediaType
  saShoppingType: string
  bidStartDate: string
  bidEndDate: string
  dailyBudget: number
  optimizationGoal: string
  kpis?: OptimizationKpi[]
  adgroupIds: ShoppingOptimizationAdgroup[]
  excludeAds?: ShoppingAd[]
  bidLimitDate?: string
  topRankImpressionBoosted: boolean
  addTopCpcYn: string
  boostingRateLevel: number
  minImps: number
  exclusiveMaxCpcYn: string | null
  exclusiveSprintYn: string | null
  exclusiveTurboYn: string | null
  exclusiveClusteringYn: string | null
  exclusiveCpcReboot: string | null
  cpcReboot: boolean
}

export interface ShoppingOptimizationList {
  totalCount: number;
  pageSize: number;
  pageIndex: number;
  currentAdgroupsCount: number;
  maxAdgroupsCount: number;
  currentItemsCount: number;
  maxItemsCount: number;
  optimizations: ShoppingOptimizationInfo[];
}

export interface ShoppingOptimizationInfo {
  optimizationId: number
  optimizationName: string
  mediaType: string
  saShoppingType: string
  bidStartDate: string
  bidEndDate: string
  dailyBudget: number
  optimizationGoal: string
  bidYn: string
  status: string
  errorStatus: string
  createdDateTime: string
  topRankImpressionBoosted: boolean,
  adgroupsCount: number
}

export interface ShoppingOptimizationsRequest {
  advertiserId: number;
  pageSize: number;
  pageIndex: number;
  mediaType?: string;
  saShoppingType?: string;
  status?: string;
  optimizationName?: string;
  optimizationGoal?: string;
  bidYn?: string;
  orderBy?: string;
  sorting?: string;
}

export enum ShoppingOptimizationListColumn {
  BID_YN = 'BID_YN',
  STATUS = 'STATUS',
  OPTIMIZATION_RESULT = 'OPTIMIZATION_RESULT',
  MEDIA_TYPE = 'MEDIA_TYPE',
  AD_TYPE = 'AD_TYPE',
  OPTIMIZATION_NAME = 'OPTIMIZATION_NAME',
  ADGROUPS_COUNT = 'ADGROUPS_COUNT',
  OPTIMIZATION_ID = 'OPTIMIZATION_ID',
  BID_START_DATE = 'BID_START_DATE',
  BID_END_DATE = 'BID_END_DATE',
  DAILY_BUDGET = 'DAILY_BUDGET',
  OPTIMIZATION_GOAL = 'OPTIMIZATION_GOAL',
  NEGATIVE_KEYWORD = 'NEGATIVE_KEYWORD',
  CREATED_DATETIME = 'CREATED_DATETIME',
  ERROR_STATUS = 'ERROR_STATUS',
  CONTEXT_MENU = 'CONTEXT_MENU',
}

export interface ContextMenuFunctions {
  requestEdit?: (optimizationId: number) => void;
  requestDelete?: (optimizationId: number) => void;
  requestBidOnOff?: (optimizationId: number, onOff: boolean) => void;
  requestRead?: (optimizationId: number) => void;
  requestNegativeKeyword?: (optimizationId: number) => void;
  requestDuplicate?: (optimizationId: number) => void;
}

export interface ShoppingOptimizationSaveRequest {
  optimizationName: string
  advertiserId: number
  mediaType: MediaType
  saShoppingType: string
  bidStartDate: string
  bidEndDate: string
  dailyBudget: number
  optimizationGoal: string
  kpis?: OptimizationKpi[]
  adgroupIds: ShoppingOptimizationAdgroup[]
  campaignIds: string[]
  excludeAdIds?: string[]
  topRankImpressionBoosted: boolean
  addTopCpcYn: string
  boostingRateLevel: number
  minImps: number
  cpcReboot: boolean | undefined
}

export interface ShoppingOptimizationSaveRequestWithoutAdgroup {
  optimizationName: string
  advertiserId: number
  mediaType: MediaType
  saShoppingType: string
  bidStartDate: string
  bidEndDate: string
  dailyBudget: number
  optimizationGoal: string
  kpis?: OptimizationKpi[]
  excludeAdIds?: string[]
  topRankImpressionBoosted: boolean
  addTopCpcYn: string
  boostingRateLevel: number
  minImps: number
}

export interface ShoppingOptimizationAdgroup {
  accountId?: string;
  adgroupId: string;
  campaignId?: string;
}

export interface ShoppingOptimizationCostsRequest {
  mediaType: string;
  advertiserId: number;
  adgroupIds: string[];
  excludeAdIds: string[];
}

export interface ShoppingOptimizationCost {
  days: number;
  cost: number;
}

export enum SaShoppingType {
  SHOPPING = 'SHOPPING',
  CATALOG = 'CATALOG',
}

export interface SaShoppingStatus {
  statusType: SaShoppingType;
  statusName: string;
}