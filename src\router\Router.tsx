import React, { Fragment, ReactElement } from 'react'
import RouteChangeTracker from '@router/RouteChangeTracker'
import { Navigate, Route, Routes } from 'react-router-dom'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, MopEmptyAdvertiser } from '@components/layout'
import SearchOptimizationListPage from '@pages/optimization/SearchOptimizationListPage'
import RankMaintenanceListPage from '@pages/rankMaintenance/RankMaintenanceListPage'
import ShoppingBiddingListPage from '@pages/manualBidding/ShoppingBiddingListPage'
import ContributionAnalysisPage from '@pages/contributionAnalysis/ContributionAnalysisPage'
import CompetitionAnalysisPage from '@pages/competitionAnalysis/CompetitionAnalysisPage'
import OptimizationInsight from '@pages/optimizationInsight/OptimizationInsight'
import BudgetAnalysisPage from '@pages/budgetAnalysis/BudgetAnalysisPage'
import DvOptimizationListPage from '@pages/budgetOptimization/DvOptimizationListPage'
import { CampaignReportPage, CampaignReportRawDataAddPage, ReportRawDataPage } from '@pages/report'
import ShoppingOptimizationListPage from '@pages/shoppingOptimization/ShoppingOptimizationListPage'
import DashboardPage from '@pages/dashboard/DashboardPage'
import { DetectionUTM, DetectionURL, DetectionSPA } from '@pages/anomalyDetection'
import { BudgetOptListPage, BudgetOptDetailPage } from '@pages/budgetOpt'
import AdEfficiencyListPage from '@pages/adEfficiency/AdEfficiencyListPage'
import AdEfficiencyDetailPage from '@pages/adEfficiency/AdEfficiencyDetailPage'
import { PageErrorCode } from '@models/common/PageErrorCode'

import { useAuthority, useLayout } from '@hooks/common'

import SessionUtil from '@utils/SessionUtil'
import CampaignReportRawDataDetailPage from '@pages/report/CampaignReportRawDataDetailPage'
import { ProfileLayout, UpdateProfile, EmailNotification, DeleteAccount, UpdatePassword } from '@pages/profile'

import ScrollToTop from '@components/common/ScrollToTop'
import { cn } from '@utils/index'
import CreationCampaignListPage from '@pages/createCampaign/CreationCampaignListPage'
import CreationCampaignPage from '@pages/createCampaign/CreationCampaignPage'
import { ProtectedRoute } from '@components/common'
import { MenuType } from '@models/common/Menu'
import NegativeKeywordReportPage from '@pages/report/NegativeKeywordReportPage'

const Router: React.FC = (): ReactElement => {
  const sessionUtil = new SessionUtil()
  const email = sessionUtil.getSessionInfo().email
  const isTester = email?.endsWith('@mop.co.kr')
  const { advertiser } = useAuthority()
  const { hideSidebar } = useLayout()

  return (
    <div className="App">
      <MopHeader />
      <main className={cn('grid bg-white w-full h-1 min-h-[calc(100%-110px)]', !hideSidebar && 'grid-cols-[auto,1fr]')}>
        <MopNav />
        <ScrollToTop />
        <section className="w-full h-full overflow-x-auto">
          <Fragment key={advertiser?.advertiserId}>
            <RouteChangeTracker />
            <Routes>
              <Route path="/" element={<Navigate to="/dashboard" />} />
              <Route path="/dashboard" element={<DashboardPage />} />
              <Route path="/profile" element={<ProfileLayout />}>
                <Route index element={<UpdateProfile />} />
                <Route path="update-password" element={<UpdatePassword />} />
                <Route path="email-notification" element={<EmailNotification />} />
                <Route path="delete-account" element={<DeleteAccount />} />
              </Route>

              {advertiser.advertiserId ? (
                <>
                  <Route path="/report/anomaly-detection-url" element={<DetectionURL />} />
                  <Route path="/report/anomaly-detection-utm" element={<DetectionUTM />} />
                  <Route path="/report/anomaly-detection-spa" element={<DetectionSPA />} />
                  <Route path="/optimization" element={<SearchOptimizationListPage />} />
                  <Route path="/budget-optimization" element={<DvOptimizationListPage />} />
                  <Route path="/shopping-optimization" element={<ShoppingOptimizationListPage />} />
                  <Route path="/rank-maintenance" element={<RankMaintenanceListPage />} />
                  <Route path="/target-bidding" element={<ShoppingBiddingListPage />} />
                  <Route  path="/campaign" element={<CreationCampaignListPage />} />
                  <Route 
                    path="/campaign/create" 
                    element={
                      <ProtectedRoute requiredMenuType={MenuType.CREATE_CAMPAIGN} requirePro>
                        <CreationCampaignPage />
                      </ProtectedRoute>
                    } 
                  />
                  <Route path="/spend-pacing" element={<OptimizationInsight />} />
                  <Route path="/attribution" element={<ContributionAnalysisPage />} />
                  <Route path="/competition/:platformType" element={<CompetitionAnalysisPage />} />
                  <Route path="/budget/:platformType" element={<BudgetAnalysisPage />} />
                  <Route path="/budget-opt" element={<BudgetOptListPage />} />
                  <Route path="/budget-opt/:optId" element={<BudgetOptDetailPage />} />
                  <Route path="/ad-efficiency" element={<AdEfficiencyListPage />} />
                  <Route path="/ad-efficiency/:analysisId" element={<AdEfficiencyDetailPage />} />
                  <Route path="/report/campaign" element={<CampaignReportPage />} />
                  <Route path="/negative-keyword" element={<NegativeKeywordReportPage />} />
                  <Route path="/report/optimization" element={<CampaignReportPage />} />
                  <Route path="/unauthorized" element={<Navigate to="/error" state={PageErrorCode.UNAUTHORIZED} />} />
                  <Route path="/*" element={<Navigate to="/error" state={PageErrorCode.NOT_FOUND} />} />
                  <Route path="/report/raw-data" element={<ReportRawDataPage />} />
                  <Route path="/report/raw-data/add" element={<CampaignReportRawDataAddPage />} />
                  <Route path="/report/raw-data/:reportId" element={<CampaignReportRawDataDetailPage />} />
                </>
              ) : (
                <Route path="/*" element={<MopEmptyAdvertiser />} />
              )}
            </Routes>
          </Fragment>
        </section>
      </main>
    </div>
  )
}

export default Router
