import React, { useState } from 'react'
import { pageSizeOptions } from '@models/common/CommonConstants'
import { useAuthority } from '@hooks/common'
import useDebounce from '@hooks/common/useDebounce'
import CreateCampaignHeader from '@components/createCampaign/CreateCampaignHeader'
import CreateCampaignList from '@components/createCampaign/CreateCampaignList'
import CampaignCreationListFilter from '@components/createCampaign/CampaignCreationListFilter'
import { useQuery } from '@tanstack/react-query'
import { getCampaigns } from '@api/creationCampaign'
import { AD_REVIEW_STATUS, CAMPAIGN_CREATION_STATUS } from '@models/createCampaign/CreateCampaign'

const CreationCampaignListPage: React.FC = () => {
  const { advertiser } = useAuthority()

  const [searchKeyword, setSearchKeyword] = useState('')
  const [page, setPage] = useState(1)
  const [rowsPerPage, setRowsPerPage] = useState(pageSizeOptions[0])
  const [filter, setFilter] = useState({
    status: 'ALL',
    adReview: 'ALL'
  })
  const { debounceValue: debouncedSearchKeyword } = useDebounce(searchKeyword, 500)

  const { data: campaignList, isLoading } = useQuery({
    queryKey: ['campaignList', advertiser.advertiserId, debouncedSearchKeyword, page, rowsPerPage, filter],
    queryFn: () =>
      getCampaigns(advertiser.advertiserId, {
        campaignName: debouncedSearchKeyword,
        pageIndex: page,
        pageSize: rowsPerPage,
        creationStatus: filter.status as CAMPAIGN_CREATION_STATUS,
        adReviewStatus: filter.adReview as AD_REVIEW_STATUS
      })
  })

  const handleSearchChange = (value: string) => {
    setSearchKeyword(value)
    setPage(1)
  }

  const handleFilterChange = (newFilter: { status: string; adReview: string }) => {
    setFilter(newFilter)
    setPage(1)
  }

  const handlePageChange = (newPage: number) => {
    setPage(newPage)
  }

  const handleRowsPerPageChange = (newRowsPerPage: number) => {
    setRowsPerPage(newRowsPerPage)
    setPage(1)
  }

  return (
    <div className="create-campaign-page bg-white min-h-screen" data-testid="createCampaignPage">
      <CampaignCreationListFilter onFilterChange={handleFilterChange} />
      <CreateCampaignHeader searchKeyword={searchKeyword} onSearchChange={handleSearchChange} />
      <CreateCampaignList
        campaigns={campaignList?.configurations || []}
        totalCount={campaignList?.totalCount || 0}
        page={page}
        rowsPerPage={rowsPerPage}
        onPageChange={handlePageChange}
        isLoading={isLoading}
        onRowsPerPageChange={handleRowsPerPageChange}
      />
    </div>
  )
}

export default CreationCampaignListPage
