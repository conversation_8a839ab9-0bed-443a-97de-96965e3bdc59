import './MediaIcon.scss';
import { AnalyticsType, MediaAnalyticsType, MediaType, CommerceMediaType } from '@models/common'
import { ReactComponent as IconNaver } from '@components/assets/images/logo_naver_b.svg';
import { ReactComponent as IconKakao } from '@components/assets/images/logo_kakao_b.svg';
import { ReactComponent as IconGoogle } from '@components/assets/images/logo_google_b.svg';
import { ReactComponent as IconMeta } from '@components/assets/images/logo_meta_b.svg';
import { ReactComponent as IconGA } from '@components/assets/images/logo_ga_b.svg';
import { ReactComponent as IconGA4 } from '@components/assets/images/logo_ga4_b.svg';
import { ReactComponent as IconAppsFlyer } from '@components/assets/images/logo_appsflyer_b.svg';
import { ReactComponent as IconAirbrdige } from '@components/assets/images/logo_airbridge_b.svg';
import { ReactComponent as IconCriteo } from '@components/assets/images/logo_criteo_b.svg';
import { ReactComponent as IconAPICenter } from '@assets/images/logo_api_center.svg';

interface Props {
  mediaType: string;
  size?: number;
  filled?: boolean;

}
const MediaIconSVG = ({ mediaType }: Props) => {
  switch (mediaType) {
    case AnalyticsType.AIRBRIDGE: return <IconAirbrdige />
    case AnalyticsType.APPSFLYER: return <IconAppsFlyer />
    case AnalyticsType.GA: return <IconGA />
    case AnalyticsType.GA4: return <IconGA4 />
    case MediaAnalyticsType.APICENTER: return <IconAPICenter />
    case MediaType.CRITEO: return <IconCriteo />
    case MediaType.GOOGLE: return <IconGoogle />
    case MediaType.KAKAO: return <IconKakao />
    case MediaType.META: return <IconMeta />
    case MediaType.NAVER:
    case CommerceMediaType.NAVER_COMMERCE:
    default: return <IconNaver />
  }
}

// --color
// --bg-color
// --contained
// --rouned

const MediaIcon = ({ mediaType, size = 32, filled = true }: Props) => {
  const styles = {'--size': `${size}px`, '--filled': filled ? '#171717' : 'transparent'} as React.CSSProperties;
  return (
    <div className="media-icon-box" style={styles}>
      <MediaIconSVG mediaType={mediaType} />
    </div>
  )
}

export default MediaIcon