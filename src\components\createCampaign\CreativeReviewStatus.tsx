import React from 'react'
import { Tooltip } from '@material-ui/core'
import { makeStyles } from '@material-ui/core/styles'
import { useTranslation } from 'react-i18next'
import './CreativeReviewStatus.scss'
import { AD_REVIEW_STATUS } from '@models/createCampaign/CreateCampaign'

// Create custom styles for the tooltip
const useStyles = makeStyles((theme) => ({
  tooltip: {
    maxWidth: '500px'
  }
}))

interface CreativeReviewStatusProps {
  status: AD_REVIEW_STATUS
  eligibleCount?: number
  approvedCount?: number
  underReviewCount?: number
  pendingCount?: number
  deniedCount?: number
}

const CreativeReviewStatus: React.FC<CreativeReviewStatusProps> = ({
  status,
  eligibleCount = 0,
  approvedCount = 0,
  underReviewCount = 0,
  pendingCount = 0,
  deniedCount = 0
}) => {
  const classes = useStyles()
  const { t } = useTranslation()

  const getStatusColor = (status: string) => {
    switch (status) {
      case AD_REVIEW_STATUS.APPROVED:
      case AD_REVIEW_STATUS.ELIGIBLE:
        return 'green'
      case AD_REVIEW_STATUS.UNDER_REVIEW:
        return 'blue'
      case AD_REVIEW_STATUS.PENDING:
        return 'gray'
      case AD_REVIEW_STATUS.DENIED:
        return 'red'
      default:
        return 'gray'
    }
  }

  const getTooltipText = () => {
    return (
      <div className="text-center">
        {t('createCampaign.label.table.creativeReview')} <br /> 
        {t('createCampaign.label.adReview.fullyApproved')} {approvedCount} / {t('createCampaign.label.adReview.eligible')} {eligibleCount} / {t('createCampaign.label.adReview.inProgress')} {underReviewCount} / {t('createCampaign.label.adReview.pending')} {pendingCount} / {t('createCampaign.label.adReview.fullyRejected')} {deniedCount}
      </div>
    )
  }

  return (
    <Tooltip
      title={getTooltipText()}
      arrow
      placement="top"
      classes={{
        tooltip: classes.tooltip
      }}
    >
      <div className="creative-review-status">
        <div className={`status-dot ${getStatusColor(status)}`} />
      </div>
    </Tooltip>
  )
}

export default CreativeReviewStatus
