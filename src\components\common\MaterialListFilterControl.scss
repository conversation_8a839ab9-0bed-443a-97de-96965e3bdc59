.material-list-filter-control {
  background: white;
  border-radius: 8px;

  .filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .header-left {
      display: flex;
      align-items: center;
      gap: 16px;

      .filter-title {
        font-size: 20px;
        font-weight: bold;
        color: #333333;
        margin: 0;
      }

      .recommendation-controls {
        display: flex;
        align-items: center;
        gap: 12px;

        .recommendation-toggle {
          display: flex;
          border: 1px solid #efefef;
          border-radius: 50px;
          background: #f9f9fb;
          overflow: hidden;
          position: relative;
          padding: 5px;

          .toggle-option {
            padding: 8px 20px;
            border: none;
            background: transparent;
            font-size: 12px;
            font-weight: 600;
            color: #969696;
            cursor: pointer;
            position: relative;
            border-radius: 50px;
            z-index: 1;

            &.active-aggressive {
              color: #eb424c;
              background: #ffe1e3;
              font-weight: 700;
            }
            &.active-conservative {
              color: #5e81f4;
              background: #d5dfff;
              font-weight: 700;
            }

            &:hover:not(.active) {
              background: #f5f5f5;
            }
          }
        }

        .select-all-button {
          font-size: 12px;
          padding: 10px 16px;
          border-radius: 20px;
          background-color:#F9F9FB ;
          color: #333333;
          border: 1px solid #efefef;
          &:hover {
            opacity: 0.8;
          }
        }
      }
    }

    .header-right {
      display: flex;
      align-items: center;
      gap: 12px;

      .analysis-date {
        font-size: 14px;
        color: #666666;
      }

      .download-button {
        padding: 8px;
        border: none;
        border-radius: 4px;
        background: white;

        &:hover {
          background: #f5f5f5;
        }
      }
    }
  }

  .filter-section {
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 16px;

    .filter-row {
      display: flex;
      gap: 16px;
      align-items: end;

      .filter-item {
        display: flex;
        flex-direction: column;
        gap: 8px;
        flex: 1;

        .filter-label {
          font-size: 14px;
          font-weight: 500;
          color: #333333;
        }
      
          .mop-select__button {
            height: 42px;
            border: 1px solid #efefef;
            border-radius: 4px;
            padding: 0 16px;
            font-size: 14px;
            background: white;

            &:focus {
              outline: none;
              border-color: #1976d2;
            }
          }
        }
      }
    }
  }

