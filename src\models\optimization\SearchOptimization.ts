import { MediaType } from '@models/common/Media';
import { OptimizationKpi } from '@models/optimization/Kpi';
import { Keyword } from '@models/common/Keyword';

export interface SearchOptimizationDetail {
  optimizationId?: number
  optimizationName: string
  advertiserId: number
  mediaType: MediaType
  bidStartDate: string
  bidEndDate: string
  dailyBudget: number
  optimizationGoal: string
  kpis?: OptimizationKpi[]
  adgroupIds: SearchOptimizationAdgroup[]
  excludeKeywords?: Keyword[]
  bidLimitDate?: string
  topRankImpressionBoosted: boolean
  addTopCpcYn: string
  boostingRateLevel: number
  exclusiveMaxCpcYn: string | null
  exclusiveSprintYn: string | null
  exclusiveTurboYn: string | null
  exclusiveClusteringYn: string | null
  exclusiveCpcReboot: string | null
  cpcReboot: boolean
}

export interface SearchOptimizationList {
  totalCount: number;
  pageSize: number;
  pageIndex: number;
  currentAdgroupsCount: number;
  maxAdgroupsCount: number;
  currentItemsCount: number;
  maxItemsCount: number;
  optimizations: SearchOptimizationInfo[];
}

export interface SearchOptimizationInfo {
  optimizationId: number;
  optimizationName: string;
  mediaType: string;
  bidStartDate: string;
  bidEndDate: string;
  dailyBudget: number;
  optimizationGoal: string;
  bidYn: string;
  status: string;
  errorStatus: string;
  createdDateTime: string;
  adgroupsCount: number;
}

export interface SearchOptimizationsRequest {
  advertiserId: number;
  mediaType?: string;
  status?: string;
  optimizationName?: string;
  optimizationGoal?: string;
  pageSize: number;
  pageIndex: number;
  bidYn?: string;
  orderBy?: string;
  sorting?: string;
}

export enum SearchOptimizationListColumn {
  BID_YN = 'BID_YN',
  STATUS = 'STATUS',
  MEDIA_TYPE = 'MEDIA_TYPE',
  OPTIMIZATION_NAME = 'OPTIMIZATION_NAME',
  OPTIMIZATION_ID = 'OPTIMIZATION_ID',
  BID_START_DATE = 'BID_START_DATE',
  BID_END_DATE = 'BID_END_DATE',
  DAILY_BUDGET = 'DAILY_BUDGET',
  OPTIMIZATION_GOAL = 'OPTIMIZATION_GOAL',
  CREATED_DATETIME = 'CREATED_DATETIME',
  ERROR_STATUS = 'ERROR_STATUS',
  CONTEXT_MENU = 'CONTEXT_MENU',
  ADGROUPS_COUNT = 'ADGROUPS_COUNT',
}

export interface ContextMenuFunctions {
  requestEdit?: (optimizationId: number) => void;
  requestDelete?: (optimizationId: number) => void;
  requestBidOnOff?: (optimizationId: number, onOff: boolean) => void;
  requestRead?: (optimizationId: number) => void;
  requestDuplicate?: (optimizationId: number) => void;
}

export enum ContextRequestType {
  MODIFY = 'MODIFY',
  READ = 'READ',
  DUPLICATE = 'DUPLICATE',
  // BID = 'BID',
  DELETE = 'DELETE',
}

export interface SearchOptimizationSaveRequest {
  optimizationName: string
  advertiserId: number
  mediaType: MediaType
  bidStartDate: string
  bidEndDate: string
  dailyBudget: number
  optimizationGoal: string
  kpis?: OptimizationKpi[]
  adgroupIds: SearchOptimizationAdgroup[]
  campaignIds: string[]
  excludeKeywordIds?: string[]
  topRankImpressionBoosted: boolean
  addTopCpcYn: string
  boostingRateLevel: number
  cpcReboot: boolean | undefined
}

export interface SearchOptimizationSaveRequestWithoutAdgroup {
  optimizationName: string
  advertiserId: number
  mediaType: MediaType
  bidStartDate: string
  bidEndDate: string
  dailyBudget: number
  optimizationGoal: string
  kpis?: OptimizationKpi[]
  excludeKeywordIds: string[]
  topRankImpressionBoosted?: boolean
  addTopCpcYn: string
  boostingRateLevel: number
}

export interface SearchOptimizationAdgroup {
  accountId?: string;
  adgroupId: string;
}

export interface downloadAdgroup {
  optimizationId: number;
  accountId: string;
  adgroupId: string;
  adgroupName: string;
  campaignId: string;
  campaignName: string;
}

export interface SearchOptimizationCostsRequest {
  mediaType: string;
  advertiserId: number;
  startDate: string;
  endDate: string;
  adgroupIds: string[];
  excludeKeywordIds: string[];
}

export interface SearchOptimizationCost {
  days: number;
  cost: number;
}


export type DatasetInjector<T, D extends DOMStringMap> = React.MouseEvent<T & {
  dataset: D;
}>;
