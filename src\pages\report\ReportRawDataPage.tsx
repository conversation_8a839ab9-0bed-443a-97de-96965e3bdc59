import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import './ReportRawDataPage.scss'

import { PageListFilterGroup, PageListFilter, PageListHeader } from '@components/common/page'
import { MenuItem } from '@material-ui/core'
import { FixedLayoutTable, TablePagination } from '@components/common/table'
import { DateFnsFormat, pageSizeOptions } from '@models/common/CommonConstants'
import ReportRawDataTableFormatter from '@components/report/table/ReportRawDataTableFormatter'
import {
  RawDataStatusEnum,
  ReportRawData,
  ReportRawDataStatus,
  ReportRawDataStatusTypes,
  ReportTypeEnum
} from '@models/report/RawData'
import TableHeaderRowCount from '@components/common/table/TableHeaderRowCount'
import {
  getRawDataReports,
  deleteRawDataReport,
  updateRawDataReportName,
  getRawDataReport,
  getRawDataReportTodayCount
} from '@api/report/RawData'
import { useNavigate } from 'react-router-dom'
import { useSetRecoilState } from 'recoil'
import { format } from 'date-fns'
import { useAuthority, useDialog, useToast } from '@hooks/common'
import { RawDataOptimizationState, RawDataState } from '@store/report/RawData'
import useCheckTodayCount from '@components/report/hook/useCheckTodayCount'

const reportRawDataTable = new ReportRawDataTableFormatter()

const ReportRawDataPage = () => {
  const navigate = useNavigate()
  const { t } = useTranslation()
  const { advertiser } = useAuthority()
  const { openToast } = useToast()
  const { openDialog, openFormDialog } = useDialog()
  const setRawData = useSetRecoilState(RawDataState)
  const setRawDataOptimization = useSetRecoilState(RawDataOptimizationState)
  const { isCreate, showToastMessge } = useCheckTodayCount()

  const [filter, setFilter] = useState({
    status: 'ALL',
    reportType: 'ALL',
    searchKeyword: ''
  })
  const [reportRawDataList, setReportRawDataList] = useState<ReportRawData[]>([])
  const [page, setPage] = useState(1)
  const [rowsPerPage, setRowsPerPage] = useState(pageSizeOptions[0])
  const [reportCountInfo, setReportCountInfo] = useState({
    totalCount: 0,
    todayCount: 0
  })
  const [sort, setSort] = useState<{ field: string; order: 'asc' | 'desc' }>({ field: 'rawReportId', order: 'desc' })
  const allColumns: any[] = reportRawDataTable.getColumnFormat()

  const typeFilterItems = [
    { name: 'CAMPAIGN', value: ReportTypeEnum.CAMPAIGN },
    { name: 'OPTIMIZATION', value: ReportTypeEnum.OPTIMIZATION }
  ]

  useEffect(() => {
    fetchData()
  }, []) // eslint-disable-line

  reportRawDataTable.handleCopy = async (advertiserId: number, reportId: number, reportType: ReportTypeEnum) => {
    if (isCreate) {
      showToastMessge()
      return
    }

    openDialog({
      message: t('common.message.copyConfirm'),
      cancelLabel: t('common.label.button.cancel'),
      actionLabel: t('common.label.button.copy'),
      onAction: async () => {
        try {
          const result: any = await getRawDataReport(advertiserId, reportId)
          if (result) {
            const reportObj = {
              conversionMetricsType: result.conversionMetricsType,
              dimensions: result.dimensions,
              metrics: result.metrics,
              startDate: format(new Date(result.startDate), DateFnsFormat.DATE),
              endDate: format(new Date(result.endDate), DateFnsFormat.DATE),
              reportName: `${result.reportName} copy`
            }
            if (reportType === ReportTypeEnum.CAMPAIGN) {
              setRawData({
                ...reportObj,
                accounts: result.accounts
              })
            } else if (reportType === ReportTypeEnum.OPTIMIZATION) {
              setRawDataOptimization({
                ...reportObj,
                optimizations: result.optimizations
              })
            }

            navigate(`/report/raw-data/add?reportType=${reportType}&action=copy`)
          }
        } catch (e) {}
      }
    })
  }

  reportRawDataTable.handleDelete = async (advertiserId: number, reportId: number) => {
    try {
      openDialog({
        title: t('common.message.title.notice'),
        message: t('common.message.deleteConfirm'),
        cancelLabel: t('common.label.button.cancel'),
        actionLabel: t('common.label.button.delete'),
        onAction: async () => {
          await deleteRawDataReport(advertiserId, reportId)
          const isLastPageIndex = page > 1 && filteredItmes.length === rowsPerPage * (page - 1) + 1
          if (isLastPageIndex) {
            setPage((prev) => prev - 1)
          }
          fetchData()
        }
      })
    } catch (e) {}
  }

  reportRawDataTable.handleReportNameEdit = async (report: ReportRawData) => {
    openFormDialog<{ inputValue: string }>({
      title: t('rawReport.modal.reportNameEditTitle'),
      inputOptions: {
        initialValue: report.reportName
      },
      onAction: async ({ inputValue: reportName }) => {
        if (!reportName.trim()) return
        const result = await updateRawDataReportName({
          advertiserId: advertiser.advertiserId,
          reportId: report.rawReportId,
          params: {
            reportName
          }
        })

        if (result.successOrNot === 'N' && result.statusCode === 'DUPLICATE_NAME') {
          return openToast(t('rawReport.toast.DUPLICATE_NAME'))
        }

        if (result.data) {
          openToast(t('rawReport.toast.EDIT_COMPLETED'))
          fetchData()
        }
      }
    })
  }

  async function fetchData() {
    try {
      const reportRawData = await getRawDataReports(advertiser.advertiserId)
      const todayCount = await getReportTodayCount()
      setReportRawDataList(reportRawData)
      setReportCountInfo({
        todayCount: todayCount || 0,
        totalCount: reportRawData.length
      })
    } catch (e) {
      alert('getCampaignReportRawData API Error')
    }
  }

  /**
   * 금일 생성 보고서 수 조회 API
   */
  const getReportTodayCount = async () => {
    try {
      const campaignReportData = await getRawDataReportTodayCount({
        advertiserId: advertiser.advertiserId,
        queryParams: {
          type: ReportTypeEnum.CAMPAIGN
        }
      })

      const optimizationReportData = await getRawDataReportTodayCount({
        advertiserId: advertiser.advertiserId,
        queryParams: {
          type: ReportTypeEnum.OPTIMIZATION
        }
      })

      if (campaignReportData || optimizationReportData) {
        return campaignReportData.count + optimizationReportData.count
      }
    } catch (e) {
      alert('getCampaignReportRawData API Error')
    }
  }

  const changeFilter = (e: any) => {
    setFilter({
      ...filter,
      [e.target.name]: e.target.value
    })
    setPage(1)
  }

  const openCreateModal = async () => {
    if (isCreate) {
      showToastMessge()
      return
    }

    navigate(`/report/raw-data/add?reportType=${ReportTypeEnum.CAMPAIGN}`)
  }

  const handleSearch = (searchKeyword: string) => {
    setFilter({
      ...filter,
      searchKeyword
    })
    setPage(1)
  }

  const orderChanged = (column: number, order: 'asc' | 'desc') => {
    setSort({
      field: allColumns[column].field!,
      order: order
    })
  }

  reportRawDataTable.field = sort.field
  reportRawDataTable.order = sort.order

  const customSort = (a: any, b: any) => {
    if (!sort) return 0

    if (a[sort.field] > b[sort.field]) {
      return sort.order === 'asc' ? 1 : -1
    } else if (a[sort.field] < b[sort.field]) {
      return sort.order === 'asc' ? -1 : 1
    }
    return 0
  }

  const filteredItmes = reportRawDataList
    .filter((reportRawDataItem) => {
      if (reportRawDataItem.status === 'SUBMITTED') reportRawDataItem.status = RawDataStatusEnum.CREATING
      return filter.status === 'ALL' || filter.status === reportRawDataItem.status
    })
    .filter((reportRawDataItem) => filter.reportType === 'ALL' || filter.reportType === reportRawDataItem.reportType)
    .filter((reportRawDataItem) => reportRawDataItem.reportName.includes(filter.searchKeyword))
    .sort(customSort)

  return (
    <div id="RawDataPage" className="campaign-report-raw-data-wrapping">
      <PageListFilterGroup>
        <PageListFilter
          label={t(`rawReport.table.column.status`)}
          name="status"
          value={filter.status}
          onChange={changeFilter}
        >
          {Object.keys(ReportRawDataStatus)
            .filter((statusKey: string) => statusKey !== RawDataStatusEnum.SUBMITTED)
            .map((statusKey: string) => {
              return (
                <MenuItem key={statusKey} value={statusKey}>
                  {t(
                    `rawReport.label.filter.status.${ReportRawDataStatus[statusKey as ReportRawDataStatusTypes].label}`
                  )}
                </MenuItem>
              )
            })}
        </PageListFilter>
        <PageListFilter
          label={t(`rawReport.table.column.reportType`)}
          name="reportType"
          value={filter.reportType}
          onChange={changeFilter}
        >
          {typeFilterItems.map((typeItem) => {
            return (
              <MenuItem key={typeItem.value} value={typeItem.value}>
                <span>{t(`rawReport.label.filter.reportType.${typeItem.name}`)}</span>
              </MenuItem>
            )
          })}
        </PageListFilter>
      </PageListFilterGroup>
      <PageListHeader
        handleCreate={openCreateModal}
        placeHolder={t('rawReport.label.search.placeHolder')}
        handleSearch={handleSearch}
        rowCountComponent={
          <TableHeaderRowCount current={reportCountInfo.todayCount} total={3} text={t('rawReport.label.todayCount')}>
            <>
              <h1>{'보고서 생성 횟수'}</h1>
              <div className="common-style">
                <p style={{ fontWeight: 500 }}>
                  하루 생성 가능한 대용량리포트 횟수는 3개로 제한됩니다. 추가 리포트가 필요하실 경우 다음날 다시
                  시도해주세요.
                </p>
                <p>
                  ※ 3개 제한은 생성을 시도한 횟수를 기준으로 산정합니다. 생성완료 및 삭제 여부 상관없이 횟수에
                  포함된다는 점 참고바랍니다.
                </p>
              </div>
            </>
          </TableHeaderRowCount>
        }
        visibleSearchIcon={true}
        buttonLabel={t('common.label.button.create')}
      />
      <FixedLayoutTable
        tableType="list-table"
        onOrderChange={orderChanged}
        columns={allColumns}
        data={filteredItmes.slice((page - 1) * rowsPerPage, page * rowsPerPage)}
        localization={{ body: { emptyDataSourceMessage: t('common.message.list.noData') } }}
      />
      <TablePagination
        totalCount={filteredItmes.length}
        page={page}
        rowsPerPage={rowsPerPage}
        onPageChange={(page) => setPage(page)}
        onRowsPerPageChange={(rowsPage) => setRowsPerPage(rowsPage)}
      />
    </div>
  )
}

export default ReportRawDataPage
