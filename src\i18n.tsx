import i18n from 'i18next'
import LanguageDetector from 'i18next-browser-languagedetector'
import { initReactI18next } from 'react-i18next'

// NOTE: refresh 할 경우 default lng 으로 돌아가는 현상 방지
export const getLanguageFromStorage = (): string | null => {
  return localStorage.getItem('lang') as string | null
}

const initialLanguage: string = getLanguageFromStorage() || 'ko'

const getResources = (language: string) => {
  return {
    common: require(`./locales/common/${language}.json`),
    layout: require(`./locales/layout/${language}.json`),
    login: require(`./locales/login/${language}.json`),
    updatePassword: require(`./locales/updatePassword/${language}.json`),
    report: require(`./locales/report/${language}.json`),
    optimization: require(`./locales/optimization/${language}.json`),
    rankMaintenance: require(`./locales/rankMaintenance/${language}.json`),
    competition: require(`./locales/competition/${language}.json`),
    analysis: require(`./locales/analysis/${language}.json`),
    adEfficiency: require(`./locales/adEfficiency/${language}.json`),
    circle: require(`./locales/circle/${language}.json`),
    admin: require(`./locales/admin/${language}.json`),
    setting: require(`./locales/setting/${language}.json`),
    indicator: require(`./locales/indicator/${language}.json`),
    insight: require(`./locales/insight/${language}.json`),
    dashboard: require(`./locales/dashboard/${language}.json`),
    anomalyDetection: require(`./locales/anomalyDetection/${language}.json`),
    errorPage: require(`./locales/errorPage/${language}.json`),
    oauthLink: require(`./locales/oauthLink/${language}.json`),
    utmRules: require(`./locales/utmRules/${language}.json`),
    rawReport: require(`./locales/rawReport/${language}.json`),
    accessManage: require(`./locales/accessManage/${language}.json`),
    redirect: require(`./locales/redirect/${language}.json`),
    landing: require(`./locales/intro/landing/${language}.json`),
    team: require(`./locales/intro/team/${language}.json`),
    apiCenter: require(`./locales/intro/apiCenter/${language}.json`),
    features: require(`./locales/intro/features/${language}.json`),
    partners: require(`./locales/intro/partners/${language}.json`),
    contact: require(`./locales/intro/contact/${language}.json`),
    createCampaign: require(`./locales/createCampaign/${language}.json`),
    negativeKeyword: require(`./locales/negativeKeyword/${language}.json`)
  }
}

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    detection: {
      order: ['localStorage', 'navigator'],
      caches: ['localStorage'],
      lookupQuerystring: 'locale',
      lookupLocalStorage: 'lang'
    },
    resources: {
      ko: { translation: getResources('ko') },
      en: { translation: getResources('en') }
    },
    lng: initialLanguage,
    fallbackLng: 'ko',
    debug: process.env.REACT_APP_I18N_DEBUG === 'true',
    interpolation: {
      escapeValue: false
    },
    react: {
      useSuspense: false
    }
  })

export default i18n
