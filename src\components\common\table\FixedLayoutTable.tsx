import React, { PropsWithChildren, ReactElement } from 'react';
import MaterialTable, { MaterialTableProps, Column, MTableHeader } from 'material-table';
import './FixedLayoutTable.scss';

export type FixedLayoutColumn<RowData extends object> = Column<RowData>;

interface ColGroupProps<RowData extends object> {
  columns: Column<RowData>[];
  hasLeftMostColumn: boolean;
}

const ColGroup = <RowData extends object>({ columns, hasLeftMostColumn }: PropsWithChildren<ColGroupProps<RowData>>): ReactElement => {
  const cols = hasLeftMostColumn ? [{cellStyle: {width: 50}}, ...columns] : columns;
  return (
    <colgroup>
      {cols.map((item, index) => {
        const defaultCellStyle = {
          color: 'inherit',
          width: undefined,
          maxWidth: undefined,
          minWidth: undefined,
          boxSizing: 'border-box',
          fontSize: 'inherit',
          fontFamily: 'inherit',
          fontWeight: 'inherit',
        } as React.CSSProperties;
        const cssProperties = item.cellStyle as React.CSSProperties;

        const cellStyle = {
          ...defaultCellStyle,
          ...(cssProperties || {}),
        };

        return (
          <col key={index} className={`fixedLayoutCell-col-${(item.field as string) || index}`} style={cellStyle} />
        );
      })}
    </colgroup>
  );
};

interface FixedLayoutTableProps<RowData extends object> extends MaterialTableProps<RowData> {
  id?: string;
  tableType?: string; // listTable
}

const FixedLayoutTable = <RowData extends object>(props: FixedLayoutTableProps<RowData>): ReactElement => {
  const { id, columns, data, options, localization, onOrderChange, detailPanel, onRowClick, tableType, isLoading } = props;
  const defaultOptions = {
    showTitle: false,
    search: false,
    selection: false,
    sorting: true,
    thirdSortClick: false,
    paging: false,
    draggable: false,
    tableLayout: 'fixed' as 'auto' | 'fixed',
  };

  return (
    <div id={id} className={`fixedLayout-table-root ${tableType ?? ''}`}>
      <MaterialTable
        onOrderChange={onOrderChange}
        options={{ ...defaultOptions, ...(options || {}) }}
        columns={columns}
        data={data}
        localization={localization}
        detailPanel={detailPanel}
        onRowClick={onRowClick}
        isLoading={isLoading}
        components={{
          Header: (props) => {
            return (
              <>
                <ColGroup columns={columns} hasLeftMostColumn={!!detailPanel || !!options?.selection} />
                <MTableHeader {...props} />
              </>
            );
          },
        }}
      />
    </div>
  );
};

export default FixedLayoutTable;
