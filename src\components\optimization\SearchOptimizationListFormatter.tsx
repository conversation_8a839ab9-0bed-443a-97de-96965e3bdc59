import React from 'react'
import './SearchOptimizationListFormatter.scss'
import { useTranslation } from 'react-i18next'
import {
  ContextMenuFunctions,
  ContextRequestType,
  SearchOptimizationInfo,
  SearchOptimizationList,
  SearchOptimizationListColumn,
  DatasetInjector
} from '@models/optimization/SearchOptimization'
import Tooltip from '@material-ui/core/Tooltip'
import { withStyles } from '@material-ui/core/styles'

import { StatusType } from '@models/optimization/Status'
import { getMediaCodes, getOptimizationGoalCodes } from '@utils/CodeUtil'
import { Media } from '@models/common/Media'
import { OptimizationGoal } from '@models/optimization/OptimizationGoal'
import { ErrorStatusType } from '@models/optimization/ErrorStatus'
import { ReactComponent as DeleteIcon } from '@components/assets/images/icon_delete.svg'
import { ReactComponent as EditIcon } from '@components/assets/images/icon_edit.svg'
import { ReactComponent as DuplicateIcon } from '@components/assets/images/icon_duplicate.svg'
import { ReactComponent as AdviceMarkIcon } from '@components/assets/images/advicemark.svg'
import { FixedLayoutColumn } from '@components/common/table'
import InnerHtml from '@components/common/InnerHtml'
import { compareDate, currentDate } from '@utils/DateUtil'
import { toCamelCase } from '@utils/StringUtil'
import { TransText } from '@components/common'
import { cn } from '@utils/index'
import { MopSwitch } from '@components/common/mopUI'
import { useAuthority } from '@hooks/common'
import TagManager from 'react-gtm-module'

export default class SearchOptimizationListFormatter {
  getColumnFormat = (
    contextMenuFunctions?: ContextMenuFunctions,
    orderBy?: string | undefined,
    sorting?: string | undefined,
    soList?: SearchOptimizationList
  ): Array<FixedLayoutColumn<SearchOptimizationInfo>> => {
    const { t } = useTranslation()
    const { hasAuthority } = useAuthority()

    const getMediaName = (mediaCode: string) => {
      const mediaCodes: Media[] = getMediaCodes()

      for (const item of mediaCodes) {
        if (item.mediaType === mediaCode) {
          return item.mediaName
        }
      }

      return ''
    }

    const getOptimizationGoalName = (optimizationGoalCode: string) => {
      const optimizationGoalCodes: OptimizationGoal[] = getOptimizationGoalCodes()

      for (const item of optimizationGoalCodes) {
        if (item.optimizationGoal === optimizationGoalCode) {
          return item.optimizationGoalName
        }
      }

      return ''
    }

    const handleRequestRead = (event?: any) => {
      event?.stopPropagation()
      contextMenuFunctions?.requestRead?.(Number(event.currentTarget.dataset.id))
    }

    const handleRequestEdit = (event?: any) => {
      event?.stopPropagation()
      contextMenuFunctions?.requestEdit?.(Number(event.currentTarget.dataset.id))
    }

    const handleRequestDelete = (event?: any) => {
      event && event.stopPropagation()
      contextMenuFunctions?.requestDelete && contextMenuFunctions.requestDelete(Number(event.currentTarget.dataset.id))
    }

    const handleRequest = (event: React.MouseEvent<HTMLOrSVGElement>, requestType: ContextRequestType) => {
      event.stopPropagation()
      const id = (event as DatasetInjector<HTMLOrSVGElement, { id: string }>).currentTarget.dataset.id
      const context = {
        [ContextRequestType.READ]: contextMenuFunctions?.requestRead,
        [ContextRequestType.MODIFY]: contextMenuFunctions?.requestEdit,
        [ContextRequestType.DELETE]: contextMenuFunctions?.requestDelete,
        [ContextRequestType.DUPLICATE]: contextMenuFunctions?.requestDuplicate
      }
      context[requestType] && context[requestType]?.(parseInt(id, 10))
    }

    const handleBidSwitchChange = (checked: boolean, id: number) => {
      contextMenuFunctions?.requestBidOnOff && contextMenuFunctions.requestBidOnOff(id, checked)
    }

    const AdviceTooltip = withStyles((theme) => ({
      tooltip: {
        backgroundColor: '#ffffff',
        color: '#2b2b2b',
        minWidth: '360px',
        boxShadow: theme.shadows[2],
        fontSize: 13
      },
      arrow: {
        '&:before': {
          border: '1px solid #E6E8ED'
        },
        color: '#ffffff'
      }
    }))(Tooltip)

    const getColumnTitle = (columnType: string, sorting: boolean) => {
      columnType = toCamelCase(columnType)

      if (sorting) {
        return (
          <>
            <AdviceTooltip
              id="sa-optimization-advice-tooltip"
              title={<InnerHtml innerHTML={t(`optimization.message.searchOptimization.list.${columnType}`) || ''} />}
              placement="right-start"
              arrow
            >
              <span id="adviceIcon">
                <AdviceMarkIcon />
              </span>
            </AdviceTooltip>
            {t(`optimization.label.list.${columnType}`)}
          </>
        )
      } else {
        return (
          <span className="no-sorting-advice">
            <AdviceTooltip
              id="sa-optimization-advice-tooltip"
              title={<InnerHtml innerHTML={t(`optimization.message.searchOptimization.list.${columnType}`) || ''} />}
              placement="right-start"
              arrow
            >
              <span id="adviceIcon">
                <AdviceMarkIcon />
              </span>
            </AdviceTooltip>
            {t(`optimization.label.list.${columnType}`)}
          </span>
        )
      }
    }

    const CustomTooltip = withStyles((theme) => ({
      tooltip: {
        backgroundColor: '#56606b',
        color: 'white',
        boxShadow: theme.shadows[1],
        fontSize: 11,
        margin: '0px 0px 0px 0px',
        whiteSpace: 'pre-wrap'
      }
    }))(Tooltip)

    const getColumnBidYn = () => {
      return {
        title: getColumnTitle(SearchOptimizationListColumn.BID_YN, false),
        field: SearchOptimizationListColumn.BID_YN,
        cellStyle: {
          width: 120
        },
        sorting: false,
        render: ({ optimizationId, bidYn, status, bidEndDate, mediaType, optimizationGoal }) => {
          const disabledStatus =
            status === StatusType.INSPECTING ||
            status === StatusType.INSPECTION_ERROR ||
            status === StatusType.END ||
            !hasAuthority
          const isDisabled =
            bidYn === 'N'
              ? disabledStatus ||
                (soList && soList.maxItemsCount !== -1 && soList.currentItemsCount > soList.maxItemsCount) ||
                status === StatusType.READY ||
                status === StatusType.BIDDING ||
                compareDate(bidEndDate, currentDate()) < 0
              : disabledStatus || status === StatusType.INSPECTION_COMPLETED || status === StatusType.STOP
          if (bidYn) {
            return (
              <MopSwitch
                checked={bidYn === 'Y'}
                data-testid={`bidYnSwitch-${optimizationId}`}
                disabled={isDisabled}
                onChange={(e) => {
                  handleBidSwitchChange(e, optimizationId)
                  TagManager.dataLayer({
                    dataLayer: {
                      event: 'click',
                      gtm_id: 'sa-opt-bidYn',
                      bid_yn: bidYn === 'Y' ? 'Y' : 'N',
                      optimization_id: optimizationId,
                      media_type: mediaType,
                      optimization_goal: optimizationGoal || ''
                    }
                  })
                }}
              />
            )
          }
        }
      } as FixedLayoutColumn<SearchOptimizationInfo>
    }

    const getColumnStatus = () => {
      return {
        title: getColumnTitle(SearchOptimizationListColumn.STATUS, true),
        field: SearchOptimizationListColumn.STATUS,
        sorting: true,
        cellStyle: {
          width: 100
        },
        defaultSort: orderBy === SearchOptimizationListColumn.STATUS ? sorting?.toLowerCase() : undefined,
        render: (rowData) => {
          if (rowData.status) {
            return (
              <TransText
                data-testid={`status-${rowData.optimizationId}`}
                i18nKey={`common.code.status.${rowData.status}`}
                className={cn('font-normal', rowData.bidYn === 'Y' && 'text-status-active')}
              />
            )
          }
        }
      } as FixedLayoutColumn<SearchOptimizationInfo>
    }
    // TODO
    const getColumnOptimizationId = () => {
      return {
        title: t('optimization.label.list.optimizationId'),
        field: SearchOptimizationListColumn.OPTIMIZATION_ID,
        sorting: true,
        cellStyle: {
          width: 100
        },
        defaultSort: orderBy === SearchOptimizationListColumn.OPTIMIZATION_ID ? sorting?.toLowerCase() : undefined,

        render: (rowData) => {
          const iconVisible = {
            visibility: compareDate(rowData.bidStartDate, currentDate()) > 0 || !hasAuthority ? 'hidden' : 'visible'
          }
          if (rowData.status) {
            return (
              <div id={`status-${rowData.optimizationId}`} className={`optimizationId`}>
                <span data-testid={`mediaType-${rowData.optimizationId}`} className={`optimizationId`}>
                  {rowData.optimizationId}
                </span>
                <span className="icon duplicate" style={iconVisible as React.CSSProperties}>
                  <DuplicateIcon
                    onClick={(e) => {
                      handleRequest(e, ContextRequestType.DUPLICATE)
                      TagManager.dataLayer({
                        dataLayer: {
                          event: 'click',
                          gtm_id: 'sa-opt-copy-click'
                        }
                      })
                    }}
                    data-id={rowData.optimizationId.toString()}
                    data-testid={`duplicate-${rowData.optimizationId}`}
                  />
                </span>
              </div>
            )
          }
        }
      } as FixedLayoutColumn<SearchOptimizationInfo>
    }

    const getColumnAdgroupsCount = () => {
      return {
        title: t('optimization.label.list.adgroupsCount'),
        field: SearchOptimizationListColumn.ADGROUPS_COUNT,
        sorting: true,
        cellStyle: {
          width: 110
        },
        defaultSort: orderBy === SearchOptimizationListColumn.ADGROUPS_COUNT ? sorting?.toLowerCase() : undefined,
        render: (rowData) => {
          if (rowData.adgroupsCount !== null) {
            return (
              <div id={`adgroupsCount-${rowData.adgroupsCount}`} className={`adgroupsCount`}>
                <span data-testid={`adgroupsCount-${rowData.adgroupsCount}`} className={`adgroupsCount`}>
                  {rowData.adgroupsCount}
                </span>
              </div>
            );
          }
        }
      } as FixedLayoutColumn<SearchOptimizationInfo>
    }

    const getColumnMediaType = () => {
      return {
        title: t('optimization.label.list.mediaType'),
        field: SearchOptimizationListColumn.MEDIA_TYPE,
        sorting: false,
        cellStyle: {
          width: 120
        },

        render: (rowData) => {
          if (rowData.mediaType !== null) {
            return (
              <div id={`mediaType-${rowData.optimizationId}`} className={`mediaType`}>
                <span data-testid={`mediaType-${rowData.optimizationId}`} className={`mediaTypeLabel`}>
                  {getMediaName(rowData.mediaType)}
                </span>
              </div>
            )
          }
        }
      } as FixedLayoutColumn<SearchOptimizationInfo>
    }

    const getColumnOptimizationName = () => {
      return {
        title: t('optimization.label.list.optimizationName'),
        field: SearchOptimizationListColumn.OPTIMIZATION_NAME,
        sorting: true,
        cellStyle: {
          minWidth: 210
        },
        defaultSort: orderBy === SearchOptimizationListColumn.OPTIMIZATION_NAME ? sorting?.toLowerCase() : undefined,

        render: (rowData) => {
          if (rowData.optimizationName !== null) {
            return (
              <div id={`optimizationName-${rowData.optimizationId}`} className={`optimizationName`}>
                <CustomTooltip
                  data-testid={`optimizationNameTooltip-${rowData.optimizationId}`}
                  title={rowData.optimizationName ? rowData.optimizationName : ''}
                  placement="bottom"
                  onClick={handleRequestRead}
                >
                  <p
                    data-testid={`optimizationName-${rowData.optimizationId}`}
                    data-id={rowData.optimizationId.toString()}
                    style={{
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      cursor: 'pointer'
                    }}
                  >
                    {rowData.optimizationName}
                  </p>
                </CustomTooltip>
              </div>
            )
          }
        }
      } as FixedLayoutColumn<SearchOptimizationInfo>
    }

    const getColumnBidStartDate = () => {
      return {
        title: t('optimization.label.list.bidStartDate'),
        field: SearchOptimizationListColumn.BID_START_DATE,
        defaultSort: orderBy === SearchOptimizationListColumn.BID_START_DATE ? sorting?.toLowerCase() : undefined,
        cellStyle: {
          width: 120
        },
        render: (rowData) => {
          if (rowData.bidStartDate !== null) {
            return (
              <div id={`bidStartDate-${rowData.optimizationId}`} className={`bidStartDate`}>
                <span data-testid={`bidStartDate-${rowData.optimizationId}`} className={`bidStartDateLabel`}>
                  {rowData.bidStartDate}
                </span>
              </div>
            )
          }
        }
      } as FixedLayoutColumn<SearchOptimizationInfo>
    }

    const getColumnBidEndDate = () => {
      return {
        title: t('optimization.label.list.bidEndDate'),
        field: SearchOptimizationListColumn.BID_END_DATE,
        defaultSort: orderBy === SearchOptimizationListColumn.BID_END_DATE ? sorting?.toLowerCase() : undefined,
        cellStyle: {
          width: 120
        },
        render: (rowData) => {
          if (rowData.bidEndDate !== null) {
            return (
              <div id={`bidEndDate-${rowData.optimizationId}`} className={`bidEndDate`}>
                <span data-testid={`bidEndDate-${rowData.optimizationId}`} className={`bidEndDateLabel`}>
                  {rowData.bidEndDate === '9999.12.31' ? t('common.datePeriodPicker.unsetEndDate') : rowData.bidEndDate}
                </span>
              </div>
            )
          }
        }
      } as FixedLayoutColumn<SearchOptimizationInfo>
    }

    const getColumnDailyBudget = () => {
      return {
        title: t('optimization.label.list.dailyBudget'),
        field: SearchOptimizationListColumn.DAILY_BUDGET,
        defaultSort: orderBy === SearchOptimizationListColumn.DAILY_BUDGET ? sorting?.toLowerCase() : undefined,
        cellStyle: {
          width: 150
        },
        render: (rowData) => {
          if (rowData.dailyBudget !== null) {
            return (
              <div id={`dailyBudget-${rowData.optimizationId}`} className={`dailyBudget`}>
                <span data-testid={`dailyBudget-${rowData.optimizationId}`} className={`dailyBudgetLabel`}>
                  {rowData.dailyBudget.toLocaleString('ko-KR')}
                </span>
              </div>
            )
          }
        }
      } as FixedLayoutColumn<SearchOptimizationInfo>
    }

    const getColumnOptimizationGoal = () => {
      return {
        title: t('optimization.label.list.optimizationGoal'),
        field: SearchOptimizationListColumn.OPTIMIZATION_GOAL,
        defaultSort: orderBy === SearchOptimizationListColumn.OPTIMIZATION_GOAL ? sorting?.toLowerCase() : undefined,
        cellStyle: {
          width: 150
        },
        render: (rowData) => {
          if (rowData.optimizationGoal !== null) {
            return (
              <div id={`optimizationGoal-${rowData.optimizationId}`} className={`optimizationGoal`}>
                <span data-testid={`optimizationGoal-${rowData.optimizationId}`} className={`optimizationGoalLabel`}>
                  {getOptimizationGoalName(rowData.optimizationGoal)}
                </span>
              </div>
            )
          }
        }
      } as FixedLayoutColumn<SearchOptimizationInfo>
    }

    const getColumnCreatedDateTime = () => {
      return {
        title: t('optimization.label.list.createdDateTime'),
        field: SearchOptimizationListColumn.CREATED_DATETIME,
        defaultSort: orderBy === SearchOptimizationListColumn.CREATED_DATETIME ? sorting?.toLowerCase() : undefined,
        cellStyle: {
          width: 160
        },
        render: (rowData) => {
          if (rowData.createdDateTime !== null) {
            return (
              <div id={`createdDateTime-${rowData.optimizationId}`} className={'createdDateTime'}>
                <span data-testid={`createdDateTime-${rowData.optimizationId}`} className={'createdDateTimeLabel'}>
                  {rowData.createdDateTime}
                </span>
              </div>
            )
          }
        }
      } as FixedLayoutColumn<SearchOptimizationInfo>
    }

    const getColumnErrorStatus = () => {
      return {
        title: getColumnTitle(SearchOptimizationListColumn.ERROR_STATUS, true),
        field: SearchOptimizationListColumn.ERROR_STATUS,
        defaultSort: orderBy === SearchOptimizationListColumn.ERROR_STATUS ? sorting?.toLowerCase() : undefined,
        cellStyle: {
          width: 160
        },
        render: ({ errorStatus, optimizationId }) => {
          if (errorStatus !== null) {
            return (
              <TransText
                data-testid={`errorStatus-${optimizationId}`}
                className={cn(
                  'font-bold text-status-error',
                  errorStatus === ErrorStatusType.BUDGET_LACK && 'text-status-running',
                  errorStatus === ErrorStatusType.BUDGET_OVER && 'text-status-warning'
                )}
                i18nKey={`common.code.status.${errorStatus}`}
              />
            )
          }
        }
      } as FixedLayoutColumn<SearchOptimizationInfo>
    }

    const getColumnContextMenu = () => {
      return {
        field: SearchOptimizationListColumn.CONTEXT_MENU,
        sorting: false,
        cellStyle: {
          width: 130
        },
        render: (rowData) => {
          return (
            <div className="delete-modify-icons">
              <span className="icon">
                <EditIcon
                  onClick={(e) => {
                    handleRequestEdit(e)
                    TagManager.dataLayer({
                      dataLayer: {
                        event: 'click',
                        gtm_id: 'sa-opt-modify',
                        bid_yn: rowData.bidYn === 'Y' ? 'Y' : 'N',
                        status: rowData.status,
                        optimization_id: rowData.optimizationId,
                        media_type: rowData.mediaType,
                        optimization_goal: rowData.optimizationGoal || ''
                      }
                    })
                  }}
                  data-id={rowData.optimizationId.toString()}
                  data-testid={`edit-${rowData.optimizationId}`}
                  data-gtm-id="sa-opt-modify"
                />
              </span>
              <span className="icon">
                <DeleteIcon
                  onClick={(e) => {
                    handleRequestDelete(e)
                    TagManager.dataLayer({
                      dataLayer: {
                        event: 'click',
                        gtm_id: 'sa-opt-delete',
                        bid_yn: rowData.bidYn === 'Y' ? 'Y' : 'N',
                        status: rowData.status,
                        optimization_id: rowData.optimizationId,
                        media_type: rowData.mediaType,
                        optimization_goal: rowData.optimizationGoal || ''
                      }
                    })
                  }}
                  data-id={rowData.optimizationId.toString()}
                  data-testid={`delete-${rowData.optimizationId}`}
                  data-gtm-id="sa-opt-delete"
                />
              </span>
            </div>
          )
        }
      } as FixedLayoutColumn<SearchOptimizationInfo>
    }

    const columns: FixedLayoutColumn<SearchOptimizationInfo>[] = [
      getColumnBidYn(),
      getColumnStatus(),
      getColumnOptimizationId(),
      getColumnOptimizationName(),
      getColumnAdgroupsCount(),
      getColumnMediaType(),
      getColumnBidStartDate(),
      getColumnBidEndDate(),
      getColumnDailyBudget(),
      getColumnOptimizationGoal(),
      getColumnCreatedDateTime(),
      getColumnErrorStatus()
    ]

    if (hasAuthority) {
      columns.push(getColumnContextMenu())
    }

    return columns
  }
}
