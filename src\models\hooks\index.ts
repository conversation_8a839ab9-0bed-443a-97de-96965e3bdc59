import React from 'react'

export interface InputHookOptions {
  maxCheck?: {
    max: number
    title: string
  }
}

export interface DialogSelectItem {
  label: string
  value: string | number
  icon?: any
  iconSize?: number
  disabled?: boolean
}
export interface DialogFormOptions extends InputHookOptions {
  type?: string
  placeholder?: string
  initialValue?: string | number
  itemOptions?: DialogSelectItem[]
}

export interface CommonDialogOptions<T> {
  isOpen: boolean
  onClose?: () => void
  onCancel?: () => void
  onAction: (value: T) => void
  actionGtmId?: string
  title?: string
  message?: string
  render?: React.ReactNode
  actionLabel?: string
  cancelLabel?: string
  showClose?: boolean
  inputOptions?: DialogFormOptions
  selectOptions?: DialogFormOptions
}

export type DialogOptions<T> = Omit<CommonDialogOptions<T>, 'isOpen'>

export type ActionParams = {
  inputValue?: string | number
  selectValue?: string | number
}

export enum IntroPopupType {
  NEW = 'NEW',
  RESTORE = 'RESTORE',
  UNAVAILABLE = 'UNAVAILABLE',
  RESTRICT = 'RESTRICT',
  GENERAL = 'GENERAL'
}

export interface IntroPopupParams {
  type: IntroPopupType
  onClose?: () => void
  isOpen: boolean
  title?: string
  subTitle?: string
  message: string
  date?: string
}

export type IntroPopupOptions = Omit<IntroPopupParams, 'isOpen'>
