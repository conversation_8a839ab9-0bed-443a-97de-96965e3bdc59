import React, { memo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { MopSearch } from '@components/common'
import MopButton from '@components/common/buttons/MopButton'
import MopSelect from '@components/common/mopUI/MopSelect'
import { MOPIcon } from '@models/common'
import './MaterialListFilterControl.scss'

export enum RecommendationType {
  CONSERVATIVE = 'conservative',
  AGGRESSIVE = 'aggressive',
  SELECT_ALL = 'select_all'
}

interface FilterState {
  material: string
  campaign: string
  adGroup: string
}

interface Props {
  analysisDate?: string
  onRecommendationTypeChange?: (type: RecommendationType) => void
  onFilterChange?: (filters: FilterState) => void
  onDownload?: () => void
  campaignOptions?: Array<{ value: string; label: string }>
  adGroupOptions?: Array<{ value: string; label: string }>
}

const MaterialListFilterControl: React.FC<Props> = memo(({
  analysisDate = '2025.08.01',
  onRecommendationTypeChange,
  onFilterChange,
  onDownload,
  campaignOptions = [
    { value: 'B2B_Notebook_PC', label: 'B2B_Notebook_PC' }
  ],
  adGroupOptions = [
    { value: 'all', label: '전체' }
  ]
}: Props) => {
  const { t } = useTranslation()
  
  const [selectedRecommendationType, setSelectedRecommendationType] = useState<RecommendationType>(RecommendationType.CONSERVATIVE)
  const [filters, setFilters] = useState<FilterState>({
    material: '',
    campaign: campaignOptions[0]?.value || '',
    adGroup: adGroupOptions[0]?.value || ''
  })

  const handleRecommendationTypeChange = (type: RecommendationType) => {
    setSelectedRecommendationType(type)
    onRecommendationTypeChange?.(type)
  }

  const handleFilterChange = (field: keyof FilterState, value: string) => {
    const newFilters = { ...filters, [field]: value }
    setFilters(newFilters)
    onFilterChange?.(newFilters)
  }

  const handleDownload = () => {
    onDownload?.()
  }

  return (
    <div className="material-list-filter-control">
      {/* Header Section */}
      <div className="filter-header">
        <div className="header-left">
          <h2 className="filter-title">
            {t('negativeKeyword.title')}
          </h2>
          <div className="recommendation-controls">
            {/* Toggle for Conservative/Aggressive */}
            <div className="recommendation-toggle">
              <button
                className={`toggle-option ${selectedRecommendationType === RecommendationType.CONSERVATIVE ? 'active-conservative' : ''}`}
                onClick={() => handleRecommendationTypeChange(RecommendationType.CONSERVATIVE)}
              >
                {t('negativeKeyword.recommendation.conservative')}
              </button>
              <button
                className={`toggle-option ${selectedRecommendationType === RecommendationType.AGGRESSIVE ? 'active-aggressive' : ''}`}
                onClick={() => handleRecommendationTypeChange(RecommendationType.AGGRESSIVE)}
              >
                {t('negativeKeyword.recommendation.aggressive')}
              </button>
            </div>
            
            {/* Separate Select All button */}
            <MopButton
              label={t('negativeKeyword.recommendation.selectAll')}
              onClick={() => handleRecommendationTypeChange(RecommendationType.SELECT_ALL)}
              size="md"
              rounded="full"
              contained={selectedRecommendationType === RecommendationType.SELECT_ALL}
              bgColor={selectedRecommendationType === RecommendationType.SELECT_ALL ? '#E3F2FD' : '#F5F5F5'}
              textColor={selectedRecommendationType === RecommendationType.SELECT_ALL ? '#1976D2' : '#666666'}
              customClass="select-all-button"
            />
          </div>
        </div>
        <div className="header-right">
          <span className="analysis-date">
            {t('negativeKeyword.analysisDate')} {analysisDate}
          </span>
          <MopButton
            onClick={handleDownload}
            size="sm"
            rounded="sm"           
            contained={false}
            rightIcon={MOPIcon.DOWNLOAD}
            customClass="download-button"
          />
        </div>
      </div>

      <div className="filter-section">
        <div className="filter-row">
          <div className="filter-item">
            <label className="filter-label">
              {t('negativeKeyword.filters.material')}
            </label>
            <MopSearch
              id="materialSearch"
              placeholder={t('negativeKeyword.filters.materialPlaceholder')}
              value={filters.material}
              onChange={(e) => handleFilterChange('material', e.target.value)}
              visibleIcon
              size={16}
            />
          </div>
          
          <div className="filter-item">
            <label className="filter-label">
              {t('negativeKeyword.filters.campaign')}
            </label>
            <MopSelect
              id="campaignSelect"
              value={filters.campaign}
              onChange={(value) => handleFilterChange('campaign', value as string)}
              options={campaignOptions}
              placeholder={t('negativeKeyword.filters.campaignPlaceholder')}
            />
          </div>
          
          <div className="filter-item">
            <label className="filter-label">
              {t('negativeKeyword.filters.adGroup')}
            </label>
            <MopSelect
              id="adGroupSelect"
              value={filters.adGroup}
              onChange={(value) => handleFilterChange('adGroup', value as string)}
              options={adGroupOptions}
              placeholder={t('negativeKeyword.filters.adGroupPlaceholder')}
            />
          </div>
        </div>
      </div>
    </div>
  )
})


export default MaterialListFilterControl
