import { Tooltip } from '@material-ui/core';
import { withStyles } from '@material-ui/core/styles';

const CommonTooltip = withStyles((theme) => ({
  tooltip: {
    border: '1px solid var(--point_color)',
    borderRadius: 0,
    backgroundColor: '#ffffff',
    color: 'var(--point_color)',
    minWidth: '360px',
    boxShadow: theme.shadows[2],
    fontFamily: 'Noto Sans Korean, sans-serif',
    fontSize: 12,
    lineHeight: 1.92,
    margin: '0px 14px',
    padding: '0 0 12px 0',
    whiteSpace: 'pre-wrap',
    '& > h1, & > h2, & > h3, & > h4, & > h5, & > h6': {
      fontSize: 14,
      margin: 0,
      padding: '12px 0px',
      borderBottom: '1px solid #9196a4',
      fontWeight: 700,
      textAlign: 'center'
    },
    '& span.cs-notice': {
      color: 'var(--error_color)'
    },
    '& ul': {
      padding: 0,
      margin: 0,
      marginTop: '8px',
      listStylePosition: 'inside'
    },
    '& p.desc.bold': {
      fontWeight: 700,
      margin: '4px 0'
    },
    '& p.desc': {
      padding: 0
    },
    '& p': {
      padding: '8px 12px 6px',
      margin: 0,
      fontSize: 12,
      fontWeight: 500
    },
    '& .indent1': {
      padding: '0 12px 0 14px',
      textIndent: -14
    },
    '& .indent2': {
      padding: '0 12px 0 30px',
      textIndent: -14
    },
    '& .indent3': {
      padding: '0 12px 0 38px',
      textIndent: -38
    },
    '& .error': {
      color: 'var(--error_color)'
    },
    '& svg': {
      verticalAlign: 'text-bottom',
      display: 'inline'
    },
    '& .common-style': {
      padding: '8px 12px',
      whiteSpace: 'pre-line',
      wordBreak: 'keep-all'
    },
    '& .common-style ul.desc-list': {
      margin: '8px 16px',
      listStylePosition: 'outside'
    },
    '& .common-style ul.desc-list > li': {
      padding: 0,
      lineHeight: 1.5
    },
    '& .common-style > p': {
      fontSize: 12,
      fontWeight: 400,
      lineHeight: 1.5
    },
    '& .common-style > ul': {
      margin: 0,
      listStylePosition: 'inside',
      fontWeight: 400
    },
    '& .common-style > h2': {
      margin: '5px 0 0 0',
      padding: '8px 12px 0px',
      fontSize: 12,
      fontWeight: 800,
      lineHeight: 1.5
    },
    '& .common-style > dl': {
      margin: 0,
      // padding: '8px 12px 6px',
      display: 'flex',
      flexFlow: 'row wrap',
      fontWeight: 500,
      lineHeight: 1.5
    },
    '& .common-style > dl > dt': {
      width: '38%',
      fontWeight: 800
    },
    '& .common-style > dl > dd': {
      width: '62%',
      marginLeft: 0
    },
    '& .common-style > .w35_w65 > dt': {
      width: '35%'
    },
    '& .common-style > .w35_w65 > dd': {
      width: '65%'
    },
    '& .common-style > .w32_w68 > dt': {
      width: '32%'
    },
    '& .common-style > .w32_w68 > dd': {
      width: '68%'
    },
    '& .common-style > .w20_w80 > dt': {
      marginTop: '3px',
      width: '20%'
    },
    '& .common-style > .w20_w80 > dd': {
      marginTop: '3px',
      width: '80%'
    },
    '& .common-style > .w22_w78 > dt': {
      marginTop: '3px',
      width: '22%'
    },
    '& .common-style > .w22_w78 > dd': {
      marginTop: '3px',
      width: '78%'
    },
    '& .common-style > .w12_w88 > dt': {
      marginTop: '3px',
      width: '15%'
    },
    '& .common-style > .w12_w88 > dd': {
      marginTop: '3px',
      width: '85%'
    },
  },
  arrow: {
    '&:before': {
      border: '1px solid var(--point_color)'
    },
    color: '#ffffff'
  }
}))(Tooltip)

export default CommonTooltip;
