.keyword-tabs {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  height: 100%;

  .tabs-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f9f9fb;

    .keyword-tabs-component {
      flex: 1;

      .MuiTabs-root {
        min-height: 48px;
      }

      .MuiTab-root {
        min-height: 48px;
        padding: 0 16px;
        text-transform: none;
        font-weight: 500;
        color: #666;

        &.Mui-selected {
          color: #333333;
          border-bottom-color: #333333;
        }

        .tab-label {
          display: flex;
          align-items: center;
          gap: 8px;

          .tab-count {
            background: #ffff;
            color: #333333;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 700;
            min-width: 20px;
            text-align: center;
          }
        }
      }

      .MuiTabs-indicator {
        background-color: #333333;
        height: 2px;
      }
    }

    .close-button {
      background: none;
      border: none;
      padding: 8px;
      cursor: pointer;
      color: #666;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      margin-right: 8px;

      &:hover {
        background: #f0f0f0;
        color: #333;
      }

      svg {
        width: 16px;
        height: 16px;
      }
    }
  }

  .tab-content {
    overflow-y: auto;
    padding: 5px;
    height: 100%;
    .keyword-list {
       .keyword-item {
         padding: 7.5px 10px;
         border-bottom: 1px solid #F2F3F6;
         position: relative;

         &.sub-item {
           margin-left: 10px;
         }

         .keyword-row {
           display: flex;
           align-items: center;
           gap: 8px;
         }

         .keyword-content {
           flex: 1;
           min-width: 0;
           display: flex;
           align-items: flex-start;
           gap: 8px;

           .expand-button {
             background: none;
             border: none;
             padding: 4px;
             cursor: pointer;
             color: #999;
             display: flex;
             align-items: center;
             justify-content: center;
             border-radius: 4px;
             margin-top: 2px;
             transition: all 0.2s ease;
             flex-shrink: 0;

             &:hover {
               background: #f0f0f0;
               color: #666;
             }

             svg {
               width: 16px;
               height: 16px;
             }
           }

           .keyword-text-container {
             width: 100%;
             display: flex;
             align-items: center;
             gap: 8px;
           }

           .keyword-text {
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            cursor: pointer;
           }

           .keyword-id {
             font-size: 12px;
             color: #999;
             font-family: monospace;
           }
         }

        .keyword-count {
          background: #F2F3F6;
          color: #666;
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 10px;
          font-weight: 500;
          margin-right: 8px;
          min-width: 20px;
          text-align: center;
        }

        .remove-button {
          background: none;
          border: none;
          padding: 4px;
          cursor: pointer;
          color: #999;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 4px;
          opacity: 0.7;
          transition: all 0.2s ease;
          flex-shrink: 0;
          width: 24px;
          height: 24px;

          &:hover {
            background: #ffebee;
            color: #d32f2f;
            opacity: 1;
          }

          svg {
            width: 16px;
            height: 16px;
          }
        }

        &:hover {
          background: #f8f9fa;

          .remove-button {
            opacity: 1;
          }
        }
      }

      .sub-items {
        margin-top: 8px;
      }
    }
  }

  // Scrollbar styling
  .tab-content::-webkit-scrollbar {
    width: 6px;
  }

  .tab-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  .tab-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}
