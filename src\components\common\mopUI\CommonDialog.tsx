import { Listbox, Popover } from '@headlessui/react'
import { useEffect, useMemo, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { MopActionButton } from '../buttons'
import MopIcon from '../icon/MopIcon'
import InnerHtml from '../InnerHtml'
import DialogOverlay from '../dialog/DialogOverlay'
import { useDialog, useInput } from '@hooks/common'
import { MOPIcon } from '@models/common'
import type { DialogOptions } from '@models/hooks'

const store = {
  isReady: false,
  setState: (_params: DialogOptions<any>) => {}
}

// utils/ApiUtil.ts 에만 사용됨 그 외에는 useDialog 사용
export const openCommonDialog = (options: DialogOptions<any>) => {
  store.setState(options)
}

const CommonDialog = () => {
  const { t } = useTranslation()
  const actionButtonRef = useRef(null)
  const { dialog, closeDialog, openDialog } = useDialog()
  const { setValue: setInputValue, ...inputProps } = useInput('', dialog?.inputOptions)
  const { setValue: setSelectValue, ...selectProps } = useInput()
  const selected = useMemo(
    () => dialog.selectOptions?.itemOptions?.find((opt) => opt.value === selectProps.value),
    [selectProps.value]
  ) //eslint-disable-line

  const handleAction = () => {
    dialog.onAction({
      inputValue: inputProps.value,
      selectValue: selectProps.value
    })
    closeDialog()
  }

  const handleClose = () => {
    dialog.onCancel?.()
    if (dialog.onClose) dialog.onClose()
    closeDialog()
  }

  useEffect(() => {
    if (dialog.inputOptions?.initialValue) setInputValue(dialog.inputOptions?.initialValue)
    if (dialog.selectOptions?.initialValue) setSelectValue(dialog.selectOptions?.initialValue)
  }, [dialog.selectOptions]) //eslint-disable-line

  useEffect(() => {
    if (!store.isReady) {
      store.isReady = true
      store.setState = (params) => openDialog(params)
    }
    return () => {
      store.isReady = false
    }
  }, []) //eslint-disable-line

  return (
    <DialogOverlay initialFocus={actionButtonRef} show={dialog.isOpen} close={closeDialog}>
      <div className={`w-full relative p-4 ${dialog.showClose ? 'border-b border-gray-300' : ''}`}>
        <p className="font-black text-primary-text text-base">{dialog.title}</p>
        {dialog.showClose && (
          <MopIcon
            customClass="absolute inset-y-4 right-3"
            name={MOPIcon.CLOSE}
            onClick={handleClose}
            size={20}
            bgColor="#f3f3f6"
          />
        )}
      </div>
      <div className="my-6 px-6 flex flex-col gap-6">
        {dialog.render ? dialog.render : dialog.message && <InnerHtml className="text-primary-text text-xl" innerHTML={dialog.message} />}
        {dialog.inputOptions && (
          <input
            {...inputProps}
            type={dialog.inputOptions.type ?? 'text'}
            className="border border-gray-300 text-primary-text rounded-full text-center outline-none focus:border-blue-600 block w-full p-4"
            placeholder={dialog.inputOptions.placeholder}
            required
          />
        )}
        {dialog.selectOptions && (
          <Popover className="relative">
            <Popover.Button className="border border-gray-300 text-primary-text rounded-full block w-full p-1">
              {selected ? (
                <div className="flex justify-center items-center gap-4 text-xs cursor-pointer">
                  {selected.icon && (
                    <MopIcon name={MOPIcon[selected.icon as keyof typeof MOPIcon]} size={selected.iconSize} />
                  )}
                  <span className="min-w-[50px] text-center">{selected.label}</span>
                </div>
              ) : (
                dialog.selectOptions.placeholder
              )}
            </Popover.Button>
            <Popover.Panel className="absolute z-100 bg-white w-full">
              {({ close }) => (
                <Listbox {...selectProps}>
                  <Listbox.Options static className="border border-gray-300">
                    {dialog.selectOptions?.itemOptions?.map((item, index) => (
                      <Listbox.Option
                        as="div"
                        className={({ active, disabled }) => `
                          flex justify-center items-center gap-4 text-xs py-1 cursor-pointer hover:bg-zinc-100
                          ${disabled ? 'opacity-50 cursor-not-allowed' : ''} ${active ? 'font-bold' : ''}`}
                        key={index}
                        value={item.value}
                        disabled={item.disabled}
                        onClick={() => close()}
                      >
                        {item.icon && (
                          <MopIcon name={MOPIcon[item.icon as keyof typeof MOPIcon]} size={item.iconSize} />
                        )}
                        <span className="min-w-[50px] text-center">{item.label}</span>
                      </Listbox.Option>
                    ))}
                  </Listbox.Options>
                </Listbox>
              )}
            </Popover.Panel>
          </Popover>
        )}
        <div className="flex">
          {dialog.cancelLabel && (
            <MopActionButton qaId="close-button" theme="cancel" label={dialog.cancelLabel} onClick={handleClose} />
          )}
          <MopActionButton
            ref={actionButtonRef}
            qaId="action-button"
            gtmId={dialog.actionGtmId}
            label={dialog.actionLabel ?? t('common.label.button.save')}
            onClick={handleAction}
          />
        </div>
      </div>
    </DialogOverlay>
  )
}

export default CommonDialog
