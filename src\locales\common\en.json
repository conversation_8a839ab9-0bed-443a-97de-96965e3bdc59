{"button": {"back": "Back", "confirm": "Confirm", "cancel": "Cancel", "search": "Search", "save": "Save", "ask": "Query", "init": "Reset", "delete": "Delete", "copy": "Copy", "create": "Create New", "more": "See More", "apply": "Apply", "previous": "Previous", "continue": "Next", "done": "Done", "ok": "OK", "hideAday": "Do not show again for a day", "create-e": "CREATE", "save-e": "SAVE", "reset-e": "RESET"}, "label": {"unlimited": "Unlimited", "media": "Media", "device": "<PERSON><PERSON>", "keyword": "Keyword", "keywordId": "Keyword ID", "advertiser": "Ad Circle Name", "advertisement": "Advertisement", "adId": "Ad ID", "ad": "Ad", "campaignAdgroup": "Campaign / Ad group", "campaign": "CAMPAIGN", "region": "Region", "selectOption": "Please select", "customIncrememtal": "Custom Bid Increment", "rank": "Rank", "maxCpcbid": "Max CPC Bid", "select": "Select", "delete": "Delete", "filter": {"empty": "Empty", "all": "All", "placeholder": "Search", "status": "Status", "performanceImpact": "Estimated Impact", "allocationYn": "ON/OFF", "media": "Media", "optimizationGoal": "Opt Goal", "allocationPeriod": "Start Date", "optimizationName": "Opt Name", "adType": "Ad Type", "device": "<PERSON><PERSON>", "needSelect": "Please Select", "date": "DATE", "settingView": "VIEW"}, "auth": {"ADMINISTRATE": "Administrator", "OPERATE": "Operator", "READ": "Viewer"}, "button": {"create": "Create", "save": "SAVE", "more": "Learn More", "optName": "Opt Name", "optId": "Opt ID", "cancel2": "Cancel", "previous": "Back", "back": "Back to List", "continue": "Next", "done": "Complete", "cancel-k": "Cancel", "apply": "Apply", "delete": "DELETE", "cancel": "NO", "delete-k": "Delete", "backList": "Back to List"}, "currency": {"KRW": "KRW", "USD": "USD", "EUR": "EUR"}}, "download": {"column": {"adgroupName": "Ad Group Name"}}, "message": {"title": {"notice": "NOTICE"}, "loading": "Loading...", "validation": {"required": "\"{{- param}}\" is a required."}, "list": {"noData": "No Data Found"}, "deleteConfirm": "Would you like to delete this?", "upgradePlan": "Upgrade to Pro plan to extend your functionality!", "upgradeBasicToPaidPlan": "Check out the latest 7 day trends after upgrading to the Pro/Lite plan!", "upgrade": "Upgrade"}, "datePeriodPicker": {"unsetEndDate": "No End Date", "button": {"today": "Today", "yesterday": "Yesterday", "last7": "Last 7 days", "last14": "Last 14 days", "last30": "Last 30 days", "last60": "Last 60 days", "last90": "Last 90 days", "custom": "Custom", "previous": "Previous Period"}, "message": {"limited": "Report data is limited to data after 2023 September 1st"}, "tooltip": {"title": "Report Period", "compareTitle": "Compare Period", "search": "Recent {{period}}days available for search as of today", "pro_search": "can be set from {{period}} days prior to the unit registration date up to today, with a maximum range of {{maxRange}} days.", "compare": "The Comparison Period cannot overlap with the Report Period."}}, "code": {"onoff": {"ON": "ON", "OFF": "OFF", "null": "-"}, "media": {"NAVER": "NAVER", "KAKAO": "KAKAO", "GOOGLE": "GOOGLE", "FACEBOOK": "FACEBOOK", "INSTAGRAM": "INSTARGRAM", "META": "META", "GA": "GA", "GA4": "GA4", "AIRBRIDGE": "AIRBRIDGE", "ALL": "ALL", "APPSFLYER": "APPSFLYER", "CRITEO": "CRITEO"}, "optimizationGoal": {"ALL": "All", "VIEW": "Views", "CLICK": "Click", "CONVERSION": "Conversion", "KPIS": "Multi-Objective", "ROAS": "ROAS"}, "budgetChangeRate": {"FIVE_OR_LESS": "5% ↓", "TEN_OR_LESS": "10% ↓", "TWENTY_OR_LESS": "20% ↓", "DIRECT": "Enter"}, "contributionType": {"TRANSACTION": "Transaction", "GOAL": "Other conversions", "TOTAL": "All (Transaction + Other conversions)", "NONE": "None"}, "status": {"INSPECTING": "In Review", "INSPECTION_ERROR": "Review Failed", "INSPECTION_COMPLETED": "Review Complete", "ALLOCATING": "Allocating", "READY": "Preparing", "BIDDING": "Bidding", "STOP": "Paused", "END": "Ended", "BUDGET_LACK": "Insufficient Budget", "BUDGET_OVER": "Excessive Budget", "OPTIMIZATION_ERROR": "Optimization Error", "BID_ERROR": "<PERSON><PERSON><PERSON>r", "OFF_BY_BATCH": "Bidding OFF"}, "saStatus": {"INSPECTING": "Inspecting", "INSPECTION_ERROR": "Inspection Error", "INSPECTION_COMPLETED": "Inspection Completed", "READY": "Ready", "BIDDING": "Bidding", "STOP": "Stop", "END": "End"}, "errorStatus": {"BUDGET_LACK": "Insufficient Budget", "BUDGET_OVER": "Excessive Budget", "OPTIMIZATION_ERROR": "Optimization Error", "BID_ERROR": "<PERSON><PERSON><PERSON>r", "OFF_BY_BATCH": "Bidding OFF"}, "chartDataType": {"CURRENT_RANK": "Current Rank", "TARGET_RANK": "Target Rank", "BID_AMOUNT": "<PERSON><PERSON>", "time": "TIME"}, "saShoppingType": {"CATALOG": "Product Catalog Type", "SHOPPING": "Shopping Mall Type"}}, "subscription": {"type": {"ABNORMAL_SPA": "쇼핑이상감지", "ABNORMAL_URL": "URL 이상감지", "ABNORMAL_UTM": "UTM 이상감지", "ABNORMAL_COST": "비용이상감지", "REPORT_CONV_KW": "신규 전환 키워드 분석 리포트", "REPORT_SURGE_KW": "노출/클릭 급상승 키워드 분석 리포트"}}, "multiSelector": {"selectAll": "Select All", "unselectAll": "Unselect All", "selectAllFiltered": "Select All Filtered", "unselectAllFiltered": "Unselect All Filtered", "noOptions": "No Data"}, "modal": {"label": {"CREATE": "New", "DUPLICATE": "New", "MODIFY": "Edit", "READ": "View"}, "title": {"notice": "Notice"}, "button": {"no": "No", "yes": "Yes", "delete": "Delete", "confirm": "Confirm", "cancel": "Cancel"}}}