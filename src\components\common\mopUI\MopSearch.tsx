import React, { ReactElement, KeyboardEvent } from 'react'
import { MOPIcon } from '@models/common';
import MopIcon from '../icon/MopIcon'
import './MopSearch.scss';

interface Props {
  id?: string;
  value: string;
  placeholder?: string;
  onChange?: React.ChangeEventHandler<HTMLInputElement>;
  onFocus?: React.FocusEventHandler<HTMLInputElement>;
  onSearch?: () => void;
  disabled?: boolean;
  visibleIcon?: boolean;
  size?: number;
  type?: string;  // raw-data, commerce
}

const MopSearch = ({
  id,
  placeholder,
  value,
  onSearch,
  onChange,
  onFocus,
  disabled=false,
  visibleIcon,
  type,
  size
}: Props): ReactElement => {
  const onKeyUp = (event: KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') onSearch && onSearch()
  }
  return (
    <div className={`mop-search-wrapper ${type}`} role="searchbox">
      <input
        className={'mop-search__input'}
        value={value}
        placeholder={placeholder}
        onChange={onChange}
        onFocus={onFocus}
        id={id}
        onKeyUp={onKeyUp}
        onKeyDown={(e) => e.stopPropagation()}
        disabled={disabled}
      />
      {visibleIcon ? (
        <MopIcon
          size={size}
          name={MOPIcon.SEARCH}
          customClass="search-icon"
          onClick={onSearch}
          data-testid={`search-test-${id}-button`}
        />
      ) : null}
    </div>
  )
};

export default MopSearch;
