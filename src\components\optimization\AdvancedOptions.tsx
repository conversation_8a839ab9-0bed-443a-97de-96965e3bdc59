import React, { useEffect, useState, useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import { FormLabel, TextField, Switch, Slider, Radio } from '@material-ui/core'
import { TransText } from '@components/common'
import TooltipCard from '@components/common/tooltip/TooltipCard'
import IntegerNumberFormat from '../common/IntegerNumberFormat'
import DecimalNumberFormat from '../common/DecimalNumberFormat'
import { KpiType } from '@models/optimization/Kpi'
import './AdvancedOptions.scss'
import CommonTooltip from '@components/common/CommonTooltip'
import { ReactComponent as AdviceMarkIcon } from '@components/assets/images/advicemark.svg'
import InnerHtml from '@components/common/InnerHtml'
import { SearchOptimizationDetail } from '@models/optimization/SearchOptimization'
import { ShoppingOptimizationDetail } from '@models/shoppingOptimization/ShoppingOptimization'
import tw from 'twin.macro'
import styled from '@emotion/styled'
import { debounce } from 'lodash'
import { useAuthority, useToast } from '@hooks/common';
import { BetaBadge, LiteBadge, ProBadge } from '@components/common/BaseChip';
import { SubscriptionProductType } from '@models/common/Advertiser'

export enum OptionType {
  SA = 'SA',
  SHOPPING = 'SHOPPING',
  CATALOG = 'CATALOG'
}
export interface Props {
  optimizationInfo: SearchOptimizationDetail | ShoppingOptimizationDetail
  handleChangeOptimizationInfo: (_key: string, _value: any, _init?: boolean) => void
  updateKpi: (kpiType: KpiType, rawValue: string) => void
  optionType: string
  isEditType: boolean
  isReadType: boolean
  todayDate: string
}

const AdvancedOptions = ({
  optimizationInfo,
  handleChangeOptimizationInfo,
  updateKpi,
  optionType,
  isEditType,
  isReadType,
  todayDate
}: React.PropsWithChildren<Props>) => {
  const { t } = useTranslation()
  const { openToast } = useToast();
  const kpis = optimizationInfo?.kpis || []
  const cpc = kpis.find((item) => item.kpiType === KpiType.CPC)
  const cpa = kpis.find((item) => item.kpiType === KpiType.CPA)
  const cpc_max = kpis.find((item) => item.kpiType === KpiType.CPC_MAX)
  const use_radio = optionType !== OptionType.CATALOG
  const [selectedKpiType, setSelectedKpiType] = useState<KpiType | null>(
    cpc ? KpiType.CPC : cpa ? KpiType.CPA : use_radio ? null : KpiType.CPC
  )
  const is_disabled = (isEditType && optimizationInfo.bidEndDate < todayDate) || isReadType
  const [boostingRateLevel, setBoostingRateLavel] = useState<number>(optimizationInfo.boostingRateLevel || 0)
  const [minImps, setMinImps] = useState<number>((optimizationInfo as ShoppingOptimizationDetail).minImps || 0)
  const [isUserInteracting, setIsUserInteracting] = useState<boolean>(false)

  const { isProAdvertiser, isLiteAdvertiser, isBasicAdvertiser } = useAuthority();

  const setOptimizationInfoDebounce = useCallback(
    debounce((boostingRateLevelValue, minImpsValue) => {
      handleChangeOptimizationInfo('boostingRateLevel', boostingRateLevelValue)
      handleChangeOptimizationInfo('minImps', minImpsValue)
    }, 100),
    [handleChangeOptimizationInfo]
  )

  useEffect(() => {
    if (!isUserInteracting) {
      setBoostingRateLavel(optimizationInfo.boostingRateLevel)
      setMinImps((optimizationInfo as ShoppingOptimizationDetail).minImps)
    }
  }, [optimizationInfo, isUserInteracting])

  useEffect(() => {
    if (isUserInteracting) {
      setOptimizationInfoDebounce(boostingRateLevel, minImps)
    }
  }, [boostingRateLevel, minImps, isUserInteracting, setOptimizationInfoDebounce])

  useEffect(() => {
    if (!selectedKpiType) {
      setSelectedKpiType(cpc ? KpiType.CPC : cpa ? KpiType.CPA : use_radio ? null : KpiType.CPC)
    }
  }, [cpc, cpa])

  const handleChangeSelectedType = (selectedType: KpiType) => {
    setSelectedKpiType(selectedType)
    updateKpi(KpiType.CPC, '')
    updateKpi(KpiType.CPA, '')
  }
  
  return (
    <div id="advanced-options" className="py-2 px-5 grid grid-cols-[404px_1fr] gap-x-9">
      <div className="border-r-[1px] border-[#E4E7EE]">
        <h5 className="flex items-center gap-2.5">
          <span className="text-sm text-primary-text leading-8 font-bold">
            {t('optimization.AdvancedOptions.label.advancedBidSetting')}
          </span>
          {/* <small className="text-[#8B8B8B] text-xs">{t('optimization.AdvancedOptions.label.bidBudgetLimit')}</small> */}
        </h5>
        <div id="kpi-lable-limit-option-items" className="grid grid-cols-[170px_1fr] gap-y-2.5 items-center">
          <div className="text-primary-text">
            <CommonTooltip
              id="sa-optimization-advice-tooltip-averageCPC"
              title={<TooltipCard tKey="optimization.AdvancedOptions.tooltip.averageCPC" type="paragraph" />}
              placement="right-start"
              arrow
            >
              <span>
                <AdviceMarkIcon className="mr-0.5 mb-[-2px]" />
                <span className="text-xs">Average&nbsp;</span>
              </span>
            </CommonTooltip>
            <FormLabel disabled={is_disabled}>
              {use_radio && (
                <Radio
                  className="!ml-2"
                  name={'KPI_TYPE'}
                  value={KpiType.CPC}
                  disabled={is_disabled}
                  onChange={() => handleChangeSelectedType(KpiType.CPC)}
                  checked={selectedKpiType === KpiType.CPC}
                  disableRipple
                />
              )}
              <TransText as="span" className="text-xs" i18nKey="optimization.AdvancedOptions.label.averageCPC" />
            </FormLabel>
            {optionType !== OptionType.CATALOG && (
              <FormLabel disabled={is_disabled}>
                {use_radio && (
                  <Radio
                    className="!ml-2"
                    name={'KPI_TYPE'}
                    value={KpiType.CPA}
                    disabled={is_disabled}
                    onChange={() => handleChangeSelectedType(KpiType.CPA)}
                    checked={selectedKpiType === KpiType.CPA}
                    disableRipple
                  />
                )}
                <TransText as="span" className="text-xs" i18nKey="optimization.AdvancedOptions.label.averageCPA" />
              </FormLabel>
            )}
          </div>
          {selectedKpiType === KpiType.CPA ? (
            <IntegerNumberFormat
              className="advanced-option-input !bg-red"
              disabled={is_disabled || (use_radio && selectedKpiType !== KpiType.CPA)}
              variant="outlined"
              size="small"
              value={selectedKpiType === KpiType.CPA ? cpa?.kpiValue : ''}
              customInput={TextField}
              onChange={(e: { target: { value: string } }) => {
                updateKpi(KpiType.CPA, e.target.value)
              }}
            />
          ) : (
            <DecimalNumberFormat
              className="advanced-option-input"
              disabled={is_disabled || (use_radio && selectedKpiType !== KpiType.CPC)}
              variant="outlined"
              size="small"
              value={selectedKpiType === KpiType.CPC ? cpc?.kpiValue : ''}
              customInput={TextField}
              onChange={(e: { target: { value: string } }) => {
                updateKpi(KpiType.CPC, e.target.value)
              }}
            />
          )}
          <FormLabel disabled={is_disabled || optimizationInfo.exclusiveMaxCpcYn !== 'Y'}>
            <CommonTooltip
              id="sa-optimization-advice-tooltip-maxCPC"
              title={
                <>
                  <h1 className="flex justify-center items-center">
                    {t('optimization.AdvancedOptions.tooltip.maxCPC.title')}
                  </h1>
                  <div className="common-style">
                    <p>{t('optimization.AdvancedOptions.tooltip.maxCPC.contents.0')}</p>
                    <p className="!mt-2.5 !py-2.5 text-center align-middle leading-relaxed bg-[#F2F3F6] rounded-sm">
                      <ProBadge disabled={false} />&nbsp;
                      <LiteBadge disabled={false} /><br/>
                      &nbsp;
                      {t('optimization.AdvancedOptions.tooltip.maxCPC.contents.1')}
                    </p>
                  </div>
                </>
              }
              placement="right-start"
              arrow
            >
              <AdviceMarkIcon className="mr-0.5 mb-[-2px]" />
            </CommonTooltip>
            <TransText as="span" className="text-xs" i18nKey="optimization.AdvancedOptions.label.maxCPC" />
            <ProBadge disabled={is_disabled || !isProAdvertiser && !isBasicAdvertiser} className="ml-1 mr-1" />
            <LiteBadge disabled={is_disabled || !isLiteAdvertiser && !isBasicAdvertiser}/>
          </FormLabel>
          <DecimalNumberFormat
            className="advanced-option-input"
            disabled={is_disabled || optimizationInfo.exclusiveMaxCpcYn !== 'Y'}
            variant="outlined"
            size="small"
            value={cpc_max?.kpiValue}
            customInput={TextField}
            onChange={(e: { target: { value: string } }) => updateKpi(KpiType.CPC_MAX, e.target.value)}
          />
        </div>
      </div>
      <div>
        <h5 className="flex items-center gap-2.5">
          <span className="text-sm leading-8 font-bold text-primary-text">
            {t('optimization.AdvancedOptions.label.advancedAlgorithmSetting')}
          </span>
          {/* <small className="text-[#8B8B8B] text-xs">
            {t('optimization.AdvancedOptions.label.configMopAdvancedAlgorithm')}
          </small> */}
        </h5>
        <div className="grid grid-cols-[190px_1fr] gap-y-3.5 items-center">
          <FormLabel disabled={is_disabled}>
            <CommonTooltip
              id="sa-optimization-advice-tooltip-topRankImpressionBoosted"
              title={
                <>
                  <h1 className="flex justify-center items-center">
                    {t('optimization.AdvancedOptions.tooltip.topRankImpressionBoosted.title')}
                  </h1>
                  <div className="common-style">
                    <p>{t('optimization.AdvancedOptions.tooltip.topRankImpressionBoosted.contents')}</p>
                  </div>
                </>
              }
              placement="right-start"
              arrow
            >
              <AdviceMarkIcon className="mr-0.5 mb-[-2px]" />
            </CommonTooltip>
            <TransText
              as="span"
              className="text-xs"
              i18nKey="optimization.AdvancedOptions.label.topRankImpressionBoosted"
            />
          </FormLabel>
          <div>
            <Switch
              className="float-right"
              edge="end"
              color="primary"
              onChange={(event, checked) => {
                handleChangeOptimizationInfo('topRankImpressionBoosted', checked)
              }}
              checked={!!optimizationInfo?.topRankImpressionBoosted}
              disabled={is_disabled}
              disableRipple
            />
          </div>
          <FormLabel disabled={is_disabled || optimizationInfo.exclusiveSprintYn !== 'Y'}>
            <CommonTooltip
              id="sa-optimization-advice-tooltip-addTopCPC"
              title={
                <>
                  <h1 className="flex justify-center items-center">
                    {t('optimization.AdvancedOptions.tooltip.addTopCpc.title')}
                  </h1>
                  <div className="common-style">
                    <p>{t('optimization.AdvancedOptions.tooltip.addTopCpc.contents.0')}</p>
                    <p className="!mt-2.5 !py-2.5 text-center align-middle leading-relaxed bg-[#F2F3F6] rounded-sm">
                      <ProBadge disabled={false} />&nbsp;
                      <LiteBadge disabled={false} /><br/>
                      {t('optimization.AdvancedOptions.tooltip.addTopCpc.contents.1')}
                    </p>
                  </div>
                </>
              }
              placement="right-start"
              arrow
            >
              <AdviceMarkIcon className="mr-0.5 mb-[-2px]" />
            </CommonTooltip>
            <TransText as="span" className="text-xs" i18nKey="optimization.AdvancedOptions.label.addTopCpcYn" />
            <ProBadge disabled={is_disabled || !isProAdvertiser && !isBasicAdvertiser} className="ml-1 mr-1" />
            <LiteBadge disabled={is_disabled || !isLiteAdvertiser && !isBasicAdvertiser}/>
          </FormLabel>
          <div>
            <Switch
              className="float-right"
              edge="end"
              color="primary"
              disabled={is_disabled || optimizationInfo.exclusiveSprintYn !== 'Y'}
              onChange={(event, checked) => {
                handleChangeOptimizationInfo('addTopCpcYn', checked ? 'Y' : 'N')
              }}
              checked={optimizationInfo?.addTopCpcYn === 'Y'}
            />
          </div>
          <FormLabel disabled={is_disabled || optimizationInfo.exclusiveTurboYn !== 'Y'}>
            <CommonTooltip
              id="sa-optimization-advice-tooltip-addTopCPC"
              title={
                <>
                  <h1 className="flex justify-center items-center">
                    {t('optimization.AdvancedOptions.tooltip.boostingRateLevel.title')}
                  </h1>
                  <div className="common-style">
                    <InnerHtml
                      tagName="p"
                      innerHTML={t('optimization.AdvancedOptions.tooltip.boostingRateLevel.contents.0')}
                    />
                    <p className="!mt-2.5 !py-2.5 text-center align-middle leading-relaxed bg-[#F2F3F6] rounded-sm">
                      <ProBadge disabled={false} />
                      &nbsp;
                      {t('optimization.AdvancedOptions.tooltip.boostingRateLevel.contents.1')}
                    </p>
                  </div>
                </>
              }
              placement="right-start"
              arrow
            >
              <AdviceMarkIcon className="mr-0.5 mb-[-2px]" />
            </CommonTooltip>
            <TransText as="span" className="text-xs" i18nKey="optimization.AdvancedOptions.label.boostingRateLevel" />
            <ProBadge disabled={is_disabled} className="ml-1" />
          </FormLabel>
          <div className="text-primary-text text-[10px]">
            <div className="slider-wrapper float-right">
              <Slider
                className="slider"
                valueLabelDisplay="off"
                value={boostingRateLevel || 0}
                min={0}
                max={10}
                step={1}
                onChange={(event, value) => {
                  setIsUserInteracting(true)
                  setBoostingRateLavel(value as number)
                }}
                onChangeCommitted={() => {
                  setTimeout(() => setIsUserInteracting(false), 200)
                }}
                disabled={is_disabled || optimizationInfo.exclusiveTurboYn !== 'Y'}
              />
            </div>
            <div
              className={`clear-right flex justify-between pl-8 ${
                is_disabled || optimizationInfo.exclusiveTurboYn !== 'Y' ? 'text-blck opacity-30' : ''
              }`}
            >
              <span>
                <b>{t('optimization.AdvancedOptions.label.basic')}</b>
                &nbsp;
                {t('optimization.AdvancedOptions.label.bid')}
              </span>
              <span>
                <b> {t('optimization.AdvancedOptions.label.aggressive')}</b>
                &nbsp;
                {t('optimization.AdvancedOptions.label.bid')}
              </span>
            </div>
          </div>
          {(optionType === OptionType.CATALOG || optionType === OptionType.SHOPPING) && (
            <>
              <FormLabel disabled={is_disabled || optimizationInfo.exclusiveClusteringYn !== 'Y'}>
                <CommonTooltip
                  id="sa-optimization-advice-tooltip-addTopCPC"
                  title={
                    <>
                      <h1 className="flex justify-center items-center">
                        {t('optimization.AdvancedOptions.tooltip.minImps.title')}
                      </h1>
                      <div className="common-style">
                        <p>{t('optimization.AdvancedOptions.tooltip.minImps.contents.0')}</p>
                        <p className="!mt-2.5 !py-2.5 text-center align-middle leading-relaxed bg-[#F2F3F6] rounded-sm">
                          <ProBadge disabled={false} />
                          &nbsp;
                          {t('optimization.AdvancedOptions.tooltip.minImps.contents.1')}
                        </p>
                      </div>
                    </>
                  }
                  placement="right-start"
                  arrow
                >
                  <AdviceMarkIcon className="mr-0.5 mb-[-2px]" />
                </CommonTooltip>
                <span className="text-xs">{t('optimization.AdvancedOptions.label.minImps')}</span>
                <ProBadge disabled={is_disabled} className="ml-1" />
              </FormLabel>
              <div className="text-primary-text text-[10px]">
                <div className="slider-wrapper red float-right">
                  <Slider
                    className="slider"
                    valueLabelDisplay="off"
                    value={minImps || 0}
                    min={0}
                    max={3}
                    step={1}
                    onChange={(event, value) => {
                      setIsUserInteracting(true)
                      setMinImps(value as number)
                    }}
                    onChangeCommitted={() => {
                      setTimeout(() => setIsUserInteracting(false), 200)
                    }}
                    disabled={is_disabled || optimizationInfo.exclusiveClusteringYn !== 'Y'}
                  />
                </div>
                <div
                  className={`clear-right flex justify-between pl-10 ${
                    is_disabled || optimizationInfo.exclusiveClusteringYn !== 'Y' ? 'text-blck opacity-30' : ''
                  }`}
                >
                  <span>
                    <b>{t('optimization.AdvancedOptions.label.tight')}</b>
                  </span>
                  <span>
                    <b> {t('optimization.AdvancedOptions.label.loose')}</b>
                  </span>
                </div>
              </div>
            </>
          )}
          <FormLabel disabled={is_disabled || optimizationInfo.exclusiveCpcReboot !== 'Y'}>
            <CommonTooltip
              id="sa-optimization-advice-tooltip-cpcReboot"
              title={
                <>
                  <h1 className="flex justify-center items-center">
                    {t('optimization.AdvancedOptions.tooltip.cpcReboot.title')}
                  </h1>
                  <div className="common-style">
                    <p>{t('optimization.AdvancedOptions.tooltip.cpcReboot.contents.0')}</p>
                    <p>{t('optimization.AdvancedOptions.tooltip.cpcReboot.contents.1')}</p>
                    <p className="!mt-2.5 !py-2.5 text-center align-middle leading-relaxed bg-[#F2F3F6] rounded-sm">
                      <ProBadge disabled={false} />
                      &nbsp;
                      {t('optimization.AdvancedOptions.tooltip.addTopCpc.contents.1')}
                    </p>
                  </div>
                </>
              }
              placement="right-start"
              arrow
            >
              <AdviceMarkIcon className="mr-0.5 mb-[-2px]" />
            </CommonTooltip>
            <TransText as="span" className="text-xs" i18nKey="optimization.AdvancedOptions.label.cpcReboot" />
            <ProBadge disabled={is_disabled} className="ml-1 mr-1" />
            <BetaBadge size="sm" />
          </FormLabel>
          <div>
            <Switch
              className="float-right"
              edge="end"
              color="primary"
              disabled={is_disabled || optimizationInfo.exclusiveCpcReboot !== 'Y'}
              onChange={(event, checked) => {
                const hasCpcValue = cpc?.kpiValue;
                const hasCpaValue = cpa?.kpiValue;

                if (checked && (hasCpcValue || hasCpaValue)) {
                  openToast(t('optimization.AdvancedOptions.validation.cpcReboot'))
                  return;
                }

                handleChangeOptimizationInfo('cpcReboot', checked);
              }}
              checked={!!optimizationInfo?.cpcReboot}
            />
          </div>
        </div>
      </div>
    </div>
  )
}

export default AdvancedOptions
